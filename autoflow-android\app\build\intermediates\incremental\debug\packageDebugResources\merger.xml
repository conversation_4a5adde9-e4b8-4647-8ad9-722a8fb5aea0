<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res"><file name="bounce_in" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\anim\bounce_in.xml" qualifiers="" type="anim"/><file name="fade_in" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\anim\fade_in.xml" qualifiers="" type="anim"/><file name="shake" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\anim\shake.xml" qualifiers="" type="anim"/><file name="slide_up" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\anim\slide_up.xml" qualifiers="" type="anim"/><file name="button_filled_mustard" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\button_filled_mustard.xml" qualifiers="" type="drawable"/><file name="button_filled_teal" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\button_filled_teal.xml" qualifiers="" type="drawable"/><file name="button_outline_mustard" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\button_outline_mustard.xml" qualifiers="" type="drawable"/><file name="button_outline_teal" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\button_outline_teal.xml" qualifiers="" type="drawable"/><file name="button_text_red" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\button_text_red.xml" qualifiers="" type="drawable"/><file name="button_text_secondary" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\button_text_secondary.xml" qualifiers="" type="drawable"/><file name="capture_button_background" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\capture_button_background.xml" qualifiers="" type="drawable"/><file name="card_background_mustard" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\card_background_mustard.xml" qualifiers="" type="drawable"/><file name="card_background_teal" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\card_background_teal.xml" qualifiers="" type="drawable"/><file name="card_background_white" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\card_background_white.xml" qualifiers="" type="drawable"/><file name="chip_background_mustard" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\chip_background_mustard.xml" qualifiers="" type="drawable"/><file name="chip_background_teal" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\chip_background_teal.xml" qualifiers="" type="drawable"/><file name="circle_button_background" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\circle_button_background.xml" qualifiers="" type="drawable"/><file name="circle_light_purple" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\circle_light_purple.xml" qualifiers="" type="drawable"/><file name="circle_purple" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\circle_purple.xml" qualifiers="" type="drawable"/><file name="dashboard_background" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\dashboard_background.xml" qualifiers="" type="drawable"/><file name="dialog_background" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\dialog_background.xml" qualifiers="" type="drawable"/><file name="dropdown_item_background" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\dropdown_item_background.xml" qualifiers="" type="drawable"/><file name="dropdown_parent_item_background" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\dropdown_parent_item_background.xml" qualifiers="" type="drawable"/><file name="floating_button_background" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\floating_button_background.xml" qualifiers="" type="drawable"/><file name="floating_share_button_background" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\floating_share_button_background.xml" qualifiers="" type="drawable"/><file name="header_background" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\header_background.xml" qualifiers="" type="drawable"/><file name="ic_add" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_arrow_back" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_arrow_back.xml" qualifiers="" type="drawable"/><file name="ic_autoflow_logo" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_autoflow_logo.xml" qualifiers="" type="drawable"/><file name="ic_camera" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_camera.xml" qualifiers="" type="drawable"/><file name="ic_car_placeholder" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_car_placeholder.xml" qualifiers="" type="drawable"/><file name="ic_categories" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_categories.xml" qualifiers="" type="drawable"/><file name="ic_category" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_category.xml" qualifiers="" type="drawable"/><file name="ic_check_circle" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_check_circle.xml" qualifiers="" type="drawable"/><file name="ic_clear" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_clear.xml" qualifiers="" type="drawable"/><file name="ic_close_small" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_close_small.xml" qualifiers="" type="drawable"/><file name="ic_delete" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_delete.xml" qualifiers="" type="drawable"/><file name="ic_edit" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_edit.xml" qualifiers="" type="drawable"/><file name="ic_email" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_email.xml" qualifiers="" type="drawable"/><file name="ic_error" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_error.xml" qualifiers="" type="drawable"/><file name="ic_filter" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_filter.xml" qualifiers="" type="drawable"/><file name="ic_fingerprint" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_fingerprint.xml" qualifiers="" type="drawable"/><file name="ic_history" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_history.xml" qualifiers="" type="drawable"/><file name="ic_home" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_home.xml" qualifiers="" type="drawable"/><file name="ic_image" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_image.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_launcher.xml" qualifiers="" type="drawable"/><file name="ic_logout" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_logout.xml" qualifiers="" type="drawable"/><file name="ic_menu" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_menu.xml" qualifiers="" type="drawable"/><file name="ic_more_vert" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_more_vert.xml" qualifiers="" type="drawable"/><file name="ic_part_placeholder" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_part_placeholder.xml" qualifiers="" type="drawable"/><file name="ic_person" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_person.xml" qualifiers="" type="drawable"/><file name="ic_search" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_search.xml" qualifiers="" type="drawable"/><file name="ic_search_empty" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_search_empty.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_share" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_share.xml" qualifiers="" type="drawable"/><file name="ic_sort" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_sort.xml" qualifiers="" type="drawable"/><file name="ic_trending" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_trending.xml" qualifiers="" type="drawable"/><file name="image_placeholder_background" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\image_placeholder_background.xml" qualifiers="" type="drawable"/><file name="input_background" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\input_background.xml" qualifiers="" type="drawable"/><file name="login_background" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\login_background.xml" qualifiers="" type="drawable"/><file name="logo_background" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\logo_background.xml" qualifiers="" type="drawable"/><file name="otp_digit_background" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\otp_digit_background.xml" qualifiers="" type="drawable"/><file name="otp_illustration" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\otp_illustration.xml" qualifiers="" type="drawable"/><file name="otp_illustration_simple" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\otp_illustration_simple.xml" qualifiers="" type="drawable"/><file name="search_input_background" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\search_input_background.xml" qualifiers="" type="drawable"/><file name="spinner_background" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\spinner_background.xml" qualifiers="" type="drawable"/><file name="stock_indicator_green" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\stock_indicator_green.xml" qualifiers="" type="drawable"/><file name="stock_indicator_red" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\stock_indicator_red.xml" qualifiers="" type="drawable"/><file name="stock_indicator_yellow" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\stock_indicator_yellow.xml" qualifiers="" type="drawable"/><file name="activity_add_part" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\activity_add_part.xml" qualifiers="" type="layout"/><file name="activity_camera_capture" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\activity_camera_capture.xml" qualifiers="" type="layout"/><file name="activity_category_selection" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\activity_category_selection.xml" qualifiers="" type="layout"/><file name="activity_dashboard" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\activity_dashboard.xml" qualifiers="" type="layout"/><file name="activity_image_crop" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\activity_image_crop.xml" qualifiers="" type="layout"/><file name="activity_login" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\activity_login.xml" qualifiers="" type="layout"/><file name="activity_otp" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\activity_otp.xml" qualifiers="" type="layout"/><file name="activity_part_detail" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\activity_part_detail.xml" qualifiers="" type="layout"/><file name="activity_part_update" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\activity_part_update.xml" qualifiers="" type="layout"/><file name="activity_search_results" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\activity_search_results.xml" qualifiers="" type="layout"/><file name="content_dashboard" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\content_dashboard.xml" qualifiers="" type="layout"/><file name="dialog_filters" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\dialog_filters.xml" qualifiers="" type="layout"/><file name="dialog_sort" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\dialog_sort.xml" qualifiers="" type="layout"/><file name="footer_backend" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\footer_backend.xml" qualifiers="" type="layout"/><file name="fragment_alternative_parts" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\fragment_alternative_parts.xml" qualifiers="" type="layout"/><file name="fragment_basic_info" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\fragment_basic_info.xml" qualifiers="" type="layout"/><file name="fragment_category" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\fragment_category.xml" qualifiers="" type="layout"/><file name="fragment_compatible_vehicles" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\fragment_compatible_vehicles.xml" qualifiers="" type="layout"/><file name="fragment_images" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\fragment_images.xml" qualifiers="" type="layout"/><file name="fragment_pricing_inventory" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\fragment_pricing_inventory.xml" qualifiers="" type="layout"/><file name="header_backend" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\header_backend.xml" qualifiers="" type="layout"/><file name="item_category" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\item_category.xml" qualifiers="" type="layout"/><file name="item_part" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\item_part.xml" qualifiers="" type="layout"/><file name="item_part_image" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\item_part_image.xml" qualifiers="" type="layout"/><file name="item_search_suggestion" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\item_search_suggestion.xml" qualifiers="" type="layout"/><file name="layout_search_with_suggestions" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout\layout_search_with_suggestions.xml" qualifiers="" type="layout"/><file name="activity_otp" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\layout-land\activity_otp.xml" qualifiers="land" type="layout"/><file name="part_detail_menu" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\menu\part_detail_menu.xml" qualifiers="" type="menu"/><file path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="brand_black">#FF1A1A1A</color><color name="brand_red">#FFE53E3E</color><color name="brand_teal">#FF38B2AC</color><color name="brand_mustard">#FFD69E2E</color><color name="brand_black_light">#FF2D2D2D</color><color name="brand_red_light">#FFFC8181</color><color name="brand_red_dark">#FFC53030</color><color name="brand_teal_light">#FF81E6D9</color><color name="brand_teal_dark">#FF2C7A7B</color><color name="brand_mustard_light">#FFF6E05E</color><color name="brand_mustard_dark">#FFB7791F</color><color name="brand_black_bg">#FF0F0F0F</color><color name="brand_red_bg">#FFFED7D7</color><color name="brand_teal_bg">#FFE6FFFA</color><color name="brand_mustard_bg">#FFFFFAF0</color><color name="autoflow_primary">#FF38B2AC</color><color name="autoflow_primary_dark">#FF2C7A7B</color><color name="autoflow_secondary">#FFD69E2E</color><color name="autoflow_secondary_dark">#FFB7791F</color><color name="autoflow_background">#FFF5F5F5</color><color name="autoflow_surface">#FFFFFFFF</color><color name="autoflow_card_background">#FFFFFFFF</color><color name="autoflow_text_primary">#FF1A1A1A</color><color name="autoflow_text_secondary">#FF4A5568</color><color name="autoflow_text_hint">#FFA0AEC0</color><color name="text_on_dark">#FFFFFFFF</color><color name="text_on_brand">#FFFFFFFF</color><color name="autoflow_success">#FF38B2AC</color><color name="autoflow_warning">#FFD69E2E</color><color name="autoflow_error">#FFE53E3E</color><color name="autoflow_info">#FF38B2AC</color><color name="autoflow_divider">#FFE0E0E0</color><color name="autoflow_border">#FFCCCCCC</color><color name="autoflow_shadow">#1F000000</color><color name="purple_500">#FF38B2AC</color><color name="purple_300">#FF81E6D9</color><color name="purple_200">#FFB2F5EA</color><color name="purple_100">#FFE6FFFA</color><color name="text_primary">#FF1A1A1A</color><color name="text_secondary">#FF4A5568</color><color name="accent_red">#FFE53E3E</color><color name="accent_mustard">#FFD69E2E</color><color name="accent_black">#FF1A1A1A</color><color name="stock_green">#4CAF50</color><color name="stock_yellow">#FF9800</color><color name="stock_red">#F44336</color><color name="background_light">#F5F5F5</color><color name="brand_primary">#FF38B2AC</color><color name="light_gray">#FFE0E0E0</color><color name="darker_gray">#FF666666</color><color name="dropdown_background">#FFFFFFFF</color><color name="dropdown_item_background">#FFF8F9FA</color><color name="dropdown_item_selected">#FFE6FFFA</color><color name="dropdown_border">#FFE0E0E0</color><color name="dropdown_text_primary">#FF1A1A1A</color><color name="dropdown_text_secondary">#FF4A5568</color><color name="dropdown_parent_category">#FF38B2AC</color><color name="dropdown_leaf_category">#FF1A1A1A</color><color name="dropdown_chevron">#FF38B2AC</color><color name="outline">#FFD1D5DB</color><color name="surface">#FFFFFFFF</color><color name="secondary">#FFD69E2E</color><color name="on_surface_variant">#FF6B7280</color><color name="primary">#FF38B2AC</color><color name="on_surface">#FF1A1A1A</color><color name="outline_variant">#FFE5E7EB</color><color name="on_secondary_container">#FF1A1A1A</color><color name="surface_variant">#FFF3F4F6</color><color name="primary_container">#FFE6FFFA</color><color name="on_primary_container">#FF1A1A1A</color><color name="secondary_container">#FFFFFAF0</color></file><file path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Autoflow</string><string name="loading">Loading...</string><string name="error_occurred">An error occurred</string><string name="try_again">Try Again</string><string name="cancel">Cancel</string><string name="ok">OK</string><string name="error_no_internet">No internet connection. Please check your network settings.</string><string name="error_connection_failed">Connection failed. Please try again.</string><string name="error_timeout">Request timed out. Please try again.</string><string name="error_invalid_credentials">Invalid email or password. Please try again.</string><string name="error_invalid_otp">Invalid OTP code. Please check and try again.</string><string name="error_otp_expired">OTP code has expired. Please request a new one.</string><string name="error_user_not_found">User not found. Please check your credentials.</string><string name="error_too_many_requests">Too many requests. Please wait and try again.</string><string name="error_server_error">Server error. Please try again later.</string><string name="error_unknown">An unexpected error occurred. Please try again.</string><string name="login_title">Login to continue</string><string name="email_hint">Email</string><string name="password_hint">Password</string><string name="login_button">LOGIN</string><string name="forgot_password">Forgot password?</string><string name="signing_in">Signing in...</string><string name="email_required">Email is required</string><string name="email_invalid">Please enter a valid email</string><string name="password_required">Password is required</string><string name="password_too_short">Password must be at least 6 characters</string><string name="verify_account">Verify Account</string><string name="enter_verification_code">Enter your Verification Code</string><string name="verification_code_sent">We sent a verification code\nto your email address</string><string name="verify_otp">Verify OTP</string><string name="didnt_receive_code">Didn\'t receive code? </string><string name="resend_again">Resend again</string><string name="verifying">Verifying...</string><string name="invalid_code">Invalid verification code</string><string name="verification_failed">Verification failed</string><string name="select_category_placeholder">Select a category</string></file><file path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\values\styles.xml" qualifiers=""><style name="AutoflowButton" parent="Widget.Material3.Button">
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">16dp</item>
        <item name="android:minHeight">48dp</item>
    </style><style name="AutoflowButton.Tonal" parent="Widget.Material3.Button.TonalButton">
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">16dp</item>
        <item name="android:minHeight">48dp</item>
    </style><style name="AutoflowButton.Outlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">16dp</item>
        <item name="android:minHeight">48dp</item>
    </style></file><file path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.AutoflowAndroid" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorPrimaryContainer">@color/primary_container</item>
        <item name="colorOnPrimaryContainer">@color/on_primary_container</item>
        
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="colorSecondaryContainer">@color/secondary_container</item>
        <item name="colorOnSecondaryContainer">@color/on_secondary_container</item>
        
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnSurface">@color/on_surface</item>
        <item name="colorSurfaceVariant">@color/surface_variant</item>
        <item name="colorOnSurfaceVariant">@color/on_surface_variant</item>
        
        <item name="colorOutline">@color/outline</item>
        <item name="colorOutlineVariant">@color/outline_variant</item>
        
        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightNavigationBar">true</item>
        
        
        <item name="colorSurfaceBright">@color/surface</item>
        <item name="colorSurfaceDim">@color/surface_variant</item>
        <item name="colorSurfaceContainer">@color/surface_variant</item>
        <item name="colorSurfaceContainerHigh">@color/surface</item>
        <item name="colorSurfaceContainerLow">@color/surface_variant</item>
        <item name="colorSurfaceContainerHighest">@color/surface</item>
    </style><style name="LoginButton" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/brand_teal</item>
        <item name="android:textColor">@color/text_on_brand</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">0.1</item>
        <item name="cornerRadius">28dp</item>
        <item name="elevation">8dp</item>
        <item name="rippleColor">@color/brand_teal_light</item>
    </style><style name="OTPDigitStyle">
        <item name="android:background">@drawable/otp_digit_background</item>
        <item name="android:gravity">center</item>
        <item name="android:inputType">number</item>
        <item name="android:maxLength">1</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:importantForAutofill">no</item>
        <item name="android:maxLines">1</item>
        <item name="android:singleLine">true</item>
        <item name="android:selectAllOnFocus">true</item>
    </style><style name="CategoryDropdownParent" parent="Widget.Material3.CardView">
        <item name="cardBackgroundColor">@color/surface_variant</item>
        <item name="cardElevation">@dimen/elevation_low</item>
        <item name="cardCornerRadius">@dimen/corner_radius_medium</item>
        <item name="rippleColor">@color/primary</item>
    </style><style name="CategoryDropdownTrigger" parent="Widget.Material3.CardView">
        <item name="cardBackgroundColor">@color/surface_variant</item>
        <item name="cardElevation">@dimen/elevation_medium</item>
        <item name="cardCornerRadius">@dimen/corner_radius_large</item>
        <item name="rippleColor">@color/primary</item>
    </style><style name="CategoryDropdownItem" parent="Widget.Material3.CardView">
        <item name="cardBackgroundColor">@color/surface</item>
        <item name="cardElevation">@dimen/elevation_low</item>
        <item name="cardCornerRadius">@dimen/corner_radius_medium</item>
        <item name="rippleColor">@color/primary</item>
    </style></file><file path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.AutoflowAndroid" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorOnPrimary">@android:color/white</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
    </style></file><file name="file_paths" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="ic_expand_more" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\ic_expand_more.xml" qualifiers="" type="drawable"/><file name="info_background" path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\drawable\info_background.xml" qualifiers="" type="drawable"/><file path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="spacing_xs">4dp</dimen><dimen name="spacing_small">8dp</dimen><dimen name="spacing_medium">12dp</dimen><dimen name="spacing_large">16dp</dimen><dimen name="spacing_xl">20dp</dimen><dimen name="spacing_xxl">24dp</dimen><dimen name="spacing_xxxl">32dp</dimen><dimen name="corner_radius_small">4dp</dimen><dimen name="corner_radius_medium">8dp</dimen><dimen name="corner_radius_large">12dp</dimen><dimen name="corner_radius_extra_large">20dp</dimen><dimen name="corner_radius_full">28dp</dimen><dimen name="elevation_none">0dp</dimen><dimen name="elevation_low">1dp</dimen><dimen name="elevation_medium">3dp</dimen><dimen name="elevation_high">6dp</dimen><dimen name="elevation_extra_high">12dp</dimen><dimen name="text_size_small">12sp</dimen><dimen name="text_size_medium">14sp</dimen><dimen name="text_size_large">16sp</dimen><dimen name="text_size_xl">20sp</dimen><dimen name="text_size_xxl">24sp</dimen><dimen name="text_size_headline">28sp</dimen><dimen name="dropdown_item_height">56dp</dimen><dimen name="dropdown_trigger_height">56dp</dimen><dimen name="search_input_height">48dp</dimen><dimen name="chip_height">32dp</dimen><dimen name="icon_size_small">16dp</dimen><dimen name="icon_size_medium">24dp</dimen><dimen name="icon_size_large">32dp</dimen></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Node\autoflow\autoflow-android\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>
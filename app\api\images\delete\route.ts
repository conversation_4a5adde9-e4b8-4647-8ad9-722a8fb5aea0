// app/api/images/delete/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { checkApiAuth } from '@/app/utils/apiAuth'
import { supabaseAdmin } from '@/app/libs/supabase/admin'

function extractStoragePath(imageUrl: string, bucket = 'car-part-images') {
  try {
    if (!imageUrl) return null
    if (!imageUrl.includes('supabase.co')) return imageUrl // assume already a storage path
    const u = new URL(imageUrl)
    // Common pattern: /storage/v1/object/public/<bucket>/<path>
    const marker = `/v1/object/public/${bucket}/`
    const idx = u.pathname.indexOf(marker)
    if (idx !== -1) {
      return u.pathname.substring(idx + marker.length)
    }
    // Fallback: split and find bucket segment
    const parts = u.pathname.split('/')
    const bIdx = parts.findIndex((p) => p === bucket)
    if (bIdx !== -1 && bIdx + 1 < parts.length) {
      return parts.slice(bIdx + 1).join('/')
    }
    return null
  } catch {
    return null
  }
}

export async function POST(request: NextRequest) {
  try {
    // Require auth (cookie or Bearer)
    const { authenticated, user, errorResponse } = await checkApiAuth(request)
    if (!authenticated || !user) return errorResponse!

    const body = await request.json()
    const imageUrl: string | undefined = body?.imageUrl
    const partId: number | undefined = body?.partId

    if (!imageUrl) {
      return NextResponse.json({ error: 'imageUrl is required' }, { status: 400 })
    }

    const bucket = 'car-part-images'
    const path = extractStoragePath(imageUrl, bucket)

    // 1) Delete from storage (best-effort)
    if (path) {
      const { error: storageErr } = await supabaseAdmin.storage.from(bucket).remove([path])
      if (storageErr) {
        // If remove by extracted path fails, try removing by full URL last segment
        const filename = imageUrl.split('/').pop()
        if (filename) {
          await supabaseAdmin.storage.from(bucket).remove([filename]).catch(() => {})
        }
      }
    }

    // 2) Delete DB record if it exists (best-effort)
    // If partId given, use both keys; else try by image_url only
    if (partId) {
      await supabaseAdmin.from('part_images').delete().eq('part_id', partId).eq('image_url', imageUrl)
    } else {
      await supabaseAdmin.from('part_images').delete().eq('image_url', imageUrl)
    }

    return NextResponse.json({ success: true })
  } catch (e: any) {
    return NextResponse.json({ error: e?.message || 'Failed to delete image' }, { status: 500 })
  }
}


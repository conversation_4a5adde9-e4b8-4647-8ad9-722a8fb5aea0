<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Material 3 button variants tuned to corporate colors -->
    <style name="AutoflowButton" parent="Widget.Material3.Button">
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">16dp</item>
        <item name="android:minHeight">48dp</item>
    </style>

    <style name="AutoflowButton.Tonal" parent="Widget.Material3.Button.TonalButton">
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">16dp</item>
        <item name="android:minHeight">48dp</item>
    </style>

    <style name="AutoflowButton.Outlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">16dp</item>
        <item name="android:minHeight">48dp</item>
    </style>
</resources>


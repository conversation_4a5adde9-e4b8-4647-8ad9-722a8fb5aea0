package com.autoflow.android.data.api

import android.content.Context
import com.autoflow.android.core.auth.AuthManager
import com.autoflow.android.domain.models.*
import java.io.File

/**
 * Simplified API client for Add Part flow Mock implementation for development - replace with actual
 * HTTP calls when ready
 */
class AddPartApiClient(private val context: Context, private val authManager: AuthManager) {

    val apiService: AddPartApiService = MockAddPartApiService()

    /**
     * Mock implementation of AddPartApiService for development Replace with actual HTTP
     * implementation when backend is ready
     */
    inner class MockAddPartApiService : AddPartApiService {

        override suspend fun getCategories(): CategoriesResponse {
            // Mock categories data
            val mockCategories =
                    listOf(
                            CategoryResponse(
                                    id = 1,
                                    label = "Engine Parts",
                                    parentCategoryId = null,
                                    children =
                                            listOf(
                                                    CategoryResponse(
                                                            id = 2,
                                                            label = "Air Filters",
                                                            parentCategoryId = 1,
                                                            children = emptyList()
                                                    ),
                                                    CategoryResponse(
                                                            id = 3,
                                                            label = "Oil Filters",
                                                            parentCategoryId = 1,
                                                            children = emptyList()
                                                    )
                                            )
                            ),
                            CategoryResponse(
                                    id = 4,
                                    label = "Brake Parts",
                                    parentCategoryId = null,
                                    children =
                                            listOf(
                                                    CategoryResponse(
                                                            id = 5,
                                                            label = "Brake Pads",
                                                            parentCategoryId = 4,
                                                            children = emptyList()
                                                    )
                                            )
                            )
                    )

            return CategoriesResponse(
                    categories = mockCategories,
                    success = true,
                    message = "Mock categories loaded"
            )
        }

        override suspend fun getCategoryAttributes(categoryId: Int): CategoryAttributesResponse {
            // Mock attributes based on category
            val mockAttributes =
                    when (categoryId) {
                        2 ->
                                listOf(
                                        com.autoflow.android.domain.models.CategoryAttribute(
                                                id = 1,
                                                attribute = "Filter Type",
                                                inputType = "select",
                                                isRequired = true,
                                                options =
                                                        listOf(
                                                                com.autoflow.android.domain.models
                                                                        .AttributeOption(
                                                                                id = 1,
                                                                                value = "Paper",
                                                                                attributeId = 1
                                                                        ),
                                                                com.autoflow.android.domain.models
                                                                        .AttributeOption(
                                                                                id = 2,
                                                                                value = "Foam",
                                                                                attributeId = 1
                                                                        ),
                                                                com.autoflow.android.domain.models
                                                                        .AttributeOption(
                                                                                id = 3,
                                                                                value = "Cotton",
                                                                                attributeId = 1
                                                                        )
                                                        )
                                        )
                                )
                        else -> emptyList()
                    }

            return CategoryAttributesResponse(
                    attributes = mockAttributes,
                    success = true,
                    message = "Mock attributes loaded"
            )
        }

        override suspend fun getBrands(): List<CarBrand> {
            return listOf(CarBrand(1, "VW"), CarBrand(2, "AUDI"))
        }

        override suspend fun getModels(brandId: Int): List<CarModel> {
            return when (brandId) {
                1 ->
                        listOf(
                                CarModel(1, 1, "Golf"),
                                CarModel(2, 1, "Passat"),
                                CarModel(3, 1, "Touareg")
                        )
                2 -> listOf(CarModel(4, 2, "A3"), CarModel(5, 2, "A4"), CarModel(6, 2, "Q7"))
                else -> emptyList()
            }
        }

        override suspend fun getGenerations(modelId: Int): List<CarGeneration> {
            return listOf(
                    CarGeneration(1, modelId, "Mk7 (2012-2020)"),
                    CarGeneration(2, modelId, "Mk8 (2020-Present)")
            )
        }

        override suspend fun getVariations(generationId: Int): List<CarVariation> {
            return listOf(
                    CarVariation(1, generationId, "Hatchback"),
                    CarVariation(2, generationId, "Estate")
            )
        }

        override suspend fun getTrims(variationId: Int): List<CarTrim> {
            return listOf(
                    CarTrim(1, variationId, "1.4 TSI"),
                    CarTrim(2, variationId, "2.0 TDI"),
                    CarTrim(3, variationId, "2.0 GTI")
            )
        }

        override suspend fun checkCompatibility(
                request: CheckCompatibilityRequest
        ): CheckCompatibilityResponse {
            // Mock compatibility response
            return CheckCompatibilityResponse(
                    success = true,
                    partName = "Mock Part for ${request.partNumber}",
                    compatiblePartNumbers =
                            listOf("${request.partNumber}A", "${request.partNumber}B"),
                    engineCompatibility =
                            listOf(
                                    EngineCompatibility(
                                            engineCode = "CAXA",
                                            engineCapacity = "1.4L",
                                            fuelType = "Petrol",
                                            engineType = "TSI"
                                    )
                            ),
                    vehicleCompatibility =
                            listOf(
                                    VehicleCompatibility(
                                            brand = "VW",
                                            model = "Golf",
                                            generation = "Mk7",
                                            variation = "Hatchback"
                                    )
                            ),
                    partnumberGroup = 1,
                    message = "Mock compatibility check completed"
            )
        }

        override suspend fun generateTitle(request: GenerateTitleRequest): GenerateTitleResponse {
            val mockTitle =
                    "VW AUDI ${request.partNumber ?: "Unknown"} ${request.vehicleInfo ?: ""}"
            return GenerateTitleResponse(
                    title = mockTitle.trim(),
                    success = true,
                    message = "Mock title generated"
            )
        }

        override suspend fun generateDescription(
                request: GenerateDescriptionRequest
        ): GenerateDescriptionResponse {
            val mockDescription =
                    "${request.condition} part for ${request.title}. Compatible with various VW and Audi models."
            return GenerateDescriptionResponse(
                    description = mockDescription,
                    success = true,
                    message = "Mock description generated"
            )
        }

        override suspend fun uploadImage(imageFile: File): ImageUploadResponse {
            // Mock image upload
            val mockUrl = "https://mock-storage.com/images/${imageFile.name}"
            return ImageUploadResponse(
                    imageUrl = mockUrl,
                    success = true,
                    message = "Mock image uploaded"
            )
        }

        override suspend fun createPart(request: CreatePartRequest): CreatePartResponse {
            // Mock part creation
            val mockPartId = (1000..9999).random()
            return CreatePartResponse(
                    partId = mockPartId,
                    title = request.title,
                    success = true,
                    message = "Mock part created successfully"
            )
        }

        override suspend fun getStorageAreas(): List<StorageArea> {
            return listOf(
                    StorageArea(1, "Warehouse A"),
                    StorageArea(2, "Warehouse B"),
                    StorageArea(3, "Shop Floor")
            )
        }

        override suspend fun getStorageUnits(areaId: Int?): List<StorageUnit> {
            return when (areaId) {
                1 ->
                        listOf(
                                StorageUnit(1, 1, "Section A1", "shelf"),
                                StorageUnit(2, 1, "Section A2", "crate")
                        )
                2 ->
                        listOf(
                                StorageUnit(3, 2, "Section B1", "container"),
                                StorageUnit(4, 2, "Section B2", "open_area")
                        )
                else ->
                        listOf(
                                StorageUnit(1, 1, "Section A1", "shelf"),
                                StorageUnit(2, 1, "Section A2", "crate"),
                                StorageUnit(3, 2, "Section B1", "container"),
                                StorageUnit(4, 2, "Section B2", "open_area")
                        )
            }
        }

        override suspend fun savePartLocation(
                partId: Int,
                request: SaveLocationRequest
        ): SaveLocationResponse {
            // Mock location save
            return SaveLocationResponse(
                    success = true,
                    message = "Mock location saved for part $partId"
            )
        }
    }
}

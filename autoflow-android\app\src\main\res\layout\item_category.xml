<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/categoryCard"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:gravity="center">

        <!-- Category Icon (placeholder) -->
        <ImageView
            android:id="@+id/categoryIcon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginBottom="12dp"
            android:src="@drawable/ic_category"
            android:contentDescription="Category icon"
            android:tint="@color/brand_primary" />

        <!-- Category Name -->
        <TextView
            android:id="@+id/categoryName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Engine Parts"
            android:textSize="16sp"
            android:textStyle="bold"
            android:gravity="center"
            android:textColor="@color/brand_black"
            android:layout_marginBottom="4dp"
            tools:text="Engine Parts" />

        <!-- Category Description -->
        <TextView
            android:id="@+id/categoryDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Engine components and accessories"
            android:textSize="12sp"
            android:gravity="center"
            android:textColor="@color/darker_gray"
            android:maxLines="2"
            android:ellipsize="end"
            tools:text="Engine components and accessories" />

    </LinearLayout>

</androidx.cardview.widget.CardView>

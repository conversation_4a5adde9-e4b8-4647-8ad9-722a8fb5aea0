<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Focused state -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/brand_teal_bg" />
            <stroke android:width="2dp" android:color="@color/brand_teal" />
            <corners android:radius="12dp" />
        </shape>
    </item>

    <!-- Filled state (when text is present) -->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/brand_teal" />
            <corners android:radius="12dp" />
        </shape>
    </item>

    <!-- Default state -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/white" />
            <stroke android:width="1dp" android:color="@color/brand_teal_light" />
            <corners android:radius="12dp" />
        </shape>
    </item>
</selector>

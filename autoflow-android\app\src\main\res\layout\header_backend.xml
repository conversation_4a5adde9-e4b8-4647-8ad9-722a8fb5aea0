<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="64dp"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:background="@drawable/header_background"
    android:elevation="4dp"
    android:paddingHorizontal="16dp">

    <!-- Hamburger <PERSON><PERSON> -->
    <ImageButton
        android:id="@+id/hamburgerMenuButton"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:src="@drawable/ic_menu"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:contentDescription="Menu"
        android:tint="@color/text_on_brand"
        android:padding="12dp" />

    <!-- <PERSON><PERSON> Container (Centered) -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/logoImage"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_autoflow_logo"
            android:layout_marginEnd="8dp" />

        <TextView
            android:id="@+id/logoText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Autoflow"
            android:textColor="@color/text_on_brand"
            android:textSize="20sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium" />

    </LinearLayout>

    <!-- Categories Menu Button -->
    <ImageButton
        android:id="@+id/categoriesMenuButton"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:src="@drawable/ic_categories"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:contentDescription="Categories"
        android:tint="@color/text_on_brand"
        android:padding="12dp" />

</LinearLayout>

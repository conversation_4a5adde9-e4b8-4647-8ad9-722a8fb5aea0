<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light">

    <!-- Header with AI Generate Button -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Compatible Vehicles"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/generateVehiclesButton"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:background="@drawable/button_filled_teal"
            android:text="🤖 AI Generate"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:paddingHorizontal="16dp" />

    </LinearLayout>

    <!-- Search and Add Vehicle -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp"
        android:paddingBottom="16dp">

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            app:boxStrokeColor="@color/brand_teal"
            app:hintTextColor="@color/brand_teal">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/vehicleSearchInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Search vehicles..."
                android:inputType="text"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <Button
            android:id="@+id/addVehicleButton"
            android:layout_width="wrap_content"
            android:layout_height="56dp"
            android:background="@drawable/button_outline_teal"
            android:text="Add"
            android:textColor="@color/brand_teal"
            android:textSize="14sp"
            android:paddingHorizontal="16dp" />

    </LinearLayout>

    <!-- Compatible Vehicles List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/compatibleVehiclesRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:paddingHorizontal="16dp"
        android:clipToPadding="false" />

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/emptyVehiclesLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="32dp"
        android:visibility="gone">

        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:src="@drawable/ic_car_placeholder"
            android:tint="@color/text_secondary"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="No compatible vehicles added"
            android:textColor="@color/text_secondary"
            android:textSize="16sp"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Use AI Generate or search to add vehicles"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            android:gravity="center" />

    </LinearLayout>

    <!-- Loading Layout -->
    <LinearLayout
        android:id="@+id/vehiclesLoadingLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:padding="16dp"
        android:visibility="gone">

        <ProgressBar
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="12dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Generating compatible vehicles..."
            android:textColor="@color/text_secondary"
            android:textSize="16sp" />

    </LinearLayout>

</LinearLayout>

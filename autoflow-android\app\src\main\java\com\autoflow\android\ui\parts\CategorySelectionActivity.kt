package com.autoflow.android.ui.parts

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.autoflow.android.R
import com.autoflow.android.data.api.PartsApiService
import com.autoflow.android.data.repository.AuthRepository
import kotlinx.coroutines.launch

class CategorySelectionActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "CategorySelectionActivity"
    }

    private lateinit var authRepository: AuthRepository
    private lateinit var partsApiService: PartsApiService

    // UI Elements
    private lateinit var imageView: ImageView
    private lateinit var categorySpinner: Spinner
    private lateinit var selectedCategoryInfo: TextView
    private lateinit var progressBar: ProgressBar
    private lateinit var statusText: TextView
    private lateinit var continueButton: Button
    private lateinit var backButton: Button

    private var croppedImageUri: Uri? = null
    private var selectedCategory: Category? = null
    private val categories = mutableListOf<Category>()

    data class Category(
            val id: Int,
            val name: String,
            val description: String? = null,
            val requiresPartNumber: Boolean = false,
            val hasAttributes: Boolean = false
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_category_selection)

        authRepository = AuthRepository(this)
        partsApiService = PartsApiService(this)

        initializeViews()
        setupClickListeners()
        loadCroppedImage()

        // Add immediate test to ensure spinner works
        testSpinnerWithHardcodedData()

        loadCategories()
    }

    private fun testSpinnerWithHardcodedData() {
        Log.d(TAG, "Testing spinner with hardcoded data")

        // Create test data
        val testCategories =
                listOf("Select a category", "Engine Parts", "Body Parts", "Electrical Parts")

        // Create and set adapter directly
        val adapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, testCategories)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)

        try {
            categorySpinner.adapter = adapter
            Log.d(TAG, "Test adapter set successfully with ${testCategories.size} items")
            updateStatus("Test: Spinner should now show categories")
        } catch (e: Exception) {
            Log.e(TAG, "Error setting test adapter", e)
            updateStatus("Error: Could not set spinner adapter")
        }
    }

    private fun initializeViews() {
        try {
            imageView = findViewById(R.id.partImageView)
            categorySpinner = findViewById(R.id.categorySpinner)
            selectedCategoryInfo = findViewById(R.id.selectedCategoryInfo)
            progressBar = findViewById(R.id.progressBar)
            statusText = findViewById(R.id.statusText)
            continueButton = findViewById(R.id.continueButton)
            backButton = findViewById(R.id.backButton)

            // Verify views are not null
            Log.d(TAG, "Views initialized:")
            Log.d(
                    TAG,
                    "  categorySpinner: ${if (::categorySpinner.isInitialized && categorySpinner != null) "OK" else "NULL"}"
            )
            Log.d(
                    TAG,
                    "  statusText: ${if (::statusText.isInitialized && statusText != null) "OK" else "NULL"}"
            )
            Log.d(
                    TAG,
                    "  continueButton: ${if (::continueButton.isInitialized && continueButton != null) "OK" else "NULL"}"
            )

            // Setup category spinner
            setupCategorySpinner()

            continueButton.isEnabled = false
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing views", e)
            updateStatus("Error: Could not initialize UI components")
        }
    }

    private fun setupCategorySpinner() {
        categorySpinner.onItemSelectedListener =
                object : AdapterView.OnItemSelectedListener {
                    override fun onItemSelected(
                            parent: AdapterView<*>?,
                            view: View?,
                            position: Int,
                            id: Long
                    ) {
                        if (position > 0 && categories.isNotEmpty()
                        ) { // Skip the first "Select category" item
                            selectedCategory = categories[position - 1] // Adjust for placeholder
                            continueButton.isEnabled = true

                            val category = selectedCategory!!
                            val infoText =
                                    "Selected: ${category.name}\n" +
                                            if (category.requiresPartNumber) "Part number required"
                                            else "Part number optional"
                            selectedCategoryInfo.text = infoText
                            selectedCategoryInfo.visibility = View.VISIBLE

                            updateStatus("Selected: ${category.name}")
                        } else {
                            selectedCategory = null
                            continueButton.isEnabled = false
                            selectedCategoryInfo.visibility = View.GONE
                            updateStatus("Select a category for your part")
                        }
                    }

                    override fun onNothingSelected(parent: AdapterView<*>?) {
                        selectedCategory = null
                        continueButton.isEnabled = false
                        selectedCategoryInfo.visibility = View.GONE
                    }
                }
    }

    private fun setupClickListeners() {
        continueButton.setOnClickListener { proceedToNextStep() }
        backButton.setOnClickListener { finish() }
    }

    private fun loadCroppedImage() {
        val imageUriString = intent.getStringExtra("cropped_image_uri")
        if (imageUriString != null) {
            croppedImageUri = Uri.parse(imageUriString)
            // Load image without Glide to avoid dependency issues
            imageView.setImageURI(croppedImageUri)
        } else {
            Toast.makeText(this, "Error loading image", Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    private fun loadCategories() {
        Log.d(TAG, "loadCategories() called")

        // First, let's try with default categories to ensure the UI works
        categories.clear()
        categories.addAll(getDefaultCategories())
        Log.d(TAG, "Added ${categories.size} default categories")

        // Setup spinner immediately with default categories
        setupSpinnerAdapter()
        updateStatus("Select a category for your part (using default categories)")

        // Then try to load from API in background
        lifecycleScope.launch {
            try {
                updateStatus("Loading categories from database...")
                showProgress(true)
                Log.d(TAG, "Starting API call to load categories...")

                // Load categories from database via API
                val result = partsApiService.fetchCategories()
                Log.d(TAG, "API result: ${result.isSuccess}")

                if (result.isSuccess) {
                    val apiCategories = result.getOrNull() ?: emptyList()
                    Log.d(TAG, "Received ${apiCategories.size} categories from API")

                    if (apiCategories.isNotEmpty()) {
                        categories.clear()

                        // Convert API categories to local Category format
                        categories.addAll(
                                apiCategories.map { apiCategory ->
                                    Log.d(TAG, "Processing category: ${apiCategory.label}")
                                    Category(
                                            id = apiCategory.id,
                                            name = apiCategory.label,
                                            description =
                                                    "Category for ${apiCategory.label.lowercase()} parts",
                                            requiresPartNumber = apiCategory.requirePartNumber,
                                            hasAttributes =
                                                    true // Assume all categories have attributes
                                    )
                                }
                        )

                        Log.d(TAG, "Updating spinner with ${categories.size} API categories")
                        setupSpinnerAdapter()
                        updateStatus("Select a category for your part")
                    }
                } else {
                    Log.w(
                            TAG,
                            "API failed, keeping default categories: ${result.exceptionOrNull()}"
                    )
                    updateStatus("Select a category for your part (offline mode)")
                }

                showProgress(false)
            } catch (e: Exception) {
                Log.e(TAG, "Error loading categories from API", e)
                updateStatus("Select a category for your part (offline mode)")
                showProgress(false)

                Toast.makeText(
                                this@CategorySelectionActivity,
                                "Using offline categories",
                                Toast.LENGTH_SHORT
                        )
                        .show()
            }
        }
    }

    private fun setupSpinnerAdapter() {
        Log.d(TAG, "Setting up spinner adapter with ${categories.size} categories...")

        try {
            // Create category names list with placeholder
            val categoryNames = mutableListOf("Select a category")

            if (categories.isNotEmpty()) {
                categoryNames.addAll(categories.map { it.name })
                Log.d(TAG, "Added category names: ${categories.map { it.name }}")
            } else {
                Log.w(TAG, "No categories available, using placeholder only")
            }

            Log.d(TAG, "Final category names for spinner: $categoryNames")

            // Create and set adapter
            val adapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, categoryNames)
            adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)

            runOnUiThread {
                try {
                    categorySpinner.adapter = adapter
                    Log.d(TAG, "Spinner adapter set successfully with ${categoryNames.size} items")
                } catch (e: Exception) {
                    Log.e(TAG, "Error setting spinner adapter on UI thread", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in setupSpinnerAdapter", e)
        }
    }

    private fun getDefaultCategories(): List<Category> {
        return listOf(
                Category(1, "Engine Parts", "Engine components and accessories", true, true),
                Category(2, "Body Parts", "Exterior and interior body components", false, true),
                Category(3, "Electrical Parts", "Electrical components and wiring", true, true),
                Category(4, "Suspension Parts", "Suspension and steering components", true, true),
                Category(5, "Brake Parts", "Brake system components", true, true),
                Category(6, "Transmission Parts", "Transmission and drivetrain", true, true),
                Category(7, "Interior Parts", "Interior components and accessories", false, false),
                Category(8, "Exhaust Parts", "Exhaust system components", false, true)
        )
    }

    private fun proceedToNextStep() {
        selectedCategory?.let { category ->
            // Navigate to category-specific form
            val intent = Intent(this, AddPartActivity::class.java)
            intent.putExtra("cropped_image_uri", croppedImageUri.toString())
            intent.putExtra("category_id", category.id)
            intent.putExtra("category_name", category.name)
            intent.putExtra("requires_part_number", category.requiresPartNumber)
            intent.putExtra("has_attributes", category.hasAttributes)
            startActivity(intent)
            finish()
        }
    }

    private fun updateStatus(message: String) {
        runOnUiThread {
            statusText.text = message
            statusText.visibility = View.VISIBLE
        }
    }

    private fun showProgress(show: Boolean) {
        runOnUiThread { progressBar.visibility = if (show) View.VISIBLE else View.GONE }
    }
}

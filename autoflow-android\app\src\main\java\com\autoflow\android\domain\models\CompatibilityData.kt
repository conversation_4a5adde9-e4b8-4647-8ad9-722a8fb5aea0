package com.autoflow.android.domain.models

/**
 * Compatibility data from AI analysis
 * Contains all compatibility information for a part number
 */
data class CompatibilityData(
    val partName: String? = null,
    val partNumber: String,
    val compatiblePartNumbers: List<String> = emptyList(),
    val engineCompatibility: List<EngineCompatibility> = emptyList(),
    val vehicleCompatibility: List<VehicleCompatibility> = emptyList(),
    val partnumberGroup: Int? = null,
    val confidence: Double = 0.0,
    val analysisTimestamp: Long = System.currentTimeMillis()
)

/**
 * Compatibility check state for UI
 */
data class CompatibilityState(
    val isChecking: Boolean = false,
    val isComplete: Boolean = false,
    val error: String? = null,
    val messages: List<String> = emptyList(),
    val progress: Float = 0f
)

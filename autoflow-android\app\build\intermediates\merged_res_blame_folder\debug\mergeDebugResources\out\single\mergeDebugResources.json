[{"merged": "com.autoflow.android.app-debug-47:/drawable_info_background.xml.flat", "source": "com.autoflow.android.app-main-49:/drawable/info_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_footer_backend.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\footer_backend.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_stock_indicator_green.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\stock_indicator_green.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_clear.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_clear.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_input_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\input_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_button_filled_teal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\button_filled_teal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_history.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_stock_indicator_red.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\stock_indicator_red.xml"}, {"merged": "com.autoflow.android.app-debug-47:/drawable_ic_clear.xml.flat", "source": "com.autoflow.android.app-main-49:/drawable/ic_clear.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_button_outline_teal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\button_outline_teal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_spinner_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\spinner_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_chip_background_mustard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\chip_background_mustard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_card_background_mustard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\card_background_mustard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_button_filled_mustard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\button_filled_mustard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_delete.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_delete.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_dropdown_parent_item_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\dropdown_parent_item_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_floating_share_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\floating_share_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_search_empty.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_search_empty.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_dashboard_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\dashboard_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_email.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_email.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_filter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_activity_dashboard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\activity_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_dialog_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\dialog_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_circle_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\circle_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_logout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_logout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_header_backend.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\header_backend.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_part_placeholder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_part_placeholder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_activity_part_detail.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\activity_part_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_arrow_back.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_arrow_back.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_fragment_alternative_parts.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\fragment_alternative_parts.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_activity_add_part.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\activity_add_part.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_otp_digit_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\otp_digit_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_item_search_suggestion.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\item_search_suggestion.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_add.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_add.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_circle_purple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\circle_purple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_dropdown_item_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\dropdown_item_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\menu_part_detail_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\menu\\part_detail_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_trending.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_trending.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_car_placeholder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_car_placeholder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_activity_otp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\activity_otp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_item_part_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\item_part_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_sort.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_sort.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\xml_file_paths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\xml\\file_paths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_more_vert.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_more_vert.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\anim_slide_up.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\anim\\slide_up.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_person.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_person.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_activity_part_update.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\activity_part_update.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_login_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\login_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout-land_activity_otp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout-land\\activity_otp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_fragment_compatible_vehicles.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\fragment_compatible_vehicles.xml"}, {"merged": "com.autoflow.android.app-debug-47:/drawable_ic_expand_more.xml.flat", "source": "com.autoflow.android.app-main-49:/drawable/ic_expand_more.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_dialog_filters.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\dialog_filters.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_activity_search_results.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\activity_search_results.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_category.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_close_small.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_close_small.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_button_text_secondary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\button_text_secondary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_search_input_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\search_input_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_item_part.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\item_part.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_button_text_red.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\button_text_red.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_otp_illustration_simple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\otp_illustration_simple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_activity_image_crop.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\activity_image_crop.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_settings.xml"}, {"merged": "com.autoflow.android.app-debug-47:/layout_fragment_category.xml.flat", "source": "com.autoflow.android.app-main-49:/layout/fragment_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_activity_camera_capture.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\activity_camera_capture.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\anim_bounce_in.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\anim\\bounce_in.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_stock_indicator_yellow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\stock_indicator_yellow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_otp_illustration.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\otp_illustration.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_item_category.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\item_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\anim_fade_in.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\anim\\fade_in.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_fragment_images.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\fragment_images.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_fragment_pricing_inventory.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\fragment_pricing_inventory.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_dialog_sort.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\dialog_sort.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_error.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_error.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_capture_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\capture_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_header_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\header_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_floating_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\floating_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_categories.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_categories.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_image_placeholder_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\image_placeholder_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_fragment_category.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\fragment_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_activity_category_selection.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\activity_category_selection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_activity_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_logo_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\logo_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\anim_shake.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\anim\\shake.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_camera.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_camera.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_share.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_share.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_card_background_teal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\card_background_teal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_fingerprint.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_fingerprint.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_fragment_basic_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\fragment_basic_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_autoflow_logo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_autoflow_logo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_edit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_edit.xml"}, {"merged": "com.autoflow.android.app-debug-47:/drawable_ic_search.xml.flat", "source": "com.autoflow.android.app-main-49:/drawable/ic_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_ic_check_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\ic_check_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_content_dashboard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\content_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_circle_light_purple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\circle_light_purple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_chip_background_teal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\chip_background_teal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_button_outline_mustard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\button_outline_mustard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\layout_layout_search_with_suggestions.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\layout\\layout_search_with_suggestions.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-debug-47:\\drawable_card_background_white.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.autoflow.android.app-main-49:\\drawable\\card_background_white.xml"}]
<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

<ScrollView
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="@drawable/dashboard_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Product Search Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="24dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Product Search"
                android:textColor="@color/brand_black"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="12dp" />

            <!-- Search Input Container -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:orientation="horizontal"
                android:background="@drawable/search_input_background"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_search"
                    android:layout_marginEnd="12dp" />

                <EditText
                    android:id="@+id/productSearchInput"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@android:color/transparent"
                    android:hint="Search for products, parts, or SKUs..."
                    android:textColorHint="@color/text_secondary"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    android:inputType="text"
                    android:imeOptions="actionSearch"
                    android:maxLines="1"
                    android:singleLine="true" />

                <ImageButton
                    android:id="@+id/clearSearchButton"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_clear"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="Clear search"
                    android:visibility="gone"
                    android:padding="4dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Welcome Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="32dp">

            <!-- App Logo/Icon -->
            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_autoflow_logo"
                android:layout_marginEnd="16dp"
                android:background="@drawable/logo_background"
                android:padding="8dp" />

            <!-- Welcome Text -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/welcomeTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Welcome to Autoflow"
                    android:textColor="@color/brand_black"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif-medium" />

                <TextView
                    android:id="@+id/userEmail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="<EMAIL>"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Status Card -->
        <LinearLayout
            android:id="@+id/authSuccessCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:background="@drawable/card_background_teal"
            android:elevation="4dp"
            android:orientation="horizontal"
            android:padding="20dp"
            android:gravity="center_vertical"
            android:visibility="gone">

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_check_circle"
                android:layout_marginEnd="16dp"
                android:tint="@color/brand_teal" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Authentication Successful"
                    android:textColor="@color/brand_teal_dark"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="You are now logged in to Autoflow"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Biometric Setup Card -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:background="@drawable/card_background_mustard"
            android:elevation="4dp"
            android:orientation="vertical"
            android:padding="20dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/ic_fingerprint"
                    android:layout_marginEnd="16dp"
                    android:tint="@color/brand_mustard_dark" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Biometric Authentication"
                        android:textColor="@color/brand_mustard_dark"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Enable fingerprint or face unlock for faster login"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </LinearLayout>

            <!-- Biometric Button -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="end">

                <Button
                    android:id="@+id/setupBiometricButton"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:text="Setup Biometric"
                    android:textColor="@color/text_on_brand"
                    android:background="@drawable/button_filled_mustard"
                    android:paddingHorizontal="16dp"
                    android:textSize="14sp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Quick Actions -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Quick Actions"
            android:textColor="@color/brand_black"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <!-- Action Buttons Grid -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:weightSum="2">

            <!-- Settings Button -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:background="@drawable/card_background_white"
                android:elevation="2dp"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="16dp">

                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/ic_settings"
                    android:tint="@color/brand_black"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Settings"
                    android:textColor="@color/brand_black"
                    android:textSize="14sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <!-- Profile Button -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:background="@drawable/card_background_white"
                android:elevation="2dp"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="16dp">

                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/ic_person"
                    android:tint="@color/brand_black"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Profile"
                    android:textColor="@color/brand_black"
                    android:textSize="14sp"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</ScrollView>

<!-- Floating Action Button -->
<com.google.android.material.floatingactionbutton.FloatingActionButton
    android:id="@+id/fabAddPart"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom|end"
    android:layout_margin="16dp"
    android:src="@drawable/ic_add"
    android:contentDescription="Add new part"
    app:backgroundTint="@color/brand_primary"
    app:tint="@android:color/white"
    app:elevation="8dp"
    app:pressedTranslationZ="12dp" />

</FrameLayout>

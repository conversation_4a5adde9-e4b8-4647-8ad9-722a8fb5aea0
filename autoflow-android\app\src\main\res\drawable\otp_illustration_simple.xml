<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="200dp"
    android:height="160dp"
    android:viewportWidth="200"
    android:viewportHeight="160">

    <!-- Simple shield icon for verification -->
    <path
        android:fillColor="@color/brand_teal"
        android:pathData="M100,20 L120,30 L120,80 Q120,100 100,120 Q80,100 80,80 L80,30 Z" />

    <!-- Inner shield -->
    <path
        android:fillColor="@color/brand_teal_light"
        android:pathData="M100,30 L115,37 L115,75 Q115,90 100,105 Q85,90 85,75 L85,37 Z" />

    <!-- Checkmark -->
    <path
        android:fillColor="@android:color/white"
        android:strokeColor="@android:color/white"
        android:strokeWidth="3"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:pathData="M90,70 L97,77 L110,60" />

    <!-- Email envelope -->
    <path
        android:fillColor="@color/brand_mustard_light"
        android:pathData="M60,130 L140,130 L140,150 L60,150 Z" />

    <!-- Envelope flap -->
    <path
        android:fillColor="@color/brand_mustard"
        android:pathData="M60,130 L100,145 L140,130 L100,135 Z" />

</vector>

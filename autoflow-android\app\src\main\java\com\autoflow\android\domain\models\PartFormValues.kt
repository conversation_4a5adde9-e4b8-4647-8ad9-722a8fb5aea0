package com.autoflow.android.domain.models

/** Mirrors web PartFormValues - complete data model for Add Part flow */
data class PartFormValues(
        // Basic part info
        val partNumber: String = "",
        val title: String = "",
        val description: String = "",

        // Category and attributes
        val categoryId: String = "",
        val selectedCategory: String = "",
        val requirePartNumber: Boolean = false,
        val attributes: Map<String, Any> = emptyMap(),
        val categoryAttributes: List<CategoryAttributeValue> = emptyList(),

        // Images - exactly like web
        val images: List<PartImage> = emptyList(),
        val imageUrl: String = "",
        val imageType: String = "",

        // Vehicle selection
        val brandId: String = "",
        val modelId: String = "",
        val modelName: String = "",
        val generationId: String = "",
        val generationName: String = "",
        val generationYears: String = "",
        val variationId: String = "",
        val variationName: String = "",
        val trimId: String = "",
        val trimName: String = "",

        // Condition and pricing - dual condition support like web
        val condition: PartCondition = PartCondition.NEW,

        // New condition pricing
        val newStock: Int? = null,
        val newPrice: Double? = null,
        val newDiscountPrice: Double? = null,

        // Used condition pricing
        val usedStock: Int? = null,
        val usedPrice: Double? = null,
        val usedDiscountPrice: Double? = null,

        // Legacy single condition fields (for backward compatibility)
        val stock: Int? = null,
        val price: Double? = null,
        val discountPrice: Double? = null,

        // Compatibility data
        val compatibilityData: CompatibilityData? = null,
        val additionalEngineCodes: List<String> = emptyList(),

        // UI state
        val isCheckingPartNumber: Boolean = false,
        val showVehicleSelection: Boolean = false,

        // User info
        val userId: String = "",

        // Flow control
        val currentStep: AddPartStep = AddPartStep.IMAGES,
        val isSubmitting: Boolean = false
)

data class PartImage(
        val url: String,
        val isMain: Boolean,
        val isUploading: Boolean = false,
        val uploadProgress: Float = 0f
)

data class CategoryAttributeValue(
        val id: Int,
        val attributeId: Int,
        val value: String = "",
        val selectionValue: String = ""
)

enum class PartCondition {
    NEW,
    USED,
    BOTH
}

enum class AddPartStep {
    IMAGES,
    CATEGORY,
    PART_NUMBER, // For PN-required categories
    VEHICLE_SELECTION, // For non-PN categories
    ATTRIBUTES,
    CONDITION_PRICING,
    SUBMIT,
    STORAGE_LOCATION
}

/** Compatibility data from AI analysis - mirrors web CompatibilityData */
data class CompatibilityDataLegacy(
        val partName: String? = null,
        val compatiblePartNumbers: List<String> = emptyList(),
        val isEnginePart: Boolean = false,
        val engineCompatibility: List<EngineCompatibility> = emptyList(),
        val vehicleCompatibility: List<VehicleCompatibility> = emptyList(),
        val partnumberGroup: Int? = null
)

data class EngineCompatibilityLegacy(
        val engineCode: String,
        val engineCapacity: String,
        val fuelType: String,
        val engineType: String
)

data class VehicleCompatibilityLegacy(
        val brand: String = "",
        val model: String,
        val generation: String,
        val variation: String = "",
        val trims: List<String> = emptyList()
)

/** Storage location form data - mirrors web StorageLocationFormData */
data class StorageLocationFormDataLegacy(
        val areaId: Int? = null,
        val unitId: Int? = null,
        val locationSubtype: String = "",
        val quantity: Int = 1,
        val notes: String = "",
        val details: Map<String, String> = emptyMap()
)

/** Category attribute definition from database */
data class CategoryAttributeLegacy(
        val id: Int,
        val categoryId: Int,
        val attribute: String,
        val inputType: String, // text, select, radio, checkbox
        val isRequired: Boolean = false,
        val dependsOnAttributeId: Int? = null,
        val dependsOnOptionId: Int? = null,
        val options: List<AttributeOption> = emptyList()
)

data class AttributeOptionLegacy(val id: Int, val optionValue: String, val attributeId: Int)

/** Vehicle selection models */
data class CarBrandLegacy(val id: Int, val name: String)

data class CarModelLegacy(
        val id: Int,
        val brandId: Int,
        val modelName: String,
        val modelImage: String? = null
)

data class CarGenerationLegacy(
        val id: Int,
        val modelId: Int,
        val name: String,
        val startProductionYear: Int? = null,
        val endProductionYear: Int? = null
)

data class CarVariationLegacy(val id: Int, val generationId: Int, val name: String)

data class CarTrimLegacy(val id: Int, val variationId: Int, val name: String)

/** Storage models */
data class StorageAreaLegacy(val id: Int, val name: String, val description: String? = null)

data class StorageUnitLegacy(
        val id: Int,
        val areaId: Int,
        val name: String,
        val unitType: String,
        val description: String? = null
)

/** API request/response models */
data class CheckCompatibilityRequestLegacy(val partNumber: String, val categoryId: Int)

data class CheckCompatibilityResponseLegacy(
        val partName: String? = null,
        val compatiblePartNumbers: List<String> = emptyList(),
        val isEnginePart: Boolean = false,
        val engineCompatibility: List<EngineCompatibility> = emptyList(),
        val vehicleCompatibility: List<VehicleCompatibility> = emptyList(),
        val partnumberGroup: Int? = null,
        val status: String = "",
        val message: String = ""
)

data class GenerateTitleRequestLegacy(
        val categoryId: Int,
        val partNumber: String? = null,
        val vehicleInfo: String? = null,
        val compatibilityData: CompatibilityData? = null
)

data class GenerateDescriptionRequestLegacy(
        val title: String,
        val categoryId: Int,
        val condition: String,
        val compatibilityData: CompatibilityData? = null
)

data class CreatePartRequestLegacy(
        val categoryId: Int,
        val title: String,
        val description: String,
        val partnumberGroup: Int? = null,
        val createdBy: Int,
        val images: List<PartImage>,
        val condition: String,
        val newCondition: ConditionDataLegacy? = null,
        val usedCondition: ConditionDataLegacy? = null,
        val attributes: List<CategoryAttributeValue>,
        val vehicles: VehicleLinkLegacy,
        val engines: List<String> = emptyList()
)

data class ConditionDataLegacy(
        val stock: Int,
        val price: Double,
        val discountedPrice: Double? = null
)

data class VehicleLinkLegacy(
        val mode: String, // "compatibility" or "manual"
        val list: List<VehicleCompatibility>? = null,
        val trimId: Int? = null
)

data class CreatePartResponseLegacy(val partId: Int, val title: String)

data class SaveLocationRequestLegacy(
        val unitId: Int,
        val quantity: Int,
        val locationSubtype: String,
        val details: Map<String, String>,
        val notes: String = ""
)

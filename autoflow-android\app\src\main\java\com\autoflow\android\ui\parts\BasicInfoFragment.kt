package com.autoflow.android.ui.parts

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.fragment.app.Fragment
import com.autoflow.android.R

class BasicInfoFragment : Fragment() {

    // UI Elements
    private lateinit var partNameInput: EditText
    private lateinit var partNumberInput: EditText
    private lateinit var partTitleInput: EditText
    private lateinit var partDescriptionInput: EditText
    private lateinit var partBrandSpinner: Spinner
    private lateinit var partCategorySpinner: Spinner
    private lateinit var partConditionSpinner: Spinner
    private lateinit var partStatusSpinner: Spinner

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_basic_info, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        initializeViews(view)
        setupSpinners()
    }

    private fun initializeViews(view: View) {
        partNameInput = view.findViewById(R.id.partNameInput)
        partNumberInput = view.findViewById(R.id.partNumberInput)
        partTitleInput = view.findViewById(R.id.partTitleInput)
        partDescriptionInput = view.findViewById(R.id.partDescriptionInput)
        partBrandSpinner = view.findViewById(R.id.partBrandSpinner)
        partCategorySpinner = view.findViewById(R.id.partCategorySpinner)
        partConditionSpinner = view.findViewById(R.id.partConditionSpinner)
        partStatusSpinner = view.findViewById(R.id.partStatusSpinner)
    }

    private fun setupSpinners() {
        // Brand spinner
        val brands = arrayOf("VW", "AUDI", "BMW", "Mercedes", "Porsche", "Other")
        val brandAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, brands)
        brandAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        partBrandSpinner.adapter = brandAdapter

        // Category spinner
        val categories = arrayOf(
            "Brakes", "Engine", "Transmission", "Suspension", "Electrical", 
            "Body", "Interior", "Exhaust", "Cooling", "Fuel System", "Other"
        )
        val categoryAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, categories)
        categoryAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        partCategorySpinner.adapter = categoryAdapter

        // Condition spinner
        val conditions = arrayOf("New", "Used - Excellent", "Used - Good", "Used - Fair", "Refurbished")
        val conditionAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, conditions)
        conditionAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        partConditionSpinner.adapter = conditionAdapter

        // Status spinner
        val statuses = arrayOf("Active", "Inactive", "Out of Stock", "Discontinued")
        val statusAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, statuses)
        statusAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        partStatusSpinner.adapter = statusAdapter
    }

    fun loadPartData(
        name: String,
        partNumber: String,
        title: String,
        description: String,
        brand: String,
        category: String
    ) {
        partNameInput.setText(name)
        partNumberInput.setText(partNumber)
        partTitleInput.setText(title)
        partDescriptionInput.setText(description)
        
        // Set spinner selections
        setSpinnerSelection(partBrandSpinner, brand)
        setSpinnerSelection(partCategorySpinner, category)
    }

    private fun setSpinnerSelection(spinner: Spinner, value: String) {
        val adapter = spinner.adapter as ArrayAdapter<String>
        val position = adapter.getPosition(value)
        if (position >= 0) {
            spinner.setSelection(position)
        }
    }

    fun validateInputs(): Boolean {
        var isValid = true

        if (partNameInput.text.toString().trim().isEmpty()) {
            partNameInput.error = "Part name is required"
            isValid = false
        }

        if (partNumberInput.text.toString().trim().isEmpty()) {
            partNumberInput.error = "Part number is required"
            isValid = false
        }

        return isValid
    }

    fun getPartData(): Map<String, Any> {
        return mapOf(
            "name" to partNameInput.text.toString().trim(),
            "part_number" to partNumberInput.text.toString().trim(),
            "title" to partTitleInput.text.toString().trim(),
            "description" to partDescriptionInput.text.toString().trim(),
            "brand" to partBrandSpinner.selectedItem.toString(),
            "category" to partCategorySpinner.selectedItem.toString(),
            "condition" to partConditionSpinner.selectedItem.toString(),
            "status" to partStatusSpinner.selectedItem.toString()
        )
    }
}

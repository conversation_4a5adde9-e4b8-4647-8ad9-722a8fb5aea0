<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@android:color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Part Image -->
        <ImageView
            android:id="@+id/partImage"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/image_placeholder_background"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_part_placeholder" />

        <!-- Part Details -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Part Name -->
            <TextView
                android:id="@+id/partName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Brake Pad Set"
                android:textColor="@color/brand_black"
                android:textSize="16sp"
                android:textStyle="bold"
                android:maxLines="2"
                android:ellipsize="end" />

            <!-- Part Number -->
            <TextView
                android:id="@+id/partNumber"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Part #: BP001"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:layout_marginTop="4dp" />

            <!-- Brand and Category -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="4dp">

                <TextView
                    android:id="@+id/partBrand"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="VW"
                    android:textColor="@color/brand_teal"
                    android:textSize="12sp"
                    android:background="@drawable/chip_background_teal"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="2dp"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:id="@+id/partCategory"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Brakes"
                    android:textColor="@color/brand_mustard_dark"
                    android:textSize="12sp"
                    android:background="@drawable/chip_background_mustard"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="2dp" />

            </LinearLayout>

            <!-- Description -->
            <TextView
                android:id="@+id/partDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="High-quality brake pads for VW Golf"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:layout_marginTop="8dp"
                android:maxLines="2"
                android:ellipsize="end" />

        </LinearLayout>

        <!-- Price and Stock -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="end"
            android:layout_marginStart="16dp">

            <!-- Price -->
            <TextView
                android:id="@+id/partPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="$89.99"
                android:textColor="@color/brand_black"
                android:textSize="18sp"
                android:textStyle="bold" />

            <!-- Stock Status -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="4dp">

                <View
                    android:id="@+id/stockIndicator"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:background="@drawable/stock_indicator_green"
                    android:layout_marginEnd="4dp" />

                <TextView
                    android:id="@+id/stockText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="15 in stock"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp" />

            </LinearLayout>

            <!-- Action Button -->
            <Button
                android:id="@+id/viewDetailsButton"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:text="View Details"
                android:textColor="@color/text_on_brand"
                android:background="@drawable/button_filled_teal"
                android:paddingHorizontal="12dp"
                android:textSize="12sp"
                android:layout_marginTop="8dp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>

<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.autoflow.android"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="23"
        android:targetSdkVersion="33" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />

    <!-- Camera and Storage permissions -->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />

    <!-- Camera feature -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />

    <permission
        android:name="com.autoflow.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.autoflow.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:icon="@drawable/ic_launcher"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.AutoflowAndroid" >

        <!-- Main Activity - Entry Point -->
        <activity
            android:name="com.autoflow.android.MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.AutoflowAndroid" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Authentication Activities -->
        <activity
            android:name="com.autoflow.android.ui.auth.LoginActivity"
            android:exported="false"
            android:theme="@style/Theme.AutoflowAndroid" />
        <activity
            android:name="com.autoflow.android.ui.auth.OTPActivity"
            android:exported="false"
            android:theme="@style/Theme.AutoflowAndroid" />

        <!-- Dashboard Activity -->
        <activity
            android:name="com.autoflow.android.ui.dashboard.DashboardActivity"
            android:exported="false"
            android:theme="@style/Theme.AutoflowAndroid" />

        <!-- Search Activities -->
        <activity
            android:name="com.autoflow.android.ui.search.SearchResultsActivity"
            android:exported="false"
            android:theme="@style/Theme.AutoflowAndroid" />

        <!-- Parts Management Activities -->
        <activity
            android:name="com.autoflow.android.ui.parts.PartsListActivity"
            android:exported="false"
            android:theme="@style/Theme.AutoflowAndroid" />
        <activity
            android:name="com.autoflow.android.ui.parts.PartDetailActivity"
            android:exported="false"
            android:parentActivityName="com.autoflow.android.ui.search.SearchResultsActivity"
            android:theme="@style/Theme.AutoflowAndroid" />
        <activity
            android:name="com.autoflow.android.ui.parts.PartUpdateActivity"
            android:exported="false"
            android:parentActivityName="com.autoflow.android.ui.parts.PartDetailActivity"
            android:theme="@style/Theme.AutoflowAndroid" />

        <!-- Part Creation Flow Activities -->
        <activity
            android:name="com.autoflow.android.ui.parts.AddPartActivity"
            android:exported="false"
            android:parentActivityName="com.autoflow.android.ui.dashboard.DashboardActivity"
            android:theme="@style/Theme.AutoflowAndroid" />
        <activity
            android:name="com.autoflow.android.ui.parts.CameraCaptureActivity"
            android:exported="false"
            android:parentActivityName="com.autoflow.android.ui.dashboard.DashboardActivity"
            android:theme="@style/Theme.AutoflowAndroid" />
        <activity
            android:name="com.autoflow.android.ui.parts.ImageCropActivity"
            android:exported="false"
            android:parentActivityName="com.autoflow.android.ui.parts.CameraCaptureActivity"
            android:theme="@style/Theme.AutoflowAndroid" />
        <activity
            android:name="com.autoflow.android.ui.parts.CategorySelectionActivity"
            android:exported="false"
            android:parentActivityName="com.autoflow.android.ui.parts.ImageCropActivity"
            android:theme="@style/Theme.AutoflowAndroid" />
        <activity
            android:name="com.autoflow.android.ui.parts.SimpleCategorySelectionActivity"
            android:exported="false"
            android:parentActivityName="com.autoflow.android.ui.parts.ImageCropActivity"
            android:theme="@style/Theme.AutoflowAndroid" />
        <activity
            android:name="com.autoflow.android.ui.parts.HierarchicalCategorySelectionActivity"
            android:exported="false"
            android:parentActivityName="com.autoflow.android.ui.parts.ImageCropActivity"
            android:theme="@style/Theme.AutoflowAndroid" />
        <activity
            android:name="com.autoflow.android.ui.parts.WebReplicaCategorySelectionActivity"
            android:exported="false"
            android:parentActivityName="com.autoflow.android.ui.parts.ImageCropActivity"
            android:theme="@style/Theme.AutoflowAndroid" />

        <!-- Complete Add Part Flow Activities -->
        <activity
            android:name="com.autoflow.android.ui.addpart.AddPartActivity"
            android:exported="false"
            android:parentActivityName="com.autoflow.android.ui.parts.ImageCropActivity"
            android:theme="@style/Theme.AutoflowAndroid" />
        <activity
            android:name="com.autoflow.android.ui.storage.StorageLocationActivity"
            android:exported="false"
            android:parentActivityName="com.autoflow.android.ui.addpart.AddPartActivity"
            android:theme="@style/Theme.AutoflowAndroid" />

        <!-- UCrop Activity for image cropping -->
        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />

        <!-- FileProvider for sharing images -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.autoflow.android.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <service
            android:name="androidx.camera.core.impl.MetadataHolderService"
            android:enabled="false"
            android:exported="false" >
            <meta-data
                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
        </service>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.autoflow.android.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>
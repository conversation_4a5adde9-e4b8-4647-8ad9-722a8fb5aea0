#Wed Aug 20 03:28:14 EAT 2025
com.autoflow.android.app-main-49\:/anim/bounce_in.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_bounce_in.xml.flat
com.autoflow.android.app-main-49\:/anim/fade_in.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_fade_in.xml.flat
com.autoflow.android.app-main-49\:/anim/shake.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_shake.xml.flat
com.autoflow.android.app-main-49\:/anim/slide_up.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_up.xml.flat
com.autoflow.android.app-main-49\:/drawable/button_filled_mustard.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_filled_mustard.xml.flat
com.autoflow.android.app-main-49\:/drawable/button_filled_teal.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_filled_teal.xml.flat
com.autoflow.android.app-main-49\:/drawable/button_outline_mustard.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_outline_mustard.xml.flat
com.autoflow.android.app-main-49\:/drawable/button_outline_teal.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_outline_teal.xml.flat
com.autoflow.android.app-main-49\:/drawable/button_text_red.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_text_red.xml.flat
com.autoflow.android.app-main-49\:/drawable/button_text_secondary.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_button_text_secondary.xml.flat
com.autoflow.android.app-main-49\:/drawable/capture_button_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_capture_button_background.xml.flat
com.autoflow.android.app-main-49\:/drawable/card_background_mustard.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_card_background_mustard.xml.flat
com.autoflow.android.app-main-49\:/drawable/card_background_teal.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_card_background_teal.xml.flat
com.autoflow.android.app-main-49\:/drawable/card_background_white.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_card_background_white.xml.flat
com.autoflow.android.app-main-49\:/drawable/chip_background_mustard.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_chip_background_mustard.xml.flat
com.autoflow.android.app-main-49\:/drawable/chip_background_teal.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_chip_background_teal.xml.flat
com.autoflow.android.app-main-49\:/drawable/circle_button_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_button_background.xml.flat
com.autoflow.android.app-main-49\:/drawable/circle_light_purple.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_light_purple.xml.flat
com.autoflow.android.app-main-49\:/drawable/circle_purple.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_purple.xml.flat
com.autoflow.android.app-main-49\:/drawable/dashboard_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dashboard_background.xml.flat
com.autoflow.android.app-main-49\:/drawable/dialog_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dialog_background.xml.flat
com.autoflow.android.app-main-49\:/drawable/dropdown_item_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_item_background.xml.flat
com.autoflow.android.app-main-49\:/drawable/dropdown_parent_item_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dropdown_parent_item_background.xml.flat
com.autoflow.android.app-main-49\:/drawable/floating_button_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_floating_button_background.xml.flat
com.autoflow.android.app-main-49\:/drawable/floating_share_button_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_floating_share_button_background.xml.flat
com.autoflow.android.app-main-49\:/drawable/header_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_header_background.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_add.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_arrow_back.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arrow_back.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_autoflow_logo.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_autoflow_logo.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_camera.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_camera.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_car_placeholder.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_car_placeholder.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_categories.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_categories.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_category.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_category.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_check_circle.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_check_circle.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_clear.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_clear.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_close_small.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_close_small.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_delete.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_delete.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_edit.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_edit.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_email.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_email.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_error.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_error.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_expand_more.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_expand_more.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_filter.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_filter.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_fingerprint.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_fingerprint.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_history.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_history.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_home.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_home.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_image.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_image.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_launcher.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_logout.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_logout.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_menu.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_menu.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_more_vert.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_more_vert.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_part_placeholder.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_part_placeholder.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_person.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_person.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_search.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_search.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_search_empty.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_search_empty.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_settings.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_settings.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_share.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_share.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_sort.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_sort.xml.flat
com.autoflow.android.app-main-49\:/drawable/ic_trending.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_trending.xml.flat
com.autoflow.android.app-main-49\:/drawable/image_placeholder_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_image_placeholder_background.xml.flat
com.autoflow.android.app-main-49\:/drawable/info_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_info_background.xml.flat
com.autoflow.android.app-main-49\:/drawable/input_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_input_background.xml.flat
com.autoflow.android.app-main-49\:/drawable/login_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_login_background.xml.flat
com.autoflow.android.app-main-49\:/drawable/logo_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_logo_background.xml.flat
com.autoflow.android.app-main-49\:/drawable/otp_digit_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_otp_digit_background.xml.flat
com.autoflow.android.app-main-49\:/drawable/otp_illustration.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_otp_illustration.xml.flat
com.autoflow.android.app-main-49\:/drawable/otp_illustration_simple.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_otp_illustration_simple.xml.flat
com.autoflow.android.app-main-49\:/drawable/search_input_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_search_input_background.xml.flat
com.autoflow.android.app-main-49\:/drawable/spinner_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_spinner_background.xml.flat
com.autoflow.android.app-main-49\:/drawable/stock_indicator_green.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_stock_indicator_green.xml.flat
com.autoflow.android.app-main-49\:/drawable/stock_indicator_red.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_stock_indicator_red.xml.flat
com.autoflow.android.app-main-49\:/drawable/stock_indicator_yellow.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_stock_indicator_yellow.xml.flat
com.autoflow.android.app-main-49\:/layout-land/activity_otp.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout-land_activity_otp.xml.flat
com.autoflow.android.app-main-49\:/layout/activity_add_part.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_add_part.xml.flat
com.autoflow.android.app-main-49\:/layout/activity_camera_capture.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_camera_capture.xml.flat
com.autoflow.android.app-main-49\:/layout/activity_category_selection.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_category_selection.xml.flat
com.autoflow.android.app-main-49\:/layout/activity_dashboard.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_dashboard.xml.flat
com.autoflow.android.app-main-49\:/layout/activity_image_crop.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_image_crop.xml.flat
com.autoflow.android.app-main-49\:/layout/activity_login.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_login.xml.flat
com.autoflow.android.app-main-49\:/layout/activity_otp.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_otp.xml.flat
com.autoflow.android.app-main-49\:/layout/activity_part_detail.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_part_detail.xml.flat
com.autoflow.android.app-main-49\:/layout/activity_part_update.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_part_update.xml.flat
com.autoflow.android.app-main-49\:/layout/activity_search_results.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_search_results.xml.flat
com.autoflow.android.app-main-49\:/layout/content_dashboard.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_content_dashboard.xml.flat
com.autoflow.android.app-main-49\:/layout/dialog_filters.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_filters.xml.flat
com.autoflow.android.app-main-49\:/layout/dialog_sort.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_sort.xml.flat
com.autoflow.android.app-main-49\:/layout/footer_backend.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_footer_backend.xml.flat
com.autoflow.android.app-main-49\:/layout/fragment_alternative_parts.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_alternative_parts.xml.flat
com.autoflow.android.app-main-49\:/layout/fragment_basic_info.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_basic_info.xml.flat
com.autoflow.android.app-main-49\:/layout/fragment_category.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_category.xml.flat
com.autoflow.android.app-main-49\:/layout/fragment_compatible_vehicles.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_compatible_vehicles.xml.flat
com.autoflow.android.app-main-49\:/layout/fragment_images.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_images.xml.flat
com.autoflow.android.app-main-49\:/layout/fragment_pricing_inventory.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_pricing_inventory.xml.flat
com.autoflow.android.app-main-49\:/layout/header_backend.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_header_backend.xml.flat
com.autoflow.android.app-main-49\:/layout/item_category.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_category.xml.flat
com.autoflow.android.app-main-49\:/layout/item_part.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_part.xml.flat
com.autoflow.android.app-main-49\:/layout/item_part_image.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_part_image.xml.flat
com.autoflow.android.app-main-49\:/layout/item_search_suggestion.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_search_suggestion.xml.flat
com.autoflow.android.app-main-49\:/layout/layout_search_with_suggestions.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_layout_search_with_suggestions.xml.flat
com.autoflow.android.app-main-49\:/menu/part_detail_menu.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_part_detail_menu.xml.flat
com.autoflow.android.app-main-49\:/xml/file_paths.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_file_paths.xml.flat

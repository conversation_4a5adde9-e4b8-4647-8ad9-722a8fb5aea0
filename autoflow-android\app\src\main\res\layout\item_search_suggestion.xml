<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:padding="16dp"
    android:background="?attr/selectableItemBackground">

    <!-- Suggestion Icon -->
    <ImageView
        android:id="@+id/suggestionIcon"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:src="@drawable/ic_search"
        android:tint="@color/text_secondary"
        android:layout_marginEnd="16dp" />

    <!-- Suggestion Content -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- Suggestion Text -->
        <TextView
            android:id="@+id/suggestionText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="brake pads"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:maxLines="1"
            android:ellipsize="end" />

        <!-- Suggestion Subtext -->
        <TextView
            android:id="@+id/suggestionSubtext"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Recent search"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            android:layout_marginTop="2dp"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Remove Button -->
    <ImageButton
        android:id="@+id/removeSuggestionButton"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@drawable/ic_close_small"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:contentDescription="Remove suggestion"
        android:tint="@color/text_secondary"
        android:padding="8dp"
        android:visibility="gone" />

</LinearLayout>

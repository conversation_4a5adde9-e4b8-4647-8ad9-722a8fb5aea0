package com.autoflow.android.data.api

import com.autoflow.android.domain.models.*
import java.io.File

/**
 * API service interface for Add Part flow Simplified interface - HTTP implementation handled by
 * AddPartApiClient using OkHttp
 */
interface AddPartApiService {

    // Categories and attributes
    suspend fun getCategories(): CategoriesResponse
    suspend fun getCategoryAttributes(categoryId: Int): CategoryAttributesResponse

    // Vehicle selection cascade
    suspend fun getBrands(): List<com.autoflow.android.domain.models.CarBrand>
    suspend fun getModels(brandId: Int): List<com.autoflow.android.domain.models.CarModel>
    suspend fun getGenerations(modelId: Int): List<com.autoflow.android.domain.models.CarGeneration>
    suspend fun getVariations(
            generationId: Int
    ): List<com.autoflow.android.domain.models.CarVariation>
    suspend fun getTrims(variationId: Int): List<com.autoflow.android.domain.models.CarTrim>

    // Part number compatibility (AI-powered)
    suspend fun checkCompatibility(request: CheckCompatibilityRequest): CheckCompatibilityResponse

    // Title and description generation
    suspend fun generateTitle(request: GenerateTitleRequest): GenerateTitleResponse
    suspend fun generateDescription(
            request: GenerateDescriptionRequest
    ): GenerateDescriptionResponse

    // Image upload
    suspend fun uploadImage(imageFile: File): ImageUploadResponse

    // Part creation (transactional)
    suspend fun createPart(request: CreatePartRequest): CreatePartResponse

    // Storage locations
    suspend fun getStorageAreas(): List<com.autoflow.android.domain.models.StorageArea>
    suspend fun getStorageUnits(
            areaId: Int? = null
    ): List<com.autoflow.android.domain.models.StorageUnit>
    suspend fun savePartLocation(partId: Int, request: SaveLocationRequest): SaveLocationResponse
}

// Response wrapper models (using domain models)
data class CategoriesResponse(
        val categories: List<com.autoflow.android.domain.models.CategoryResponse>,
        val success: Boolean = true,
        val message: String = ""
)

data class CategoryAttributesResponse(
        val attributes: List<com.autoflow.android.domain.models.CategoryAttribute>,
        val success: Boolean = true,
        val message: String = ""
)

data class CheckCompatibilityResponse(
        val success: Boolean,
        val partName: String? = null,
        val compatiblePartNumbers: List<String> = emptyList(),
        val engineCompatibility: List<com.autoflow.android.domain.models.EngineCompatibility> =
                emptyList(),
        val vehicleCompatibility: List<com.autoflow.android.domain.models.VehicleCompatibility> =
                emptyList(),
        val partnumberGroup: Int? = null,
        val message: String = ""
)

data class GenerateTitleResponse(
        val title: String,
        val success: Boolean = true,
        val message: String = ""
)

data class GenerateDescriptionResponse(
        val description: String,
        val success: Boolean = true,
        val message: String = ""
)

data class ImageUploadResponse(
        val imageUrl: String,
        val success: Boolean = true,
        val message: String = ""
)

data class CreatePartResponse(
        val partId: Int,
        val title: String,
        val success: Boolean = true,
        val message: String = ""
)

data class SaveLocationResponse(val success: Boolean, val message: String = "")

// Request models
data class CheckCompatibilityRequest(val partNumber: String, val categoryId: Int)

data class GenerateTitleRequest(
        val categoryId: Int,
        val partNumber: String? = null,
        val vehicleInfo: String? = null,
        val compatibilityData: CompatibilityData? = null
)

data class GenerateDescriptionRequest(
        val title: String,
        val categoryId: Int,
        val condition: String,
        val compatibilityData: CompatibilityData? = null
)

data class CreatePartRequest(
        val categoryId: Int,
        val title: String,
        val description: String,
        val partnumberGroup: Int? = null,
        val createdBy: Int,
        val images: List<String>,
        val condition: String,
        val newCondition: ConditionData? = null,
        val usedCondition: ConditionData? = null,
        val attributes: Map<String, Any> = emptyMap(),
        val vehicles: VehicleLink,
        val engines: List<String> = emptyList()
)

data class ConditionData(val stock: Int, val price: Double, val discountedPrice: Double? = null)

data class VehicleLink(
        val mode: String, // "compatibility" or "manual"
        val list: List<com.autoflow.android.domain.models.VehicleCompatibility>? = null,
        val trimId: Int? = null
)

data class SaveLocationRequest(
        val unitId: Int,
        val quantity: Int,
        val locationSubtype: String,
        val details: Map<String, String> = emptyMap(),
        val notes: String? = null
)

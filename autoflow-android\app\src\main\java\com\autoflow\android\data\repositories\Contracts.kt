package com.autoflow.android.data.repositories

import android.net.Uri
import com.autoflow.android.data.api.ApiResult
import com.autoflow.android.data.api.dto.*

interface ImagesRepository {
    suspend fun uploadImage(uri: Uri, filename: String, onProgress: (Int) -> Unit = {}): ApiResult<String>
}

interface CategoriesRepository {
    suspend fun getCategories(): ApiResult<List<CategoryDto>>
    suspend fun getAttributes(categoryId: Int): ApiResult<List<AttributeDto>>
}

interface VehiclesRepository {
    suspend fun getModels(brandId: Int): ApiResult<List<CarModelDto>>
    suspend fun getGenerations(modelId: Int): ApiResult<List<CarGenerationDto>>
    suspend fun getVariations(generationId: Int): ApiResult<List<CarVariationDto>>
    suspend fun getTrims(variationId: Int): ApiResult<List<CarTrimDto>>
}

interface CompatibilityRepository {
    suspend fun checkCompatibility(body: CheckCompatibilityBody): ApiResult<CompatibilityDto>
}

interface PartsRepository {
    suspend fun generateTitle(body: GenerateTitleBody): ApiResult<TitleDto>
    suspend fun generateDescription(body: GenerateDescriptionBody): ApiResult<DescriptionDto>
    suspend fun createPart(idempotencyKey: String, body: CreatePartBody): ApiResult<CreatePartDto>
}

interface StorageRepository {
    suspend fun getAreas(): ApiResult<List<StorageAreaDto>>
    suspend fun getUnits(areaId: Int?): ApiResult<List<StorageUnitDto>>
    suspend fun saveLocation(partId: Int, body: SaveLocationBody): ApiResult<Unit>
}


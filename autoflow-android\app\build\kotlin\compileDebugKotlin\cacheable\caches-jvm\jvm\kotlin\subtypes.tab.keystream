'com.autoflow.android.data.api.ApiResult/com.autoflow.android.data.api.AddPartApiServiceandroidx.fragment.app.Fragment0com.autoflow.android.ui.base.BaseBackendActivityjava.io.Serializable1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolderkotlin.Enum$androidx.fragment.app.DialogFragment(androidx.appcompat.app.AppCompatActivity,androidx.lifecycle.ViewModelProvider.Factoryandroidx.lifecycle.ViewModel7com.autoflow.android.data.repositories.ImagesRepository;com.autoflow.android.data.repositories.CategoriesRepository9com.autoflow.android.data.repositories.VehiclesRepository>com.autoflow.android.data.repositories.CompatibilityRepository6com.autoflow.android.data.repositories.PartsRepository8com.autoflow.android.data.repositories.StorageRepositoryokhttp3.RequestBody                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       
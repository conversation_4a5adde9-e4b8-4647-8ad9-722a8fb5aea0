<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@android:color/black"
    tools:context=".ui.parts.ImageCropActivity">

    <!-- Top Bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="@color/brand_primary"
        android:paddingHorizontal="16dp">

        <Button
            android:id="@+id/cancelButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Cancel"
            android:textColor="@android:color/white"
            android:background="@android:color/transparent"
            android:textAllCaps="false" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Crop Image"
            android:textColor="@android:color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="center" />

        <Button
            android:id="@+id/cropButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Done"
            android:textColor="@android:color/white"
            android:background="@android:color/transparent"
            android:textAllCaps="false"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- Crop Instructions -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Review your image and tap 'Done' to proceed"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        android:gravity="center"
        android:padding="16dp"
        android:background="#33000000" />

    <!-- Image Preview -->
    <ImageView
        android:id="@+id/imageView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:scaleType="centerCrop"
        android:background="@android:color/black"
        android:contentDescription="Part image preview" />

    <!-- Bottom Instructions -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="#33000000"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Next Steps:"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="• Image will be cropped to square format\n• Next: Select part category\n• Then: Fill category-specific details"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            android:lineSpacingExtra="2dp" />

    </LinearLayout>

</LinearLayout>

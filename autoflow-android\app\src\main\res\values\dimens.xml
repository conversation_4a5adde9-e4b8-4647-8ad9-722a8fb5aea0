<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Material Design 3 Spacing System -->
    <dimen name="spacing_xs">4dp</dimen>
    <dimen name="spacing_small">8dp</dimen>
    <dimen name="spacing_medium">12dp</dimen>
    <dimen name="spacing_large">16dp</dimen>
    <dimen name="spacing_xl">20dp</dimen>
    <dimen name="spacing_xxl">24dp</dimen>
    <dimen name="spacing_xxxl">32dp</dimen>

    <!-- Material Design 3 Corner Radius System -->
    <dimen name="corner_radius_small">4dp</dimen>
    <dimen name="corner_radius_medium">8dp</dimen>
    <dimen name="corner_radius_large">12dp</dimen>
    <dimen name="corner_radius_extra_large">20dp</dimen>
    <dimen name="corner_radius_full">28dp</dimen>

    <!-- Material Design 3 Elevation System -->
    <dimen name="elevation_none">0dp</dimen>
    <dimen name="elevation_low">1dp</dimen>
    <dimen name="elevation_medium">3dp</dimen>
    <dimen name="elevation_high">6dp</dimen>
    <dimen name="elevation_extra_high">12dp</dimen>

    <!-- Material Design 3 Typography Scale -->
    <dimen name="text_size_small">12sp</dimen>
    <dimen name="text_size_medium">14sp</dimen>
    <dimen name="text_size_large">16sp</dimen>
    <dimen name="text_size_xl">20sp</dimen>
    <dimen name="text_size_xxl">24sp</dimen>
    <dimen name="text_size_headline">28sp</dimen>

    <!-- Component Specific Dimensions -->
    <dimen name="dropdown_item_height">56dp</dimen>
    <dimen name="dropdown_trigger_height">56dp</dimen>
    <dimen name="search_input_height">48dp</dimen>
    <dimen name="chip_height">32dp</dimen>
    <dimen name="icon_size_small">16dp</dimen>
    <dimen name="icon_size_medium">24dp</dimen>
    <dimen name="icon_size_large">32dp</dimen>
</resources> 
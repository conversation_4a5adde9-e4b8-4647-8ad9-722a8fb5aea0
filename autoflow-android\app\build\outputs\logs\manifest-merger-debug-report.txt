-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:144:9-152:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:148:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:146:13-64
	android:exported
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:147:13-37
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:145:13-62
manifest
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:2:1-156:12
INJECTED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:2:1-156:12
INJECTED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:2:1-156:12
INJECTED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:2:1-156:12
MERGED from [com.github.yalantis:ucrop:2.2.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e54198a82394605fc9efe0eed5d1d5f\transformed\jetified-ucrop-2.2.8\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2d40d61e01e6ef2b0f2f90a3e8aca5\transformed\material-1.9.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7078e8e71893b82f32857a328dfc89\transformed\biometric-1.1.0\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.camera:camera-view:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f1c71fda7d38cac5836f376d3cd4841\transformed\jetified-camera-view-1.2.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd4ca0b0d75028876529c00154594ee\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f59aa3ce8c9c3009cb30362368e734b6\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\303aa64de72ab8a914eac82254eebb45\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e15637815ca42e4b319252e4b709f57\transformed\jetified-glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7f5a8b5debc415a9ff99230e8cb1d6b\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6b682ebfb3461f254619670e2b339ea\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6afcc8c092fdc0e69dcc0a494c737cf3\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba1c76a861f67417531d3b17cb9afe8e\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\00cf38f4190a53e97ba9209600294ab2\transformed\jetified-activity-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d922eac76ec096fc81edd177259e3f1\transformed\jetified-activity-ktx-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f8aa0f8ce018e96e0c47b96e3baa33\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\894a783dca4aa815eb545af54b4b48b6\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-lifecycle:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e3d9b328cdd75da8fd382fc6a82584c\transformed\jetified-camera-lifecycle-1.2.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\14db308c6417231c0c3d58fa46d38064\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1235f37b232d25913db15dc3cf2502d1\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe349995b136e85f6a005f5d89fa32f9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ece72575c0e3a0c9796d85af5708f2d2\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6ecc5ab7198e78d415df13f119ae1fd\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad4577fe8a010038de8762b6815943f3\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52cdc81732f911f8eab31db215be4e67\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b56ec4989056985e0fc55b312a352c65\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8816de40106596fc9a016cd8eebb8eae\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\574a51baaed8611751e256cb469fde66\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ae3f07561c5228f22a7d470c7685769\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\db03cf5cf2ca6b085efb9013b689807c\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e679ca1755443ba159bad16970bae55\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\125478a017f02376aaf907258ea43015\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e511441ebcdbb52494bd61e876808728\transformed\jetified-core-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5db4685bf6ccf83c8280681ab238ef\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\402ab57b0e7ba6f5089b05fcb3c0ea47\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3fe071448913bb1730b603dba651790\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba70780b2769bb18a5f53897935adda0\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\adb6c888e1928c051191f75518118095\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\82df6f32405bc3ccfe53fdcfe1ddea3b\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed0cb4ef6b1722f5f4c814ba33951cff\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\70aee03866c8d8d27e2862c7da29844e\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c89db88e6ae2827a86b641e6bb26d8d1\transformed\jetified-lifecycle-livedata-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3ebb97f7886adc68cb1b96e69a304aa\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b9c5a6e42df1b61f13f380ee190613b\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.13\transforms\655c0f29bd4e7daa35521825d54b2b0b\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff5b21ba38e36a4569cbfeacc97468f9\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6cbba339ad84aadcb7c88ebb4e3df614\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1905fa04720f9aded41c2c7266b432f5\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45f0d462065695589efc5ad5bfd8c56f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacbe320dbe79b1db3462fa879db08f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7231db8ad7ab98c57915fe35d469d361\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd40cf52013e79f216188201b79d1d5b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef966fdc63c2cf5a4c000e6db6f28be6\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf327a7e21d5807061f1d7629f8a7744\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\02815fae27931ae44da7fab74be7eac3\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61fa31cda83801bd82678badafb23161\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8897813398ed3770443f80a5810a48fc\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c710f4b0e8bd721889621d641ebb86\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:6:5-79
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.USE_BIOMETRIC
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:7:5-72
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7078e8e71893b82f32857a328dfc89\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7078e8e71893b82f32857a328dfc89\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:7:22-69
uses-permission#android.permission.USE_FINGERPRINT
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:8:5-74
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7078e8e71893b82f32857a328dfc89\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7078e8e71893b82f32857a328dfc89\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:8:22-71
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:11:5-65
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:11:22-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:12:5-80
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:12:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:13:5-14:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:14:9-35
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:13:22-78
uses-feature#android.hardware.camera
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:17:5-85
	android:required
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:17:58-82
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:17:19-57
uses-feature#android.hardware.camera.autofocus
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:18:5-95
	android:required
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:18:68-92
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:18:19-67
application
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:20:5-154:19
INJECTED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:20:5-154:19
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2d40d61e01e6ef2b0f2f90a3e8aca5\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2d40d61e01e6ef2b0f2f90a3e8aca5\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd4ca0b0d75028876529c00154594ee\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd4ca0b0d75028876529c00154594ee\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\894a783dca4aa815eb545af54b4b48b6\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\894a783dca4aa815eb545af54b4b48b6\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\14db308c6417231c0c3d58fa46d38064\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\14db308c6417231c0c3d58fa46d38064\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe349995b136e85f6a005f5d89fa32f9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe349995b136e85f6a005f5d89fa32f9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ece72575c0e3a0c9796d85af5708f2d2\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ece72575c0e3a0c9796d85af5708f2d2\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacbe320dbe79b1db3462fa879db08f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacbe320dbe79b1db3462fa879db08f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd40cf52013e79f216188201b79d1d5b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd40cf52013e79f216188201b79d1d5b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:24:9-35
	android:label
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:23:9-41
	android:icon
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:22:9-45
	android:allowBackup
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:21:9-35
	android:theme
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:25:9-53
activity#com.autoflow.android.MainActivity
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:28:9-37:20
	android:label
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:31:13-45
	android:exported
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:30:13-36
	android:theme
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:32:13-57
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:29:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:33:13-36:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:34:17-69
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:34:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:35:17-77
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:35:27-74
activity#com.autoflow.android.ui.auth.LoginActivity
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:40:9-43:60
	android:exported
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:42:13-37
	android:theme
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:43:13-57
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:41:13-50
activity#com.autoflow.android.ui.auth.OTPActivity
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:45:9-48:60
	android:exported
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:47:13-37
	android:theme
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:48:13-57
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:46:13-48
activity#com.autoflow.android.ui.dashboard.DashboardActivity
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:51:9-54:60
	android:exported
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:53:13-37
	android:theme
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:54:13-57
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:52:13-59
activity#com.autoflow.android.ui.search.SearchResultsActivity
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:57:9-60:60
	android:exported
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:59:13-37
	android:theme
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:60:13-57
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:58:13-60
activity#com.autoflow.android.ui.parts.PartsListActivity
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:63:9-66:60
	android:exported
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:65:13-37
	android:theme
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:66:13-57
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:64:13-55
activity#com.autoflow.android.ui.parts.PartDetailActivity
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:68:9-72:77
	android:parentActivityName
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:72:13-74
	android:exported
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:70:13-37
	android:theme
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:71:13-57
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:69:13-56
activity#com.autoflow.android.ui.parts.PartUpdateActivity
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:74:9-78:73
	android:parentActivityName
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:78:13-70
	android:exported
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:76:13-37
	android:theme
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:77:13-57
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:75:13-56
activity#com.autoflow.android.ui.parts.AddPartActivity
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:81:9-85:76
	android:parentActivityName
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:85:13-73
	android:exported
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:83:13-37
	android:theme
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:84:13-57
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:82:13-53
activity#com.autoflow.android.ui.parts.CameraCaptureActivity
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:87:9-91:76
	android:parentActivityName
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:91:13-73
	android:exported
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:89:13-37
	android:theme
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:90:13-57
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:88:13-59
activity#com.autoflow.android.ui.parts.ImageCropActivity
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:93:9-97:76
	android:parentActivityName
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:97:13-73
	android:exported
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:95:13-37
	android:theme
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:96:13-57
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:94:13-55
activity#com.autoflow.android.ui.parts.CategorySelectionActivity
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:99:9-103:72
	android:parentActivityName
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:103:13-69
	android:exported
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:101:13-37
	android:theme
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:102:13-57
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:100:13-63
activity#com.autoflow.android.ui.parts.SimpleCategorySelectionActivity
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:105:9-109:72
	android:parentActivityName
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:109:13-69
	android:exported
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:107:13-37
	android:theme
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:108:13-57
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:106:13-69
activity#com.autoflow.android.ui.parts.HierarchicalCategorySelectionActivity
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:111:9-115:72
	android:parentActivityName
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:115:13-69
	android:exported
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:113:13-37
	android:theme
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:114:13-57
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:112:13-75
activity#com.autoflow.android.ui.parts.WebReplicaCategorySelectionActivity
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:117:9-121:72
	android:parentActivityName
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:121:13-69
	android:exported
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:119:13-37
	android:theme
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:120:13-57
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:118:13-73
activity#com.autoflow.android.ui.addpart.AddPartActivity
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:124:9-128:72
	android:parentActivityName
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:128:13-69
	android:exported
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:126:13-37
	android:theme
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:127:13-57
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:125:13-55
activity#com.autoflow.android.ui.storage.StorageLocationActivity
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:130:9-134:72
	android:parentActivityName
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:134:13-69
	android:exported
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:132:13-37
	android:theme
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:133:13-57
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:131:13-63
activity#com.yalantis.ucrop.UCropActivity
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:137:9-141:72
	android:screenOrientation
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:140:13-49
	android:exported
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:139:13-37
	android:theme
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:141:13-69
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:138:13-60
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:149:13-151:54
	android:resource
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:151:17-51
	android:name
		ADDED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:150:17-67
uses-sdk
INJECTED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml
MERGED from [com.github.yalantis:ucrop:2.2.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e54198a82394605fc9efe0eed5d1d5f\transformed\jetified-ucrop-2.2.8\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.yalantis:ucrop:2.2.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\0e54198a82394605fc9efe0eed5d1d5f\transformed\jetified-ucrop-2.2.8\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2d40d61e01e6ef2b0f2f90a3e8aca5\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2d40d61e01e6ef2b0f2f90a3e8aca5\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7078e8e71893b82f32857a328dfc89\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7078e8e71893b82f32857a328dfc89\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.camera:camera-view:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f1c71fda7d38cac5836f376d3cd4841\transformed\jetified-camera-view-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-view:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\3f1c71fda7d38cac5836f376d3cd4841\transformed\jetified-camera-view-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd4ca0b0d75028876529c00154594ee\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\afd4ca0b0d75028876529c00154594ee\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f59aa3ce8c9c3009cb30362368e734b6\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f59aa3ce8c9c3009cb30362368e734b6\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\303aa64de72ab8a914eac82254eebb45\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\303aa64de72ab8a914eac82254eebb45\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e15637815ca42e4b319252e4b709f57\transformed\jetified-glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6e15637815ca42e4b319252e4b709f57\transformed\jetified-glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7f5a8b5debc415a9ff99230e8cb1d6b\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7f5a8b5debc415a9ff99230e8cb1d6b\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6b682ebfb3461f254619670e2b339ea\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.13\transforms\f6b682ebfb3461f254619670e2b339ea\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6afcc8c092fdc0e69dcc0a494c737cf3\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\6afcc8c092fdc0e69dcc0a494c737cf3\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba1c76a861f67417531d3b17cb9afe8e\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba1c76a861f67417531d3b17cb9afe8e\transformed\jetified-fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\00cf38f4190a53e97ba9209600294ab2\transformed\jetified-activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\00cf38f4190a53e97ba9209600294ab2\transformed\jetified-activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d922eac76ec096fc81edd177259e3f1\transformed\jetified-activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d922eac76ec096fc81edd177259e3f1\transformed\jetified-activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f8aa0f8ce018e96e0c47b96e3baa33\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f8aa0f8ce018e96e0c47b96e3baa33\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\894a783dca4aa815eb545af54b4b48b6\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\894a783dca4aa815eb545af54b4b48b6\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-lifecycle:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e3d9b328cdd75da8fd382fc6a82584c\transformed\jetified-camera-lifecycle-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-lifecycle:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e3d9b328cdd75da8fd382fc6a82584c\transformed\jetified-camera-lifecycle-1.2.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\14db308c6417231c0c3d58fa46d38064\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\14db308c6417231c0c3d58fa46d38064\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1235f37b232d25913db15dc3cf2502d1\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1235f37b232d25913db15dc3cf2502d1\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe349995b136e85f6a005f5d89fa32f9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe349995b136e85f6a005f5d89fa32f9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ece72575c0e3a0c9796d85af5708f2d2\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ece72575c0e3a0c9796d85af5708f2d2\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6ecc5ab7198e78d415df13f119ae1fd\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6ecc5ab7198e78d415df13f119ae1fd\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad4577fe8a010038de8762b6815943f3\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad4577fe8a010038de8762b6815943f3\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52cdc81732f911f8eab31db215be4e67\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\52cdc81732f911f8eab31db215be4e67\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b56ec4989056985e0fc55b312a352c65\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b56ec4989056985e0fc55b312a352c65\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8816de40106596fc9a016cd8eebb8eae\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8816de40106596fc9a016cd8eebb8eae\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\574a51baaed8611751e256cb469fde66\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\574a51baaed8611751e256cb469fde66\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ae3f07561c5228f22a7d470c7685769\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ae3f07561c5228f22a7d470c7685769\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\db03cf5cf2ca6b085efb9013b689807c\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\db03cf5cf2ca6b085efb9013b689807c\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e679ca1755443ba159bad16970bae55\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7e679ca1755443ba159bad16970bae55\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\125478a017f02376aaf907258ea43015\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\125478a017f02376aaf907258ea43015\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e511441ebcdbb52494bd61e876808728\transformed\jetified-core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e511441ebcdbb52494bd61e876808728\transformed\jetified-core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5db4685bf6ccf83c8280681ab238ef\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5db4685bf6ccf83c8280681ab238ef\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\402ab57b0e7ba6f5089b05fcb3c0ea47\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\402ab57b0e7ba6f5089b05fcb3c0ea47\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3fe071448913bb1730b603dba651790\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3fe071448913bb1730b603dba651790\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba70780b2769bb18a5f53897935adda0\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba70780b2769bb18a5f53897935adda0\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\adb6c888e1928c051191f75518118095\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\adb6c888e1928c051191f75518118095\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\82df6f32405bc3ccfe53fdcfe1ddea3b\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\82df6f32405bc3ccfe53fdcfe1ddea3b\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed0cb4ef6b1722f5f4c814ba33951cff\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed0cb4ef6b1722f5f4c814ba33951cff\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\70aee03866c8d8d27e2862c7da29844e\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\70aee03866c8d8d27e2862c7da29844e\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c89db88e6ae2827a86b641e6bb26d8d1\transformed\jetified-lifecycle-livedata-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c89db88e6ae2827a86b641e6bb26d8d1\transformed\jetified-lifecycle-livedata-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3ebb97f7886adc68cb1b96e69a304aa\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3ebb97f7886adc68cb1b96e69a304aa\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b9c5a6e42df1b61f13f380ee190613b\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4b9c5a6e42df1b61f13f380ee190613b\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.13\transforms\655c0f29bd4e7daa35521825d54b2b0b\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.13\transforms\655c0f29bd4e7daa35521825d54b2b0b\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff5b21ba38e36a4569cbfeacc97468f9\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff5b21ba38e36a4569cbfeacc97468f9\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6cbba339ad84aadcb7c88ebb4e3df614\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6cbba339ad84aadcb7c88ebb4e3df614\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1905fa04720f9aded41c2c7266b432f5\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1905fa04720f9aded41c2c7266b432f5\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45f0d462065695589efc5ad5bfd8c56f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\45f0d462065695589efc5ad5bfd8c56f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacbe320dbe79b1db3462fa879db08f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ceacbe320dbe79b1db3462fa879db08f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7231db8ad7ab98c57915fe35d469d361\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7231db8ad7ab98c57915fe35d469d361\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd40cf52013e79f216188201b79d1d5b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd40cf52013e79f216188201b79d1d5b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef966fdc63c2cf5a4c000e6db6f28be6\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef966fdc63c2cf5a4c000e6db6f28be6\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf327a7e21d5807061f1d7629f8a7744\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf327a7e21d5807061f1d7629f8a7744\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\02815fae27931ae44da7fab74be7eac3\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\02815fae27931ae44da7fab74be7eac3\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61fa31cda83801bd82678badafb23161\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61fa31cda83801bd82678badafb23161\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8897813398ed3770443f80a5810a48fc\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8897813398ed3770443f80a5810a48fc\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c710f4b0e8bd721889621d641ebb86\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\65c710f4b0e8bd721889621d641ebb86\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\894a783dca4aa815eb545af54b4b48b6\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\14db308c6417231c0c3d58fa46d38064\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\14db308c6417231c0c3d58fa46d38064\transformed\jetified-camera-core-1.2.3\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\894a783dca4aa815eb545af54b4b48b6\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\894a783dca4aa815eb545af54b4b48b6\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\894a783dca4aa815eb545af54b4b48b6\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\894a783dca4aa815eb545af54b4b48b6\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\894a783dca4aa815eb545af54b4b48b6\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\894a783dca4aa815eb545af54b4b48b6\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\894a783dca4aa815eb545af54b4b48b6\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\894a783dca4aa815eb545af54b4b48b6\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:31:17-103
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe349995b136e85f6a005f5d89fa32f9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ece72575c0e3a0c9796d85af5708f2d2\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ece72575c0e3a0c9796d85af5708f2d2\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd40cf52013e79f216188201b79d1d5b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd40cf52013e79f216188201b79d1d5b\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe349995b136e85f6a005f5d89fa32f9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe349995b136e85f6a005f5d89fa32f9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe349995b136e85f6a005f5d89fa32f9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe349995b136e85f6a005f5d89fa32f9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe349995b136e85f6a005f5d89fa32f9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe349995b136e85f6a005f5d89fa32f9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe349995b136e85f6a005f5d89fa32f9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ece72575c0e3a0c9796d85af5708f2d2\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ece72575c0e3a0c9796d85af5708f2d2\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ece72575c0e3a0c9796d85af5708f2d2\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
permission#com.autoflow.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
uses-permission#com.autoflow.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92

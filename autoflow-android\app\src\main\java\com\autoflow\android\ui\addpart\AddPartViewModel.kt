package com.autoflow.android.ui.addpart

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.autoflow.android.core.auth.AuthManager
import com.autoflow.android.data.api.ApiResult
import com.autoflow.android.data.api.CheckCompatibilityRequest
import com.autoflow.android.data.repositories.AddPartRepository
import com.autoflow.android.domain.models.*
import java.io.File
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * Main ViewModel for Add Part flow - orchestrates the complete wizard Mirrors web app's form state
 * management and validation
 */
class AddPartViewModel(
        private val repository: AddPartRepository,
        private val authManager: AuthManager
) : ViewModel() {

    // Main form state - mirrors web PartFormValues
    private val _formState = MutableStateFlow(PartFormValues())
    val formState: StateFlow<PartFormValues> = _formState.asStateFlow()

    // UI state
    private val _uiState = MutableStateFlow(AddPartUiState())
    val uiState: StateFlow<AddPartUiState> = _uiState.asStateFlow()

    // Categories and attributes
    private val _categories = MutableStateFlow<List<CategoryResponse>>(emptyList())
    val categories: StateFlow<List<CategoryResponse>> = _categories.asStateFlow()

    private val _categoryAttributes = MutableStateFlow<List<CategoryAttribute>>(emptyList())
    val categoryAttributes: StateFlow<List<CategoryAttribute>> = _categoryAttributes.asStateFlow()

    // Vehicle selection cascade
    private val _brands = MutableStateFlow<List<CarBrand>>(emptyList())
    val brands: StateFlow<List<CarBrand>> = _brands.asStateFlow()

    private val _models = MutableStateFlow<List<CarModel>>(emptyList())
    val models: StateFlow<List<CarModel>> = _models.asStateFlow()

    private val _generations = MutableStateFlow<List<CarGeneration>>(emptyList())
    val generations: StateFlow<List<CarGeneration>> = _generations.asStateFlow()

    private val _variations = MutableStateFlow<List<CarVariation>>(emptyList())
    val variations: StateFlow<List<CarVariation>> = _variations.asStateFlow()

    private val _trims = MutableStateFlow<List<CarTrim>>(emptyList())
    val trims: StateFlow<List<CarTrim>> = _trims.asStateFlow()

    // Compatibility check state
    private val _compatibilityState = MutableStateFlow(CompatibilityCheckState())
    val compatibilityState: StateFlow<CompatibilityCheckState> = _compatibilityState.asStateFlow()

    init {
        // Initialize with current user
        authManager.getCurrentUser()?.let { user ->
            updateFormState { it.copy(userId = user.id.toString()) }
        }

        // Load initial data
        loadCategories()
        loadBrands()
    }

    // ========== STEP 1: IMAGES ==========

    fun addImage(imageFile: File) {
        // Add a temporary item to show per-image uploading overlay
        val tempUrl = imageFile.absolutePath
        val makeMain = _formState.value.images.isEmpty()
        val tempImage = PartImage(url = tempUrl, isMain = makeMain, isUploading = true)
        updateFormState { it.copy(images = it.images + tempImage) }

        updateUiState { it.copy(isUploadingImage = true) }

        viewModelScope.launch {
            when (val result = repository.uploadImage(imageFile)) {
                is ApiResult.Success -> {
                    // Replace temp item with the uploaded URL
                    updateFormState { currentState ->
                        val replaced =
                                currentState.images.map { img ->
                                    if (img.url == tempUrl) {
                                        img.copy(url = result.data.imageUrl, isUploading = false)
                                    } else img
                                }
                        currentState.copy(images = replaced)
                    }
                    updateUiState { it.copy(isUploadingImage = false) }
                }
                is ApiResult.Error -> {
                    // Remove the temp item and show error
                    updateFormState { currentState ->
                        currentState.copy(
                                images = currentState.images.filterNot { it.url == tempUrl }
                        )
                    }
                    updateUiState {
                        it.copy(
                                isUploadingImage = false,
                                error = "Failed to upload image: ${'$'}{result.message}"
                        )
                    }
                }
                is ApiResult.Loading -> {
                    // Already handled above
                }
            }
        }
    }

    fun removeImage(imageUrl: String) {
        updateFormState { currentState ->
            val updatedImages = currentState.images.filter { it.url != imageUrl }

            // If we removed the main image, make the first remaining image main
            val imagesWithMain =
                    if (updatedImages.isNotEmpty() && updatedImages.none { it.isMain }) {
                        updatedImages.mapIndexed { index, image -> image.copy(isMain = index == 0) }
                    } else {
                        updatedImages
                    }

            currentState.copy(images = imagesWithMain)
        }
    }

    fun setMainImage(imageUrl: String) {
        updateFormState { currentState ->
            currentState.copy(
                    images =
                            currentState.images.map { image ->
                                image.copy(isMain = image.url == imageUrl)
                            }
            )
        }
    }

    fun canProceedFromImages(): Boolean {
        return _formState.value.images.isNotEmpty()
    }

    // ========== STEP 2: CATEGORY ==========

    private fun loadCategories() {
        viewModelScope.launch {
            updateUiState { it.copy(isLoadingCategories = true) }

            when (val result = repository.getCategories()) {
                is ApiResult.Success -> {
                    _categories.value = result.data.categories
                    updateUiState { it.copy(isLoadingCategories = false) }
                }
                is ApiResult.Error -> {
                    updateUiState {
                        it.copy(
                                isLoadingCategories = false,
                                error = "Failed to load categories: ${result.message}"
                        )
                    }
                }
                is ApiResult.Loading -> {
                    // Already handled above
                }
            }
        }
    }

    fun selectCategory(category: CategoryResponse) {
        updateFormState { currentState ->
            currentState.copy(
                    categoryId = category.id.toString(),
                    selectedCategory = category.label,
                    requirePartNumber = category.requiresPartNumber,
                    showVehicleSelection = !category.requiresPartNumber
            )
        }

        // Load attributes for this category
        loadCategoryAttributes(category.id)

        // Update current step based on category requirements
        val nextStep =
                if (category.requiresPartNumber) {
                    AddPartStep.PART_NUMBER
                } else {
                    AddPartStep.VEHICLE_SELECTION
                }

        updateFormState { it.copy(currentStep = nextStep) }
    }

    private fun loadCategoryAttributes(categoryId: Int) {
        viewModelScope.launch {
            when (val result = repository.getCategoryAttributes(categoryId)) {
                is ApiResult.Success -> {
                    _categoryAttributes.value = result.data.attributes
                }
                is ApiResult.Error -> {
                    updateUiState {
                        it.copy(error = "Failed to load category attributes: ${result.message}")
                    }
                }
                is ApiResult.Loading -> {
                    // Handle loading state if needed
                }
            }
        }
    }

    // ========== STEP 3A: PART NUMBER (PN-required categories) ==========

    fun updatePartNumber(partNumber: String) {
        updateFormState { it.copy(partNumber = partNumber) }
    }

    fun checkPartNumberCompatibility() {
        val currentState = _formState.value
        if (currentState.partNumber.isBlank() || currentState.categoryId.isBlank()) {
            return
        }

        updateFormState { it.copy(isCheckingPartNumber = true) }
        updateCompatibilityState { it.copy(isChecking = true, messages = emptyList()) }

        viewModelScope.launch {
            val request =
                    CheckCompatibilityRequest(
                            partNumber = currentState.partNumber,
                            categoryId = currentState.categoryId.toInt()
                    )

            when (val result = repository.checkCompatibility(request)) {
                is ApiResult.Success -> {
                    val compatibilityData =
                            CompatibilityData(
                                    partName = result.data.partName,
                                    partNumber = currentState.partNumber,
                                    compatiblePartNumbers = result.data.compatiblePartNumbers,
                                    engineCompatibility = result.data.engineCompatibility,
                                    vehicleCompatibility = result.data.vehicleCompatibility,
                                    partnumberGroup = result.data.partnumberGroup
                            )

                    updateFormState {
                        it.copy(isCheckingPartNumber = false, compatibilityData = compatibilityData)
                    }

                    updateCompatibilityState {
                        it.copy(
                                isChecking = false,
                                isComplete = true,
                                messages = listOf("Compatibility check completed successfully")
                        )
                    }
                }
                is ApiResult.Error -> {
                    updateFormState { it.copy(isCheckingPartNumber = false) }
                    updateCompatibilityState {
                        it.copy(
                                isChecking = false,
                                error = "Compatibility check failed: ${result.message}"
                        )
                    }
                }
                is ApiResult.Loading -> {
                    // Already handled above
                }
            }
        }
    }

    fun addEngineCode(engineCode: String) {
        if (engineCode.isNotBlank()) {
            updateFormState { currentState ->
                currentState.copy(
                        additionalEngineCodes = currentState.additionalEngineCodes + engineCode
                )
            }
        }
    }

    fun removeEngineCode(engineCode: String) {
        updateFormState { currentState ->
            currentState.copy(
                    additionalEngineCodes = currentState.additionalEngineCodes - engineCode
            )
        }
    }

    fun canProceedFromPartNumber(): Boolean {
        return _formState.value.compatibilityData != null
    }

    // ========== STEP 3B: VEHICLE SELECTION (non-PN categories) ==========

    private fun loadBrands() {
        viewModelScope.launch {
            when (val result = repository.getBrands()) {
                is ApiResult.Success -> {
                    _brands.value = result.data
                }
                is ApiResult.Error -> {
                    updateUiState { it.copy(error = "Failed to load brands: ${result.message}") }
                }
                is ApiResult.Loading -> {
                    // Handle loading state if needed
                }
            }
        }
    }

    fun selectBrand(brand: CarBrand) {
        updateFormState { it.copy(brandId = brand.id.toString()) }
        loadModels(brand.id)

        // Clear downstream selections
        _models.value = emptyList()
        _generations.value = emptyList()
        _variations.value = emptyList()
        _trims.value = emptyList()
    }

    private fun loadModels(brandId: Int) {
        viewModelScope.launch {
            when (val result = repository.getModels(brandId)) {
                is ApiResult.Success -> {
                    _models.value = result.data
                }
                is ApiResult.Error -> {
                    updateUiState { it.copy(error = "Failed to load models: ${result.message}") }
                }
                is ApiResult.Loading -> {
                    // Handle loading state if needed
                }
            }
        }
    }

    fun selectModel(model: CarModel) {
        updateFormState { it.copy(modelId = model.id.toString(), modelName = model.name) }
        loadGenerations(model.id)

        // Clear downstream selections
        _generations.value = emptyList()
        _variations.value = emptyList()
        _trims.value = emptyList()
    }

    private fun loadGenerations(modelId: Int) {
        viewModelScope.launch {
            when (val result = repository.getGenerations(modelId)) {
                is ApiResult.Success -> {
                    _generations.value = result.data
                }
                is ApiResult.Error -> {
                    updateUiState {
                        it.copy(error = "Failed to load generations: ${result.message}")
                    }
                }
                is ApiResult.Loading -> {
                    // Handle loading state if needed
                }
            }
        }
    }

    fun selectGeneration(generation: CarGeneration) {
        updateFormState {
            it.copy(
                    generationId = generation.id.toString(),
                    generationName = generation.name,
                    generationYears = generation.years
            )
        }
        loadVariations(generation.id)

        // Clear downstream selections
        _variations.value = emptyList()
        _trims.value = emptyList()
    }

    private fun loadVariations(generationId: Int) {
        viewModelScope.launch {
            when (val result = repository.getVariations(generationId)) {
                is ApiResult.Success -> {
                    _variations.value = result.data
                }
                is ApiResult.Error -> {
                    updateUiState {
                        it.copy(error = "Failed to load variations: ${result.message}")
                    }
                }
                is ApiResult.Loading -> {
                    // Handle loading state if needed
                }
            }
        }
    }

    fun selectVariation(variation: CarVariation) {
        updateFormState {
            it.copy(variationId = variation.id.toString(), variationName = variation.name)
        }
        loadTrims(variation.id)

        // Clear downstream selections
        _trims.value = emptyList()
    }

    private fun loadTrims(variationId: Int) {
        viewModelScope.launch {
            when (val result = repository.getTrims(variationId)) {
                is ApiResult.Success -> {
                    _trims.value = result.data
                }
                is ApiResult.Error -> {
                    updateUiState { it.copy(error = "Failed to load trims: ${result.message}") }
                }
                is ApiResult.Loading -> {
                    // Handle loading state if needed
                }
            }
        }
    }

    fun selectTrim(trim: CarTrim) {
        updateFormState { it.copy(trimId = trim.id.toString(), trimName = trim.name) }
    }

    fun canProceedFromVehicleSelection(): Boolean {
        return _formState.value.trimId.isNotBlank()
    }

    // ========== Helper functions ==========

    private fun updateFormState(update: (PartFormValues) -> PartFormValues) {
        _formState.value = update(_formState.value)
    }

    private fun updateUiState(update: (AddPartUiState) -> AddPartUiState) {
        _uiState.value = update(_uiState.value)
    }

    private fun updateCompatibilityState(
            update: (CompatibilityCheckState) -> CompatibilityCheckState
    ) {
        _compatibilityState.value = update(_compatibilityState.value)
    }

    fun clearError() {
        updateUiState { it.copy(error = null) }
    }

    // ========== Mutators from UI ==========

    fun updateCondition(condition: PartCondition) {
        updateFormState { it.copy(condition = condition) }
    }

    fun updatePricing(
            newStock: Int?,
            newPrice: Double?,
            newDiscountPrice: Double?,
            usedStock: Int?,
            usedPrice: Double?,
            usedDiscountPrice: Double?
    ) {
        updateFormState { state ->
            state.copy(
                    newStock = newStock,
                    newPrice = newPrice,
                    newDiscountPrice = newDiscountPrice,
                    usedStock = usedStock,
                    usedPrice = usedPrice,
                    usedDiscountPrice = usedDiscountPrice
            )
        }
    }

    fun setCategoryAttributeValues(values: List<CategoryAttributeValue>) {
        // Persist both the raw list (for review) and the simple map expected by API
        val attrsMap = values.associate { it.attributeId.toString() to it.value }
        updateFormState { it.copy(categoryAttributes = values, attributes = attrsMap) }
    }

    fun goToStep(step: AddPartStep) {
        updateFormState { it.copy(currentStep = step) }
    }

    fun goToNextStep() {
        val currentStep = _formState.value.currentStep
        val nextStep =
                when (currentStep) {
                    AddPartStep.IMAGES -> AddPartStep.CATEGORY
                    AddPartStep.CATEGORY -> {
                        if (_formState.value.requirePartNumber) {
                            AddPartStep.PART_NUMBER
                        } else {
                            AddPartStep.VEHICLE_SELECTION
                        }
                    }
                    AddPartStep.PART_NUMBER -> AddPartStep.ATTRIBUTES
                    AddPartStep.VEHICLE_SELECTION -> AddPartStep.ATTRIBUTES
                    AddPartStep.ATTRIBUTES -> AddPartStep.CONDITION_PRICING
                    AddPartStep.CONDITION_PRICING -> AddPartStep.SUBMIT
                    AddPartStep.SUBMIT -> AddPartStep.STORAGE_LOCATION
                    AddPartStep.STORAGE_LOCATION -> AddPartStep.IMAGES // Complete, back to start
                }

        updateFormState { it.copy(currentStep = nextStep) }
    }

    fun goToPreviousStep() {
        val currentStep = _formState.value.currentStep
        val previousStep =
                when (currentStep) {
                    AddPartStep.IMAGES -> AddPartStep.IMAGES // Can't go back from first step
                    AddPartStep.CATEGORY -> AddPartStep.IMAGES
                    AddPartStep.PART_NUMBER -> AddPartStep.CATEGORY
                    AddPartStep.VEHICLE_SELECTION -> AddPartStep.CATEGORY
                    AddPartStep.ATTRIBUTES -> {
                        if (_formState.value.requirePartNumber) {
                            AddPartStep.PART_NUMBER
                        } else {
                            AddPartStep.VEHICLE_SELECTION
                        }
                    }
                    AddPartStep.CONDITION_PRICING -> AddPartStep.ATTRIBUTES
                    AddPartStep.SUBMIT -> AddPartStep.CONDITION_PRICING
                    AddPartStep.STORAGE_LOCATION -> AddPartStep.SUBMIT
                }

        updateFormState { it.copy(currentStep = previousStep) }
    }

    // ========== SUBMIT ==========

    fun submitPart(onSuccess: ((Int, String) -> Unit)? = null) {
        val form = _formState.value
        // Basic validation: ensure images and category present
        if (form.images.isEmpty() || form.categoryId.isBlank()) {
            updateUiState { it.copy(error = "Missing required fields") }
            return
        }
        viewModelScope.launch {
            updateUiState { it.copy(isSubmitting = true, error = null) }
            when (val result = repository.createPartComplete(form)) {
                is ApiResult.Success -> {
                    updateUiState { it.copy(isSubmitting = false, submitSuccess = true) }
                    onSuccess?.invoke(result.data.partId, result.data.title)
                }
                is ApiResult.Error -> {
                    updateUiState {
                        it.copy(
                                isSubmitting = false,
                                error = "Failed to create part: ${'$'}{result.message}"
                        )
                    }
                }
                is ApiResult.Loading -> Unit
            }
        }
    }
}

/** UI state for Add Part flow */
data class AddPartUiState(
        val isLoadingCategories: Boolean = false,
        val isUploadingImage: Boolean = false,
        val error: String? = null,
        val isSubmitting: Boolean = false,
        val submitSuccess: Boolean = false
)

/** Compatibility check state */
data class CompatibilityCheckState(
        val isChecking: Boolean = false,
        val isComplete: Boolean = false,
        val messages: List<String> = emptyList(),
        val error: String? = null
)

{"logs": [{"outputFile": "com.autoflow.android.app-mergeDebugResources-45:/values-zu/values-zu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1c5f58891eb309bb4a04ac21b97fad24\\transformed\\core-1.10.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,257,356,459,565,672,785", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "148,252,351,454,560,667,780,881"}, "to": {"startLines": "38,39,40,41,42,43,44,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3421,3519,3623,3722,3825,3931,4038,10455", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "3514,3618,3717,3820,3926,4033,4146,10551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd2d40d61e01e6ef2b0f2f90a3e8aca5\\transformed\\material-1.9.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,349,426,503,597,685,797,923,1004,1071,1174,1249,1312,1404,1475,1540,1607,1679,1751,1805,1926,1985,2049,2103,2180,2312,2397,2478,2627,2714,2797,2889,2945,3003,3069,3141,3218,3309,3389,3468,3543,3622,3712,3785,3879,3976,4050,4123,4222,4277,4345,4433,4522,4584,4648,4711,4820,4925,5028,5137,5197,5259", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,77,76,76,93,87,111,125,80,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,80,148,86,82,91,55,57,65,71,76,90,79,78,74,78,89,72,93,96,73,72,98,54,67,87,88,61,63,62,108,104,102,108,59,61,81", "endOffsets": "266,344,421,498,592,680,792,918,999,1066,1169,1244,1307,1399,1470,1535,1602,1674,1746,1800,1921,1980,2044,2098,2175,2307,2392,2473,2622,2709,2792,2884,2940,2998,3064,3136,3213,3304,3384,3463,3538,3617,3707,3780,3874,3971,4045,4118,4217,4272,4340,4428,4517,4579,4643,4706,4815,4920,5023,5132,5192,5254,5336"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,50,51,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3085,3162,3239,3333,4151,4263,4389,4679,4746,6206,6281,6344,6436,6507,6572,6639,6711,6783,6837,6958,7017,7081,7135,7212,7344,7429,7510,7659,7746,7829,7921,7977,8035,8101,8173,8250,8341,8421,8500,8575,8654,8744,8817,8911,9008,9082,9155,9254,9309,9377,9465,9554,9616,9680,9743,9852,9957,10060,10169,10229,10291", "endLines": "5,33,34,35,36,37,45,46,47,50,51,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113", "endColumns": "12,77,76,76,93,87,111,125,80,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,80,148,86,82,91,55,57,65,71,76,90,79,78,74,78,89,72,93,96,73,72,98,54,67,87,88,61,63,62,108,104,102,108,59,61,81", "endOffsets": "316,3080,3157,3234,3328,3416,4258,4384,4465,4741,4844,6276,6339,6431,6502,6567,6634,6706,6778,6832,6953,7012,7076,7130,7207,7339,7424,7505,7654,7741,7824,7916,7972,8030,8096,8168,8245,8336,8416,8495,8570,8649,8739,8812,8906,9003,9077,9150,9249,9304,9372,9460,9549,9611,9675,9738,9847,9952,10055,10164,10224,10286,10368"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\303aa64de72ab8a914eac82254eebb45\\transformed\\appcompat-1.6.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,432,520,623,738,817,894,985,1078,1173,1267,1367,1460,1555,1649,1740,1833,1914,2018,2121,2219,2326,2433,2538,2695,2791", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "208,315,427,515,618,733,812,889,980,1073,1168,1262,1362,1455,1550,1644,1735,1828,1909,2013,2116,2214,2321,2428,2533,2690,2786,2868"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,536,648,736,839,954,1033,1110,1201,1294,1389,1483,1583,1676,1771,1865,1956,2049,2130,2234,2337,2435,2542,2649,2754,2911,10373", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "424,531,643,731,834,949,1028,1105,1196,1289,1384,1478,1578,1671,1766,1860,1951,2044,2125,2229,2332,2430,2537,2644,2749,2906,3002,10450"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ed7078e8e71893b82f32857a328dfc89\\transformed\\biometric-1.1.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,264,387,543,680,833,965,1109,1203,1353,1499", "endColumns": "113,94,122,155,136,152,131,143,93,149,145,121", "endOffsets": "164,259,382,538,675,828,960,1104,1198,1348,1494,1616"}, "to": {"startLines": "48,49,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4470,4584,4849,4972,5128,5265,5418,5550,5694,5788,5938,6084", "endColumns": "113,94,122,155,136,152,131,143,93,149,145,121", "endOffsets": "4579,4674,4967,5123,5260,5413,5545,5689,5783,5933,6079,6201"}}]}]}
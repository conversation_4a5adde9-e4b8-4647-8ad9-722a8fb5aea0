package com.autoflow.android.ui.addpart.fragments

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.autoflow.android.domain.models.EngineCompatibility
import com.autoflow.android.domain.models.VehicleCompatibility
import com.autoflow.android.ui.addpart.AddPartViewModel
import kotlinx.coroutines.launch

/**
 * Part Number Fragment - Step 3A of Add Part flow (for PN-required categories)
 * Handles part number input and AI compatibility analysis
 * Mirrors web app's compatibility check flow
 */
class PartNumberFragment : Fragment() {
    
    companion object {
        private const val TAG = "PartNumberFragment"
    }
    
    private val viewModel: AddPartViewModel by activityViewModels()
    
    // UI Components
    private lateinit var instructionText: TextView
    private lateinit var partNumberInput: EditText
    private lateinit var checkCompatibilityButton: Button
    private lateinit var compatibilitySection: LinearLayout
    private lateinit var statusText: TextView
    private lateinit var progressBar: ProgressBar
    private lateinit var resultsContainer: LinearLayout
    
    // Results sections
    private lateinit var partNameText: TextView
    private lateinit var compatiblePartsSection: LinearLayout
    private lateinit var compatiblePartsRecyclerView: RecyclerView
    private lateinit var enginesSection: LinearLayout
    private lateinit var enginesRecyclerView: RecyclerView
    private lateinit var vehiclesSection: LinearLayout
    private lateinit var vehiclesRecyclerView: RecyclerView
    private lateinit var additionalEnginesSection: LinearLayout
    private lateinit var additionalEnginesInput: EditText
    private lateinit var addEngineButton: Button
    private lateinit var additionalEnginesRecyclerView: RecyclerView
    
    // Adapters
    private lateinit var compatiblePartsAdapter: SimpleListAdapter
    private lateinit var enginesAdapter: EnginesAdapter
    private lateinit var vehiclesAdapter: VehiclesAdapter
    private lateinit var additionalEnginesAdapter: SimpleListAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return createLayout()
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupRecyclerViews()
        setupClickListeners()
        observeViewModel()
    }
    
    private fun createLayout(): ScrollView {
        val scrollView = ScrollView(requireContext())
        
        val mainLayout = LinearLayout(requireContext()).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(16, 16, 16, 16)
        }
        
        // Instructions
        instructionText = TextView(requireContext()).apply {
            text = "Enter the part number to check compatibility and find compatible vehicles"
            textSize = 16f
            setPadding(0, 0, 0, 16)
        }
        
        // Part number input section
        val inputSection = LinearLayout(requireContext()).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(0, 0, 0, 16)
        }
        
        val partNumberLabel = TextView(requireContext()).apply {
            text = "Part Number"
            textSize = 16f
            setTypeface(null, android.graphics.Typeface.BOLD)
            setPadding(0, 0, 0, 8)
        }
        
        partNumberInput = EditText(requireContext()).apply {
            hint = "Enter part number (e.g., 1K0129620D)"
            setPadding(16, 16, 16, 16)
            addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) {
                    viewModel.updatePartNumber(s.toString())
                    updateCheckButtonState()
                }
            })
        }
        
        checkCompatibilityButton = Button(requireContext()).apply {
            text = "Check Compatibility"
            isEnabled = false
            setOnClickListener { viewModel.checkPartNumberCompatibility() }
        }
        
        inputSection.addView(partNumberLabel)
        inputSection.addView(partNumberInput)
        inputSection.addView(checkCompatibilityButton)
        
        // Compatibility results section
        compatibilitySection = LinearLayout(requireContext()).apply {
            orientation = LinearLayout.VERTICAL
            visibility = View.GONE
        }
        
        // Status and progress
        statusText = TextView(requireContext()).apply {
            text = ""
            textSize = 14f
            setPadding(0, 16, 0, 8)
        }
        
        progressBar = ProgressBar(requireContext()).apply {
            visibility = View.GONE
        }
        
        // Results container
        resultsContainer = LinearLayout(requireContext()).apply {
            orientation = LinearLayout.VERTICAL
            visibility = View.GONE
        }
        
        // Part name
        partNameText = TextView(requireContext()).apply {
            textSize = 18f
            setTypeface(null, android.graphics.Typeface.BOLD)
            setPadding(0, 16, 0, 16)
        }
        
        // Compatible parts section
        compatiblePartsSection = createResultSection(
            "Compatible Part Numbers",
            "These part numbers are compatible with your part:"
        )
        compatiblePartsRecyclerView = RecyclerView(requireContext()).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                200
            )
        }
        compatiblePartsSection.addView(compatiblePartsRecyclerView)
        
        // Engines section
        enginesSection = createResultSection(
            "Compatible Engines",
            "This part is compatible with these engines:"
        )
        enginesRecyclerView = RecyclerView(requireContext()).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                200
            )
        }
        enginesSection.addView(enginesRecyclerView)
        
        // Vehicles section
        vehiclesSection = createResultSection(
            "Compatible Vehicles",
            "This part fits these vehicles:"
        )
        vehiclesRecyclerView = RecyclerView(requireContext()).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                300
            )
        }
        vehiclesSection.addView(vehiclesRecyclerView)
        
        // Additional engines section
        additionalEnginesSection = createAdditionalEnginesSection()
        
        // Add all sections to results container
        resultsContainer.addView(partNameText)
        resultsContainer.addView(compatiblePartsSection)
        resultsContainer.addView(enginesSection)
        resultsContainer.addView(vehiclesSection)
        resultsContainer.addView(additionalEnginesSection)
        
        compatibilitySection.addView(statusText)
        compatibilitySection.addView(progressBar)
        compatibilitySection.addView(resultsContainer)
        
        // Add all sections to main layout
        mainLayout.addView(instructionText)
        mainLayout.addView(inputSection)
        mainLayout.addView(compatibilitySection)
        
        scrollView.addView(mainLayout)
        return scrollView
    }
    
    private fun createResultSection(title: String, description: String): LinearLayout {
        return LinearLayout(requireContext()).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(0, 16, 0, 16)
            
            val titleText = TextView(context).apply {
                text = title
                textSize = 16f
                setTypeface(null, android.graphics.Typeface.BOLD)
                setPadding(0, 0, 0, 8)
            }
            
            val descText = TextView(context).apply {
                text = description
                textSize = 14f
                setPadding(0, 0, 0, 8)
            }
            
            addView(titleText)
            addView(descText)
        }
    }
    
    private fun createAdditionalEnginesSection(): LinearLayout {
        return LinearLayout(requireContext()).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(0, 16, 0, 16)
            
            val titleText = TextView(context).apply {
                text = "Additional Engine Codes"
                textSize = 16f
                setTypeface(null, android.graphics.Typeface.BOLD)
                setPadding(0, 0, 0, 8)
            }
            
            val descText = TextView(context).apply {
                text = "Add any missing engine codes that you know are compatible:"
                textSize = 14f
                setPadding(0, 0, 0, 8)
            }
            
            val inputLayout = LinearLayout(context).apply {
                orientation = LinearLayout.HORIZONTAL
            }
            
            additionalEnginesInput = EditText(context).apply {
                hint = "Engine code (e.g., CAXA)"
                layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
            }
            
            addEngineButton = Button(context).apply {
                text = "Add"
                setOnClickListener { addEngineCode() }
            }
            
            inputLayout.addView(additionalEnginesInput)
            inputLayout.addView(addEngineButton)
            
            additionalEnginesRecyclerView = RecyclerView(context).apply {
                layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    150
                )
            }
            
            addView(titleText)
            addView(descText)
            addView(inputLayout)
            addView(additionalEnginesRecyclerView)
        }
    }
    
    private fun setupRecyclerViews() {
        compatiblePartsAdapter = SimpleListAdapter()
        compatiblePartsRecyclerView.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = compatiblePartsAdapter
        }
        
        enginesAdapter = EnginesAdapter()
        enginesRecyclerView.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = enginesAdapter
        }
        
        vehiclesAdapter = VehiclesAdapter()
        vehiclesRecyclerView.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = vehiclesAdapter
        }
        
        additionalEnginesAdapter = SimpleListAdapter { engineCode ->
            viewModel.removeEngineCode(engineCode)
        }
        additionalEnginesRecyclerView.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = additionalEnginesAdapter
        }
    }
    
    private fun setupClickListeners() {
        // Click listeners are set in createLayout()
    }
    
    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.formState.collect { formState ->
                partNumberInput.setText(formState.partNumber)
                updateCheckButtonState()
                
                // Update additional engines list
                additionalEnginesAdapter.updateItems(formState.additionalEngineCodes)
            }
        }
        
        lifecycleScope.launch {
            viewModel.compatibilityState.collect { compatibilityState ->
                when {
                    compatibilityState.isChecking -> {
                        showProgress("Checking compatibility...")
                        compatibilitySection.visibility = View.VISIBLE
                        resultsContainer.visibility = View.GONE
                    }
                    compatibilityState.isComplete -> {
                        hideProgress()
                        showResults()
                    }
                    compatibilityState.error != null -> {
                        hideProgress()
                        showError(compatibilityState.error)
                    }
                }
                
                // Update status messages
                if (compatibilityState.messages.isNotEmpty()) {
                    statusText.text = compatibilityState.messages.last()
                }
            }
        }
        
        lifecycleScope.launch {
            viewModel.formState.collect { formState ->
                formState.compatibilityData?.let { data ->
                    displayCompatibilityResults(data)
                }
            }
        }
    }
    
    private fun updateCheckButtonState() {
        val partNumber = partNumberInput.text.toString().trim()
        checkCompatibilityButton.isEnabled = partNumber.isNotBlank()
    }
    
    private fun addEngineCode() {
        val engineCode = additionalEnginesInput.text.toString().trim()
        if (engineCode.isNotBlank()) {
            viewModel.addEngineCode(engineCode)
            additionalEnginesInput.text.clear()
        }
    }
    
    private fun showProgress(message: String) {
        statusText.text = message
        progressBar.visibility = View.VISIBLE
    }
    
    private fun hideProgress() {
        progressBar.visibility = View.GONE
    }
    
    private fun showResults() {
        statusText.text = "Compatibility check completed"
        resultsContainer.visibility = View.VISIBLE
    }
    
    private fun showError(error: String) {
        statusText.text = "Error: $error"
        resultsContainer.visibility = View.GONE
    }
    
    private fun displayCompatibilityResults(data: com.autoflow.android.domain.models.CompatibilityData) {
        // Part name
        partNameText.text = data.partName ?: "Unknown Part"
        
        // Compatible parts
        compatiblePartsAdapter.updateItems(data.compatiblePartNumbers)
        
        // Engines
        enginesAdapter.updateEngines(data.engineCompatibility)
        
        // Vehicles
        vehiclesAdapter.updateVehicles(data.vehicleCompatibility)
    }
}

// Simple adapter for string lists
class SimpleListAdapter(
    private val onItemClick: ((String) -> Unit)? = null
) : RecyclerView.Adapter<SimpleListAdapter.ViewHolder>() {
    
    private var items = listOf<String>()
    
    fun updateItems(newItems: List<String>) {
        items = newItems
        notifyDataSetChanged()
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val textView = TextView(parent.context).apply {
            setPadding(16, 8, 16, 8)
            textSize = 14f
            setBackgroundResource(android.R.drawable.list_selector_background)
        }
        return ViewHolder(textView)
    }
    
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(items[position], onItemClick)
    }
    
    override fun getItemCount(): Int = items.size
    
    class ViewHolder(private val textView: TextView) : RecyclerView.ViewHolder(textView) {
        fun bind(item: String, onItemClick: ((String) -> Unit)?) {
            textView.text = item
            onItemClick?.let { callback ->
                textView.setOnClickListener { callback(item) }
            }
        }
    }
}

// Adapter for engines
class EnginesAdapter : RecyclerView.Adapter<EnginesAdapter.ViewHolder>() {
    
    private var engines = listOf<EngineCompatibility>()
    
    fun updateEngines(newEngines: List<EngineCompatibility>) {
        engines = newEngines
        notifyDataSetChanged()
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val layout = LinearLayout(parent.context).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(16, 8, 16, 8)
            setBackgroundResource(android.R.drawable.list_selector_background)
        }
        return ViewHolder(layout)
    }
    
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(engines[position])
    }
    
    override fun getItemCount(): Int = engines.size
    
    class ViewHolder(private val layout: LinearLayout) : RecyclerView.ViewHolder(layout) {
        fun bind(engine: EngineCompatibility) {
            layout.removeAllViews()
            
            val codeText = TextView(layout.context).apply {
                text = engine.engineCode
                textSize = 16f
                setTypeface(null, android.graphics.Typeface.BOLD)
            }
            
            val detailsText = TextView(layout.context).apply {
                text = "${engine.engineCapacity} ${engine.fuelType} ${engine.engineType}"
                textSize = 14f
            }
            
            layout.addView(codeText)
            layout.addView(detailsText)
        }
    }
}

// Adapter for vehicles
class VehiclesAdapter : RecyclerView.Adapter<VehiclesAdapter.ViewHolder>() {
    
    private var vehicles = listOf<VehicleCompatibility>()
    
    fun updateVehicles(newVehicles: List<VehicleCompatibility>) {
        vehicles = newVehicles
        notifyDataSetChanged()
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val textView = TextView(parent.context).apply {
            setPadding(16, 8, 16, 8)
            textSize = 14f
            setBackgroundResource(android.R.drawable.list_selector_background)
        }
        return ViewHolder(textView)
    }
    
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(vehicles[position])
    }
    
    override fun getItemCount(): Int = vehicles.size
    
    class ViewHolder(private val textView: TextView) : RecyclerView.ViewHolder(textView) {
        fun bind(vehicle: VehicleCompatibility) {
            textView.text = "${vehicle.brand} ${vehicle.model} ${vehicle.generation} ${vehicle.variation}"
        }
    }
}

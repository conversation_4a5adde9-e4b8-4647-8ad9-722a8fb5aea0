<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="@drawable/login_background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="24dp">

        <!-- Decorative circles -->
        <View
            android:id="@+id/circle1"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginTop="60dp"
            android:background="@drawable/circle_light_purple"
            android:alpha="0.3"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/circle2"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginStart="40dp"
            android:layout_marginTop="20dp"
            android:background="@drawable/circle_light_purple"
            android:alpha="0.2"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/circle1" />

        <View
            android:id="@+id/circle3"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_marginTop="40dp"
            android:layout_marginEnd="60dp"
            android:background="@drawable/circle_light_purple"
            android:alpha="0.15"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/circle1" />

        <!-- User icon -->
        <ImageView
            android:id="@+id/userIcon"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginTop="40dp"
            android:background="@drawable/circle_purple"
            android:padding="20dp"
            android:src="@drawable/ic_person"
            android:tint="@android:color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/circle1" />

        <!-- Title -->
        <TextView
            android:id="@+id/titleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:text="Login to continue"
            android:textColor="@color/text_primary"
            android:textSize="24sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/userIcon" />

        <!-- Email Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/emailInputLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="48dp"
            android:hint="Email"
            app:endIconDrawable="@drawable/ic_email"
            app:endIconMode="custom"
            app:endIconTint="@color/purple_500"
            app:hintTextColor="@color/text_secondary"
            app:boxStrokeColor="@color/purple_500"
            app:boxStrokeWidth="2dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/titleText">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/emailEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textEmailAddress"
                android:textColor="@color/text_primary"
                android:textSize="16sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Password Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/passwordInputLayout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:hint="Password"
            app:endIconMode="password_toggle"
            app:endIconTint="@color/purple_500"
            app:hintTextColor="@color/text_secondary"
            app:boxStrokeColor="@color/purple_500"
            app:boxStrokeWidth="2dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/emailInputLayout">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/passwordEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textPassword"
                android:textColor="@color/text_primary"
                android:textSize="16sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Login Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/loginButton"
            style="@style/LoginButton"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_marginTop="32dp"
            android:text="LOGIN"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/passwordInputLayout" />

        <!-- Forgot Password -->
        <TextView
            android:id="@+id/forgotPasswordText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="Forgot password?"
            android:textColor="@color/purple_500"
            android:textSize="14sp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/loginButton" />

        <!-- Loading Progress -->
        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/loadingProgress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:indicatorColor="@color/purple_500"
            app:trackColor="@color/purple_100"
            app:layout_constraintBottom_toBottomOf="@id/loginButton"
            app:layout_constraintEnd_toEndOf="@id/loginButton"
            app:layout_constraintStart_toStartOf="@id/loginButton"
            app:layout_constraintTop_toTopOf="@id/loginButton" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>

package com.autoflow.android.ui.parts

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import com.autoflow.android.data.api.PartsApiService
import com.autoflow.android.data.repository.AuthRepository

class SimpleCategorySelectionActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "SimpleCategorySelection"
    }

    private lateinit var authRepository: AuthRepository
    private lateinit var partsApiService: PartsApiService

    // UI Elements - created programmatically
    private lateinit var imageView: ImageView
    private lateinit var categorySpinner: Spinner
    private lateinit var statusText: TextView
    private lateinit var continueButton: Button
    private lateinit var backButton: Button

    private var croppedImageUri: Uri? = null
    private var selectedCategory: Category? = null
    private val categories = mutableListOf<Category>()

    data class Category(
            val id: Int,
            val name: String,
            val description: String? = null,
            val requiresPartNumber: Boolean = false,
            val hasAttributes: Boolean = false,
            val parentId: Int? = null,
            val level: Int = 0,
            val children: MutableList<Category> = mutableListOf()
    ) {
        val isLeaf: Boolean
            get() = children.isEmpty()
        val hasChildren: Boolean
            get() = children.isNotEmpty()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        authRepository = AuthRepository(this)
        partsApiService = PartsApiService(this)

        createUIProgrammatically()
        loadCroppedImage()
        loadCategories()
    }

    private fun createUIProgrammatically() {
        Log.d(TAG, "Creating UI programmatically to bypass R class issues")

        // Create main layout
        val mainLayout =
                LinearLayout(this).apply {
                    orientation = LinearLayout.VERTICAL
                    setPadding(32, 32, 32, 32)
                }

        // Create header
        val headerLayout = LinearLayout(this).apply { orientation = LinearLayout.HORIZONTAL }

        backButton =
                Button(this).apply {
                    text = "Back"
                    setOnClickListener { finish() }
                }

        val titleText =
                TextView(this).apply {
                    text = "Select Category"
                    textSize = 20f
                    layoutParams =
                            LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
                }

        continueButton =
                Button(this).apply {
                    text = "Continue"
                    isEnabled = false
                    setOnClickListener { proceedToNextStep() }
                }

        headerLayout.addView(backButton)
        headerLayout.addView(titleText)
        headerLayout.addView(continueButton)

        // Create status text
        statusText =
                TextView(this).apply {
                    text = "Loading categories..."
                    textSize = 14f
                    setPadding(0, 16, 0, 16)
                }

        // Create image view
        imageView =
                ImageView(this).apply {
                    layoutParams = LinearLayout.LayoutParams(200, 200)
                    scaleType = ImageView.ScaleType.CENTER_CROP
                }

        // Create category spinner
        val categoryLabel =
                TextView(this).apply {
                    text = "Category"
                    textSize = 16f
                    setPadding(0, 24, 0, 8)
                }

        categorySpinner =
                Spinner(this).apply {
                    layoutParams =
                            LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, 120)
                    setPadding(16, 16, 16, 16)
                }

        // Setup spinner listener
        categorySpinner.onItemSelectedListener =
                object : AdapterView.OnItemSelectedListener {
                    override fun onItemSelected(
                            parent: AdapterView<*>?,
                            view: View?,
                            position: Int,
                            id: Long
                    ) {
                        if (position > 0 && categories.isNotEmpty()) {
                            selectedCategory = categories[position - 1]
                            continueButton.isEnabled = true
                            statusText.text = "Selected: ${selectedCategory!!.name}"
                        } else {
                            selectedCategory = null
                            continueButton.isEnabled = false
                            statusText.text = "Select a category for your part"
                        }
                    }

                    override fun onNothingSelected(parent: AdapterView<*>?) {
                        selectedCategory = null
                        continueButton.isEnabled = false
                    }
                }

        // Add all views to main layout
        mainLayout.addView(headerLayout)
        mainLayout.addView(statusText)
        mainLayout.addView(imageView)
        mainLayout.addView(categoryLabel)
        mainLayout.addView(categorySpinner)

        // Set as content view
        setContentView(mainLayout)

        Log.d(TAG, "UI created successfully")
    }

    private fun loadCroppedImage() {
        val imageUriString = intent.getStringExtra("cropped_image_uri")
        if (imageUriString != null) {
            croppedImageUri = Uri.parse(imageUriString)
            imageView.setImageURI(croppedImageUri)
            Log.d(TAG, "Image loaded successfully")
        } else {
            Toast.makeText(this, "Error loading image", Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    private fun loadCategories() {
        Log.d(TAG, "Loading categories...")

        // Load default categories immediately
        categories.clear()
        categories.addAll(getDefaultCategories())
        setupSpinnerAdapter()
        statusText.text = "Select a category for your part"

        Log.d(TAG, "Default categories loaded: ${categories.size}")
    }

    private fun setupSpinnerAdapter() {
        Log.d(TAG, "Setting up spinner adapter with ${categories.size} categories")

        val categoryNames = mutableListOf("Select a category")
        categoryNames.addAll(categories.map { it.name })

        val adapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, categoryNames)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)

        categorySpinner.adapter = adapter

        Log.d(TAG, "Spinner adapter set with items: $categoryNames")
    }

    private fun getDefaultCategories(): List<Category> {
        return listOf(
                Category(1, "Engine Parts", "Engine components and accessories", true, true),
                Category(2, "Body Parts", "Exterior and interior body components", false, true),
                Category(3, "Electrical Parts", "Electrical components and wiring", true, true),
                Category(4, "Suspension Parts", "Suspension and steering components", true, true),
                Category(5, "Brake Parts", "Brake system components", true, true),
                Category(6, "Transmission Parts", "Transmission and drivetrain", true, true),
                Category(7, "Interior Parts", "Interior components and accessories", false, false),
                Category(8, "Exhaust Parts", "Exhaust system components", false, true)
        )
    }

    private fun proceedToNextStep() {
        selectedCategory?.let { category ->
            val intent = Intent(this, AddPartActivity::class.java)
            intent.putExtra("cropped_image_uri", croppedImageUri.toString())
            intent.putExtra("category_id", category.id)
            intent.putExtra("category_name", category.name)
            intent.putExtra("requires_part_number", category.requiresPartNumber)
            intent.putExtra("has_attributes", category.hasAttributes)
            startActivity(intent)
            finish()
        }
    }
}

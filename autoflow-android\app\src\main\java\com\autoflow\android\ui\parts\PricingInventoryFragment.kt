package com.autoflow.android.ui.parts

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import androidx.fragment.app.Fragment
import com.autoflow.android.R

class PricingInventoryFragment : Fragment() {

    // UI Elements
    private lateinit var partPriceInput: EditText
    private lateinit var partCostInput: EditText
    private lateinit var partDiscountInput: EditText
    private lateinit var partTaxInput: EditText
    private lateinit var partStockInput: EditText
    private lateinit var partMinStockInput: EditText
    private lateinit var partLocationInput: EditText
    private lateinit var partSupplierInput: EditText
    private lateinit var partSupplierPartNumberInput: EditText

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_pricing_inventory, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        initializeViews(view)
    }

    private fun initializeViews(view: View) {
        partPriceInput = view.findViewById(R.id.partPriceInput)
        partCostInput = view.findViewById(R.id.partCostInput)
        partDiscountInput = view.findViewById(R.id.partDiscountInput)
        partTaxInput = view.findViewById(R.id.partTaxInput)
        partStockInput = view.findViewById(R.id.partStockInput)
        partMinStockInput = view.findViewById(R.id.partMinStockInput)
        partLocationInput = view.findViewById(R.id.partLocationInput)
        partSupplierInput = view.findViewById(R.id.partSupplierInput)
        partSupplierPartNumberInput = view.findViewById(R.id.partSupplierPartNumberInput)
    }

    fun loadPartData(
        price: Double,
        cost: Double,
        discount: Double,
        tax: Double,
        stock: Int,
        minStock: Int,
        location: String,
        supplier: String,
        supplierPartNumber: String
    ) {
        partPriceInput.setText(if (price > 0) price.toString() else "")
        partCostInput.setText(if (cost > 0) cost.toString() else "")
        partDiscountInput.setText(if (discount > 0) discount.toString() else "")
        partTaxInput.setText(if (tax > 0) tax.toString() else "")
        partStockInput.setText(stock.toString())
        partMinStockInput.setText(if (minStock > 0) minStock.toString() else "")
        partLocationInput.setText(location)
        partSupplierInput.setText(supplier)
        partSupplierPartNumberInput.setText(supplierPartNumber)
    }

    fun validateInputs(): Boolean {
        var isValid = true

        val priceText = partPriceInput.text.toString().trim()
        if (priceText.isEmpty() || priceText.toDoubleOrNull() == null || priceText.toDouble() < 0) {
            partPriceInput.error = "Valid selling price is required"
            isValid = false
        }

        val stockText = partStockInput.text.toString().trim()
        if (stockText.isEmpty() || stockText.toIntOrNull() == null || stockText.toInt() < 0) {
            partStockInput.error = "Valid stock quantity is required"
            isValid = false
        }

        // Validate cost if provided
        val costText = partCostInput.text.toString().trim()
        if (costText.isNotEmpty() && (costText.toDoubleOrNull() == null || costText.toDouble() < 0)) {
            partCostInput.error = "Valid cost price is required"
            isValid = false
        }

        // Validate discount if provided
        val discountText = partDiscountInput.text.toString().trim()
        if (discountText.isNotEmpty() && (discountText.toDoubleOrNull() == null || discountText.toDouble() < 0 || discountText.toDouble() > 100)) {
            partDiscountInput.error = "Discount must be between 0-100%"
            isValid = false
        }

        // Validate tax if provided
        val taxText = partTaxInput.text.toString().trim()
        if (taxText.isNotEmpty() && (taxText.toDoubleOrNull() == null || taxText.toDouble() < 0)) {
            partTaxInput.error = "Valid tax rate is required"
            isValid = false
        }

        // Validate min stock if provided
        val minStockText = partMinStockInput.text.toString().trim()
        if (minStockText.isNotEmpty() && (minStockText.toIntOrNull() == null || minStockText.toInt() < 0)) {
            partMinStockInput.error = "Valid minimum stock is required"
            isValid = false
        }

        return isValid
    }

    fun getPartData(): Map<String, Any> {
        return mapOf(
            "price" to (partPriceInput.text.toString().toDoubleOrNull() ?: 0.0),
            "cost" to (partCostInput.text.toString().toDoubleOrNull() ?: 0.0),
            "discount" to (partDiscountInput.text.toString().toDoubleOrNull() ?: 0.0),
            "tax" to (partTaxInput.text.toString().toDoubleOrNull() ?: 0.0),
            "stock" to (partStockInput.text.toString().toIntOrNull() ?: 0),
            "min_stock" to (partMinStockInput.text.toString().toIntOrNull() ?: 0),
            "location" to partLocationInput.text.toString().trim(),
            "supplier" to partSupplierInput.text.toString().trim(),
            "supplier_part_number" to partSupplierPartNumberInput.text.toString().trim()
        )
    }
}

package com.autoflow.android.domain.models

/** API request and response models Mirrors web app's API structure */

// UI State models
data class AddPartUiState(
        val isLoading: <PERSON>olean = false,
        val isSubmitting: Boolean = false,
        val isUploadingImage: Boolean = false,
        val isLoadingCategories: Boolean = false,
        val submitSuccess: Boolean = false,
        val error: String? = null,
        val currentStep: AddPartStep = AddPartStep.IMAGES
)

// Form validation
data class FormValidationResult(
        val isValid: Boolean,
        val errors: List<String> = emptyList(),
        val warnings: List<String> = emptyList()
)

// Step completion status
data class StepCompletionStatus(
        val imagesComplete: Boolean = false,
        val categoryComplete: Boolean = false,
        val partNumberComplete: Boolean = false,
        val vehicleSelectionComplete: Boolean = false,
        val attributesComplete: Boolean = false,
        val conditionPricingComplete: Boolean = false,
        val canSubmit: Boolean = false
)

package com.autoflow.android.data.api

import com.autoflow.android.core.auth.AuthManager
import com.autoflow.android.data.api.dto.*
import com.autoflow.android.data.mappers.toDomain
import com.autoflow.android.domain.models.*
import java.io.File
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import org.json.JSONArray
import org.json.JSONObject
import retrofit2.HttpException

/** Real HTTP-backed implementation of AddPartApiService using RetrofitServices */
class RealAddPartApiService(private val authManager: AuthManager) : AddPartApiService {

    // ========== CATEGORIES AND ATTRIBUTES ==========
    override suspend fun getCategories(): CategoriesResponse {
        val svc = RetrofitServices.categories(authManager)

        // 1) Try full categories FIRST (typed) → /api/categories -> [{ id, label, parent,
        // requirePartNumber }]
        try {
            val full = svc.getCategories()
            if (full.isSuccessful) {
                val list = full.body()
                if (list != null && list.isNotEmpty()) {
                    android.util.Log.d(
                            "RealAddPartApiService",
                            "Using hierarchical categories from /api/categories, received ${list.size} items"
                    )

                    // Log first few categories to see their structure
                    list.take(5).forEachIndexed { index, dto ->
                        android.util.Log.d(
                                "RealAddPartApiService",
                                "DTO $index: id=${dto.id}, label='${dto.label}', parent=${dto.parent}, requirePartNumber=${dto.requirePartNumber}"
                        )
                    }

                    val mapped =
                            list.map { dto ->
                                CategoryResponse(
                                        id = dto.id,
                                        label = dto.label,
                                        parentCategoryId = dto.parent,
                                        children = emptyList(),
                                        requiresPartNumber = dto.requirePartNumber,
                                        hasAttributes = false
                                )
                            }
                    return CategoriesResponse(categories = mapped, success = true)
                }
            } else {
                android.util.Log.w(
                        "RealAddPartApiService",
                        "/api/categories failed with code: ${full.code()}"
                )
            }
        } catch (e: Exception) {
            android.util.Log.w(
                    "RealAddPartApiService",
                    "/api/categories threw exception: ${e.message}"
            )
        }

        // 2) Fallback to simple categories (no hierarchy) → /api/parts/categories -> [{ id, name }]
        try {
            val resp = svc.getSimpleCategories()
            if (resp.isSuccessful) {
                val list = resp.body()
                if (list != null && list.isNotEmpty()) {
                    android.util.Log.d(
                            "RealAddPartApiService",
                            "Falling back to simple categories from /api/parts/categories, received ${list.size} items"
                    )

                    // Log first few simple categories
                    list.take(3).forEachIndexed { index, dto ->
                        android.util.Log.d(
                                "RealAddPartApiService",
                                "Simple DTO $index: id=${dto.id}, name='${dto.name}'"
                        )
                    }

                    val mapped =
                            list.map { dto ->
                                CategoryResponse(
                                        id = dto.id,
                                        label = dto.name,
                                        parentCategoryId = null,
                                        children = emptyList(),
                                        requiresPartNumber = false,
                                        hasAttributes = false
                                )
                            }
                    return CategoriesResponse(categories = mapped, success = true)
                }
            } else {
                android.util.Log.w(
                        "RealAddPartApiService",
                        "/api/parts/categories failed with code: ${resp.code()}"
                )
            }
        } catch (e: Exception) {
            android.util.Log.w(
                    "RealAddPartApiService",
                    "/api/parts/categories threw exception: ${e.message}"
            )
        }

        // 3) Fetch raw body and attempt resilient parsing for common wrapper shapes
        val raw = runCatching { svc.getCategoriesRaw() }.getOrNull()
        val rawText =
                when {
                    raw?.isSuccessful == true -> raw.body()?.string()
                    else -> raw?.errorBody()?.string()
                }
                        ?: ""

        // Try to parse as array
        runCatching {
            val arr = JSONArray(rawText)
            val mapped =
                    (0 until arr.length()).map { i ->
                        val obj = arr.getJSONObject(i)
                        CategoryResponse(
                                id = obj.optInt("id"),
                                label = obj.optString("label", obj.optString("name")),
                                parentCategoryId =
                                        if (obj.has("parent")) obj.optInt("parent") else null,
                                children = emptyList(),
                                requiresPartNumber = obj.optBoolean("requirePartNumber", false),
                                hasAttributes = obj.optBoolean("hasAttributes", false)
                        )
                    }
            return CategoriesResponse(categories = mapped, success = true)
        }

        // Try wrapped shapes: { categories: [...] } or { data: [...] }
        runCatching {
            val obj = JSONObject(rawText)
            val key =
                    when {
                        obj.has("categories") -> "categories"
                        obj.has("data") -> "data"
                        else -> null
                    }
                            ?: throw IllegalStateException("No categories key")

            val arr = obj.getJSONArray(key)
            val mapped =
                    (0 until arr.length()).map { i ->
                        val item = arr.getJSONObject(i)
                        CategoryResponse(
                                id = item.optInt("id"),
                                label = item.optString("label", item.optString("name")),
                                parentCategoryId =
                                        if (item.has("parent")) item.optInt("parent") else null,
                                children = emptyList(),
                                requiresPartNumber = item.optBoolean("requirePartNumber", false),
                                hasAttributes = item.optBoolean("hasAttributes", false)
                        )
                    }
            return CategoriesResponse(categories = mapped, success = true)
        }

        throw IllegalStateException(
                "Failed to load categories. Unexpected payload: ${rawText.take(200)}"
        )
    }

    override suspend fun getCategoryAttributes(categoryId: Int): CategoryAttributesResponse {
        val resp = RetrofitServices.categories(authManager).getCategoryAttributes(categoryId)
        if (!resp.isSuccessful) throw HttpException(resp)
        val list = resp.body() ?: emptyList()
        val mapped = list.map { it.toDomain() }
        return CategoryAttributesResponse(attributes = mapped, success = true)
    }

    // =========== VEHICLES CASCADE ==========
    override suspend fun getBrands(): List<CarBrand> {
        // Backend supports only VW and AUDI in current scope
        return listOf(CarBrand(1, "VW"), CarBrand(2, "AUDI"))
    }

    override suspend fun getModels(brandId: Int): List<CarModel> {
        val resp = RetrofitServices.vehicles(authManager).getModels(brandId)
        if (!resp.isSuccessful) throw HttpException(resp)
        val list = resp.body() ?: emptyList()
        return list.map { CarModel(id = it.id, brandId = it.brandId, name = it.name) }
    }

    override suspend fun getGenerations(modelId: Int): List<CarGeneration> {
        val resp = RetrofitServices.vehicles(authManager).getGenerations(modelId)
        if (!resp.isSuccessful) throw HttpException(resp)
        return (resp.body() ?: emptyList()).map {
            CarGeneration(id = it.id, modelId = it.modelId, name = it.name)
        }
    }

    override suspend fun getVariations(generationId: Int): List<CarVariation> {
        val resp = RetrofitServices.vehicles(authManager).getVariations(generationId)
        if (!resp.isSuccessful) throw HttpException(resp)
        return (resp.body() ?: emptyList()).map {
            CarVariation(id = it.id, generationId = it.generationId, name = it.name)
        }
    }

    override suspend fun getTrims(variationId: Int): List<CarTrim> {
        val resp = RetrofitServices.vehicles(authManager).getTrims(variationId)
        if (!resp.isSuccessful) throw HttpException(resp)
        return (resp.body() ?: emptyList()).map {
            CarTrim(id = it.id, variationId = it.variationId, name = it.name)
        }
    }

    // ========== PART NUMBER COMPATIBILITY ==========
    override suspend fun checkCompatibility(
            request: CheckCompatibilityRequest
    ): CheckCompatibilityResponse {
        val resp =
                RetrofitServices.parts(authManager)
                        .checkCompatibility(
                                CheckCompatibilityBody(request.partNumber, request.categoryId)
                        )
        if (!resp.isSuccessful) throw HttpException(resp)
        val dto =
                resp.body()
                        ?: return CheckCompatibilityResponse(
                                success = false,
                                message = "Empty body"
                        )
        val comp = dto.toDomain(request.partNumber)
        return CheckCompatibilityResponse(
                success = true,
                partName = comp.partName,
                compatiblePartNumbers = comp.compatiblePartNumbers,
                engineCompatibility = comp.engineCompatibility,
                vehicleCompatibility = comp.vehicleCompatibility,
                partnumberGroup = comp.partnumberGroup,
                message = "OK"
        )
    }

    // ========== TITLE AND DESCRIPTION ==========
    override suspend fun generateTitle(request: GenerateTitleRequest): GenerateTitleResponse {
        // Server expects minimal context; we send categoryId and let backend template handle it
        val resp =
                RetrofitServices.parts(authManager)
                        .generateTitle(GenerateTitleBody(categoryId = request.categoryId))
        return if (resp.isSuccessful) {
            val body = resp.body()
            if (body != null) GenerateTitleResponse(title = body.title) else fallbackTitle(request)
        } else {
            fallbackTitle(request)
        }
    }

    private fun fallbackTitle(request: GenerateTitleRequest): GenerateTitleResponse {
        val pn = request.partNumber?.takeIf { it.isNotBlank() }
        val vehicle = request.vehicleInfo?.takeIf { it.isNotBlank() }
        val title =
                buildString {
                            vehicle?.let { append(it).append(' ') }
                            append("Part")
                            pn?.let { append(' ').append(it) }
                        }
                        .trim()
        return GenerateTitleResponse(title = title.ifBlank { "Auto Part" })
    }

    override suspend fun generateDescription(
            request: GenerateDescriptionRequest
    ): GenerateDescriptionResponse {
        val resp =
                RetrofitServices.parts(authManager)
                        .generateDescription(
                                GenerateDescriptionBody(
                                        categoryId = request.categoryId,
                                        title = request.title
                                )
                        )
        return if (resp.isSuccessful) {
            val body = resp.body()
            if (body != null) GenerateDescriptionResponse(description = body.description)
            else fallbackDescription(request)
        } else fallbackDescription(request)
    }

    private fun fallbackDescription(
            request: GenerateDescriptionRequest
    ): GenerateDescriptionResponse {
        val desc =
                "${request.condition.replaceFirstChar { if (it.isLowerCase()) it.titlecase() else it.toString() }} part - ${request.title}"
        return GenerateDescriptionResponse(description = desc)
    }

    // ========== IMAGE UPLOAD ==========
    override suspend fun uploadImage(imageFile: File): ImageUploadResponse {
        val mediaType = "image/jpeg".toMediaTypeOrNull()
        val reqBody: RequestBody = imageFile.asRequestBody(mediaType)
        val part = MultipartBody.Part.createFormData("file", imageFile.name, reqBody)
        val resp = RetrofitServices.images(authManager).uploadImage(part)
        if (!resp.isSuccessful) throw HttpException(resp)
        val dto = resp.body() ?: throw IllegalStateException("Empty body")
        return ImageUploadResponse(imageUrl = dto.imageUrl)
    }

    // ========== PART CREATION ==========
    override suspend fun createPart(request: CreatePartRequest): CreatePartResponse {
        val images =
                request.images.mapIndexed { index, url ->
                    CreatePartImageDto(url = url, isMain = index == 0)
                }
        val attrs: List<AttributeValueDto> =
                request.attributes.mapNotNull { (k, v) ->
                    val id = k.toIntOrNull() ?: return@mapNotNull null
                    AttributeValueDto(attribute_id = id, value = v.toString())
                }
        val vehicles: VehiclesPayloadDto? =
                when (request.vehicles.mode) {
                    "manual" -> VehiclesPayloadDto("manual", null, request.vehicles.trimId)
                    else -> {
                        val list =
                                request.vehicles.list?.map { vc ->
                                    VehicleCompatibilityDto(
                                            brand = vc.brand,
                                            model = vc.model,
                                            generation = vc.generation,
                                            variation = vc.variation.ifBlank { null },
                                            trims =
                                                    vc.trim.takeIf { it.isNotBlank() }?.let {
                                                        listOf(it)
                                                    }
                                    )
                                }
                        VehiclesPayloadDto("compatibility", list, null)
                    }
                }
        val body =
                CreatePartBody(
                        category_id = request.categoryId,
                        title = request.title,
                        description = request.description,
                        partnumber_group = request.partnumberGroup?.toLong(),
                        createdBy = request.createdBy,
                        images = images,
                        condition = request.condition,
                        new =
                                request.newCondition?.let {
                                    StockPriceDto(
                                            stock = it.stock,
                                            price = it.price,
                                            discounted_price = it.discountedPrice
                                    )
                                },
                        used =
                                request.usedCondition?.let {
                                    StockPriceDto(
                                            stock = it.stock,
                                            price = it.price,
                                            discounted_price = it.discountedPrice
                                    )
                                },
                        attributes = attrs,
                        vehicles = vehicles,
                        engines = request.engines
                )
        val idemp = java.util.UUID.randomUUID().toString()
        val resp = RetrofitServices.parts(authManager).createPart(idemp, body)
        if (!resp.isSuccessful) throw HttpException(resp)
        val dto = resp.body() ?: throw IllegalStateException("Empty body")
        return CreatePartResponse(partId = dto.partId, title = dto.title)
    }

    // ========== STORAGE ==========
    override suspend fun getStorageAreas(): List<StorageArea> {
        val resp = RetrofitServices.storage(authManager).getAreas()
        if (!resp.isSuccessful) throw HttpException(resp)
        return (resp.body() ?: emptyList()).map { StorageArea(id = it.id, name = it.name) }
    }

    override suspend fun getStorageUnits(areaId: Int?): List<StorageUnit> {
        val resp = RetrofitServices.storage(authManager).getUnits(areaId)
        if (!resp.isSuccessful) throw HttpException(resp)
        return (resp.body() ?: emptyList()).map {
            StorageUnit(id = it.id, areaId = it.areaId, name = it.name, unitType = it.unitType)
        }
    }

    override suspend fun savePartLocation(
            partId: Int,
            request: SaveLocationRequest
    ): SaveLocationResponse {
        val body =
                SaveLocationBody(
                        unit_id = request.unitId,
                        quantity = request.quantity,
                        location_subtype = request.locationSubtype,
                        details = request.details,
                        notes = request.notes
                )
        val resp = RetrofitServices.storage(authManager).saveLocation(partId, body)
        if (!resp.isSuccessful) throw HttpException(resp)
        return SaveLocationResponse(success = true, message = "Saved")
    }
}

<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".ui.parts.CategorySelectionActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="24dp">

            <Button
                android:id="@+id/backButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Back"
                android:textAllCaps="false"
                android:background="@android:color/transparent"
                android:textColor="@color/brand_primary" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Select Category"
                android:textSize="20sp"
                android:textStyle="bold"
                android:gravity="center"
                android:textColor="@color/brand_black" />

            <Button
                android:id="@+id/continueButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Continue"
                android:textAllCaps="false"
                android:background="@color/brand_primary"
                android:textColor="@android:color/white"
                android:paddingHorizontal="24dp"
                android:enabled="false" />

        </LinearLayout>

        <!-- Status Text -->
        <TextView
            android:id="@+id/statusText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Loading categories..."
            android:textSize="14sp"
            android:textColor="@color/brand_primary"
            android:gravity="center"
            android:layout_marginBottom="16dp"
            android:visibility="visible" />

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="16dp"
            android:visibility="gone" />

        <!-- Image Preview -->
        <androidx.cardview.widget.CardView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_gravity="center"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <ImageView
                android:id="@+id/partImageView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:background="@color/light_gray"
                android:contentDescription="Part image preview"
                tools:src="@drawable/ic_camera" />

        </androidx.cardview.widget.CardView>

        <!-- Instructions -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Choose the category that best describes your part. This will determine what information you need to provide."
            android:textSize="14sp"
            android:textColor="@color/brand_black"
            android:gravity="center"
            android:layout_marginBottom="24dp"
            android:lineSpacingExtra="2dp" />

        <!-- Category Selection Dropdown -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Category"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp"
            android:textColor="@color/brand_black" />

        <Spinner
            android:id="@+id/categorySpinner"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/spinner_background" />

        <!-- Selected Category Info -->
        <TextView
            android:id="@+id/selectedCategoryInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text=""
            android:textSize="14sp"
            android:textColor="@color/brand_primary"
            android:gravity="center"
            android:layout_marginBottom="24dp"
            android:visibility="gone" />

        <!-- Bottom Spacing -->
        <View
            android:layout_width="match_parent"
            android:layout_height="24dp" />

    </LinearLayout>

</ScrollView>

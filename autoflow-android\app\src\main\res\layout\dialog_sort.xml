<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/dialog_background">

    <!-- Dialog Title -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Sort By"
        android:textColor="@color/brand_black"
        android:textSize="20sp"
        android:textStyle="bold"
        android:layout_marginBottom="24dp" />

    <!-- Sort Options -->
    <RadioGroup
        android:id="@+id/sortOptionsGroup"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp">

        <RadioButton
            android:id="@+id/sortRelevance"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Relevance"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:paddingVertical="8dp"
            android:checked="true" />

        <RadioButton
            android:id="@+id/sortPriceLowToHigh"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Price: Low to High"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:paddingVertical="8dp" />

        <RadioButton
            android:id="@+id/sortPriceHighToLow"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Price: High to Low"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:paddingVertical="8dp" />

        <RadioButton
            android:id="@+id/sortNameAToZ"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Name: A to Z"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:paddingVertical="8dp" />

        <RadioButton
            android:id="@+id/sortNameZToA"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Name: Z to A"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:paddingVertical="8dp" />

        <RadioButton
            android:id="@+id/sortNewestFirst"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Newest First"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:paddingVertical="8dp" />

    </RadioGroup>

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <Button
            android:id="@+id/cancelSortButton"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:text="Cancel"
            android:textColor="@color/text_secondary"
            android:background="@drawable/button_text_secondary"
            android:paddingHorizontal="24dp"
            android:textSize="14sp"
            android:layout_marginEnd="12dp" />

        <Button
            android:id="@+id/applySortButton"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:text="Apply"
            android:textColor="@color/text_on_brand"
            android:background="@drawable/button_filled_teal"
            android:paddingHorizontal="24dp"
            android:textSize="14sp" />

    </LinearLayout>

</LinearLayout>

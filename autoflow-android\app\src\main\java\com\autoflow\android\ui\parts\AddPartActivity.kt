package com.autoflow.android.ui.parts

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.autoflow.android.R
import com.autoflow.android.data.api.PartsApiService
import com.autoflow.android.data.repository.AuthRepository
import com.autoflow.android.ui.dashboard.DashboardActivity
import kotlinx.coroutines.launch

class AddPartActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "AddPartActivity"
    }

    private lateinit var authRepository: AuthRepository
    private lateinit var partsApiService: PartsApiService

    // UI Elements
    private lateinit var imageView: ImageView
    private lateinit var titleEditText: EditText
    private lateinit var descriptionEditText: EditText
    private lateinit var categoryNameText: TextView
    private lateinit var partNumberEditText: EditText
    private lateinit var partNumberLayout: LinearLayout
    private lateinit var storageLocationEditText: EditText
    private lateinit var priceEditText: EditText
    private lateinit var stockEditText: EditText
    private lateinit var conditionSpinner: Spinner
    private lateinit var attributesContainer: LinearLayout
    private lateinit var saveButton: Button
    private lateinit var cancelButton: Button
    private lateinit var progressBar: ProgressBar
    private lateinit var statusText: TextView

    private var croppedImageUri: Uri? = null
    private var uploadedImageUrl: String? = null
    private var categoryId: Int = -1
    private var categoryName: String = ""
    private var requiresPartNumber: Boolean = false
    private var hasAttributes: Boolean = false
    private val categoryAttributes = mutableListOf<CategoryAttribute>()

    data class CategoryAttribute(
            val id: Int,
            val name: String,
            val inputType: String,
            val isRequired: Boolean,
            val options: List<String> = emptyList()
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_add_part)

        authRepository = AuthRepository(this)
        partsApiService = PartsApiService(this)

        initializeViews()
        setupClickListeners()
        loadCroppedImage()
        loadCategoryData()
        setupCategorySpecificForm()
    }

    private fun initializeViews() {
        imageView = findViewById(R.id.partImageView)
        titleEditText = findViewById(R.id.titleEditText)
        descriptionEditText = findViewById(R.id.descriptionEditText)
        categoryNameText = findViewById(R.id.categoryNameText)
        partNumberEditText = findViewById(R.id.partNumberEditText)
        partNumberLayout = findViewById(R.id.partNumberLayout)
        storageLocationEditText = findViewById(R.id.storageLocationEditText)
        priceEditText = findViewById(R.id.priceEditText)
        stockEditText = findViewById(R.id.stockEditText)
        conditionSpinner = findViewById(R.id.conditionSpinner)
        attributesContainer = findViewById(R.id.attributesContainer)
        saveButton = findViewById(R.id.saveButton)
        cancelButton = findViewById(R.id.cancelButton)
        progressBar = findViewById(R.id.progressBar)
        statusText = findViewById(R.id.statusText)

        // Setup condition spinner
        val conditions = arrayOf("New", "Used", "Refurbished")
        val conditionAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, conditions)
        conditionAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        conditionSpinner.adapter = conditionAdapter
        conditionSpinner.setSelection(1) // Default to "Used"
    }

    private fun setupClickListeners() {
        saveButton.setOnClickListener { uploadImageAndSavePart() }
        cancelButton.setOnClickListener { finish() }
    }

    private fun loadCroppedImage() {
        val imageUriString = intent.getStringExtra("cropped_image_uri")
        if (imageUriString != null) {
            croppedImageUri = Uri.parse(imageUriString)
            // Load image without Glide for now to avoid dependency issues
            imageView.setImageURI(croppedImageUri)
        } else {
            Toast.makeText(this, "Error loading image", Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    private fun loadCategoryData() {
        categoryId = intent.getIntExtra("category_id", -1)
        categoryName = intent.getStringExtra("category_name") ?: ""
        requiresPartNumber = intent.getBooleanExtra("requires_part_number", false)
        hasAttributes = intent.getBooleanExtra("has_attributes", false)

        if (categoryId == -1 || categoryName.isEmpty()) {
            Toast.makeText(this, "Invalid category data", Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        categoryNameText.text = categoryName
    }

    private fun setupCategorySpecificForm() {
        // Show/hide part number field based on category
        partNumberLayout.visibility = if (requiresPartNumber) View.VISIBLE else View.GONE

        // Load category attributes if needed
        if (hasAttributes) {
            loadCategoryAttributes()
        } else {
            attributesContainer.visibility = View.GONE
        }

        updateStatus("Fill in the ${categoryName.lowercase()} details")
    }

    private fun loadCategoryAttributes() {
        // Use API-based loading instead of hardcoded attributes
        loadCategoryAttributesFromAPI()
    }

    private fun loadCategoryAttributesFromAPI() {
        lifecycleScope.launch {
            try {
                updateStatus("Loading category attributes...")

                // Fetch attributes from database via API
                val result = partsApiService.fetchCategoryAttributes(categoryId)

                if (result.isSuccess) {
                    val apiAttributes = result.getOrNull() ?: emptyList()
                    categoryAttributes.clear()

                    // Convert API attributes to local CategoryAttribute format
                    categoryAttributes.addAll(
                            apiAttributes.map { apiAttribute ->
                                CategoryAttribute(
                                        id = apiAttribute.id,
                                        name = apiAttribute.attribute,
                                        inputType = apiAttribute.inputType,
                                        isRequired = apiAttribute.isRequired,
                                        options = apiAttribute.options.map { it.optionValue }
                                )
                            }
                    )

                    // Create dynamic form fields for attributes
                    createAttributeFields()
                    updateStatus("Fill in the ${categoryName.lowercase()} details")
                } else {
                    // Fallback to default attributes if API fails
                    Log.w(TAG, "API failed, using default attributes: ${result.exceptionOrNull()}")
                    categoryAttributes.clear()
                    categoryAttributes.addAll(getCategoryAttributes(categoryId))
                    createAttributeFields()
                    updateStatus("Fill in the ${categoryName.lowercase()} details (offline mode)")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error loading category attributes", e)
                updateStatus("Error loading attributes")
                Toast.makeText(this@AddPartActivity, "Error loading attributes", Toast.LENGTH_SHORT)
                        .show()

                // Fallback to default attributes
                categoryAttributes.clear()
                categoryAttributes.addAll(getCategoryAttributes(categoryId))
                createAttributeFields()
            }
        }
    }

    private fun getCategoryAttributes(categoryId: Int): List<CategoryAttribute> {
        // Return category-specific attributes based on web version implementation
        return when (categoryId) {
            1 ->
                    listOf( // Engine Parts
                            CategoryAttribute(
                                    1,
                                    "Engine Type",
                                    "dropdown",
                                    true,
                                    listOf("Petrol", "Diesel", "Hybrid")
                            ),
                            CategoryAttribute(2, "Engine Size", "text", true),
                            CategoryAttribute(
                                    3,
                                    "Cylinder Count",
                                    "dropdown",
                                    false,
                                    listOf("4", "6", "8", "12")
                            )
                    )
            2 ->
                    listOf( // Body Parts
                            CategoryAttribute(
                                    4,
                                    "Side",
                                    "dropdown",
                                    true,
                                    listOf("Left", "Right", "Center")
                            ),
                            CategoryAttribute(5, "Color", "text", false),
                            CategoryAttribute(
                                    6,
                                    "Material",
                                    "dropdown",
                                    false,
                                    listOf("Metal", "Plastic", "Carbon Fiber")
                            )
                    )
            3 ->
                    listOf( // Electrical Parts
                            CategoryAttribute(7, "Voltage", "dropdown", true, listOf("12V", "24V")),
                            CategoryAttribute(8, "Amperage", "text", false),
                            CategoryAttribute(9, "Connector Type", "text", false)
                    )
            4 ->
                    listOf( // Suspension Parts
                            CategoryAttribute(
                                    10,
                                    "Position",
                                    "dropdown",
                                    true,
                                    listOf("Front", "Rear")
                            ),
                            CategoryAttribute(
                                    11,
                                    "Side",
                                    "dropdown",
                                    true,
                                    listOf("Left", "Right", "Both")
                            ),
                            CategoryAttribute(
                                    12,
                                    "Type",
                                    "dropdown",
                                    false,
                                    listOf("Shock Absorber", "Spring", "Strut")
                            )
                    )
            5 ->
                    listOf( // Brake Parts
                            CategoryAttribute(
                                    13,
                                    "Position",
                                    "dropdown",
                                    true,
                                    listOf("Front", "Rear")
                            ),
                            CategoryAttribute(
                                    14,
                                    "Type",
                                    "dropdown",
                                    true,
                                    listOf("Disc", "Drum", "Pad", "Shoe")
                            ),
                            CategoryAttribute(15, "Diameter", "text", false)
                    )
            else -> emptyList()
        }
    }

    private fun createAttributeFields() {
        attributesContainer.removeAllViews()

        for (attribute in categoryAttributes) {
            val fieldLayout =
                    LinearLayout(this).apply {
                        orientation = LinearLayout.VERTICAL
                        setPadding(0, 16, 0, 16)
                    }

            // Label
            val label =
                    TextView(this).apply {
                        text = if (attribute.isRequired) "${attribute.name} *" else attribute.name
                        textSize = 16f
                        setTextColor(resources.getColor(R.color.brand_black, null))
                        setPadding(0, 0, 0, 8)
                    }
            fieldLayout.addView(label)

            // Input field based on type
            when (attribute.inputType) {
                "text" -> {
                    val editText =
                            EditText(this).apply {
                                hint = "Enter ${attribute.name.lowercase()}"
                                tag = "attribute_${attribute.id}"
                                background =
                                        resources.getDrawable(R.drawable.spinner_background, null)
                                setPadding(16, 16, 16, 16)
                            }
                    fieldLayout.addView(editText)
                }
                "dropdown" -> {
                    val spinner =
                            Spinner(this).apply {
                                tag = "attribute_${attribute.id}"
                                background =
                                        resources.getDrawable(R.drawable.spinner_background, null)
                            }
                    val adapter =
                            ArrayAdapter(
                                    this,
                                    android.R.layout.simple_spinner_item,
                                    attribute.options
                            )
                    adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
                    spinner.adapter = adapter
                    fieldLayout.addView(spinner)
                }
            }

            attributesContainer.addView(fieldLayout)
        }
    }

    private fun uploadImageAndSavePart() {
        if (validateForm()) {
            lifecycleScope.launch {
                try {
                    updateStatus("Uploading image...")
                    setFormEnabled(false)

                    // Upload image to Supabase
                    uploadedImageUrl = uploadImageToSupabase()

                    if (uploadedImageUrl != null) {
                        updateStatus("Saving part...")
                        savePart()
                    } else {
                        updateStatus("Failed to upload image")
                        setFormEnabled(true)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error in upload process", e)
                    updateStatus("Error: ${e.message}")
                    setFormEnabled(true)
                }
            }
        }
    }

    private fun validateForm(): Boolean {
        if (titleEditText.text.toString().trim().isEmpty()) {
            titleEditText.error = "Title is required"
            return false
        }

        // Validate part number if required
        if (requiresPartNumber && partNumberEditText.text.toString().trim().isEmpty()) {
            partNumberEditText.error = "Part number is required for this category"
            return false
        }

        if (priceEditText.text.toString().trim().isEmpty()) {
            priceEditText.error = "Price is required"
            return false
        }
        if (stockEditText.text.toString().trim().isEmpty()) {
            stockEditText.error = "Stock is required"
            return false
        }

        // Validate required attributes
        for (attribute in categoryAttributes) {
            if (attribute.isRequired) {
                val view = attributesContainer.findViewWithTag<View>("attribute_${attribute.id}")
                when (view) {
                    is EditText -> {
                        if (view.text.toString().trim().isEmpty()) {
                            view.error = "${attribute.name} is required"
                            return false
                        }
                    }
                    is Spinner -> {
                        if (view.selectedItemPosition == 0 && attribute.options.isNotEmpty()) {
                            Toast.makeText(
                                            this,
                                            "${attribute.name} is required",
                                            Toast.LENGTH_SHORT
                                    )
                                    .show()
                            return false
                        }
                    }
                }
            }
        }

        return true
    }

    private suspend fun uploadImageToSupabase(): String? {
        // This is a simplified version - in a real app you'd use proper Supabase client
        // For now, we'll simulate the upload
        return try {
            // Simulate upload delay
            kotlinx.coroutines.delay(2000)
            "https://example.com/uploaded-image.jpg"
        } catch (e: Exception) {
            Log.e(TAG, "Error uploading image", e)
            null
        }
    }

    private suspend fun savePart() {
        try {
            // Collect attribute values
            val attributeValues = mutableMapOf<String, String>()
            for (attribute in categoryAttributes) {
                val view = attributesContainer.findViewWithTag<View>("attribute_${attribute.id}")
                when (view) {
                    is EditText -> {
                        attributeValues[attribute.name] = view.text.toString().trim()
                    }
                    is Spinner -> {
                        attributeValues[attribute.name] = view.selectedItem?.toString() ?: ""
                    }
                }
            }

            val partData =
                    mapOf(
                            "title" to titleEditText.text.toString().trim(),
                            "description" to descriptionEditText.text.toString().trim(),
                            "category_id" to categoryId,
                            "category_name" to categoryName,
                            "part_number" to
                                    if (requiresPartNumber)
                                            partNumberEditText.text.toString().trim()
                                    else "",
                            "imageUrl" to uploadedImageUrl,
                            "price" to priceEditText.text.toString().toDoubleOrNull(),
                            "stock" to stockEditText.text.toString().toIntOrNull(),
                            "condition" to conditionSpinner.selectedItem.toString(),
                            "storage_location" to storageLocationEditText.text.toString().trim(),
                            "attributes" to attributeValues
                    )

            // Call API to save part
            // This would use your PartsApiService
            updateStatus("Part saved successfully!")

            Toast.makeText(this, "Part added successfully!", Toast.LENGTH_LONG).show()

            // Navigate back to dashboard
            val intent = Intent(this, DashboardActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
            startActivity(intent)
            finish()
        } catch (e: Exception) {
            Log.e(TAG, "Error saving part", e)
            updateStatus("Error saving part: ${e.message}")
            setFormEnabled(true)
        }
    }

    private fun updateStatus(message: String) {
        runOnUiThread {
            statusText.text = message
            statusText.visibility = View.VISIBLE
        }
    }

    private fun setFormEnabled(enabled: Boolean) {
        runOnUiThread {
            titleEditText.isEnabled = enabled
            descriptionEditText.isEnabled = enabled

            // Enable/disable part number field if visible
            if (requiresPartNumber) {
                partNumberEditText.isEnabled = enabled
            }

            // Enable/disable dynamic attribute fields
            for (attribute in categoryAttributes) {
                val view = attributesContainer.findViewWithTag<View>("attribute_${attribute.id}")
                when (view) {
                    is EditText -> view.isEnabled = enabled
                    is Spinner -> view.isEnabled = enabled
                }
            }

            storageLocationEditText.isEnabled = enabled
            priceEditText.isEnabled = enabled
            stockEditText.isEnabled = enabled
            conditionSpinner.isEnabled = enabled
            saveButton.isEnabled = enabled

            progressBar.visibility = if (enabled) View.GONE else View.VISIBLE
        }
    }
}

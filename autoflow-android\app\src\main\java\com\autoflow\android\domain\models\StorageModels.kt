package com.autoflow.android.domain.models

/** Storage location models for part storage assignment Mirrors web app's storage location system */
data class StorageArea(
        val id: Int,
        val name: String,
        val description: String = "",
        val isActive: Boolean = true
) {
    override fun toString(): String = name
}

data class StorageUnit(
        val id: Int,
        val areaId: Int,
        val name: String,
        val unitType: String, // "shelf", "crate", "container", "open_area", etc.
        val description: String = "",
        val isActive: Boolean = true
) {
    override fun toString(): String = name
}

/** Storage location form data for assignment */
data class StorageLocationFormData(
        val areaId: Int? = null,
        val unitId: Int? = null,
        val locationSubtype: String = "",
        val quantity: Int = 1,
        val notes: String? = null,
        val details: Map<String, String> = emptyMap()
)

/** Storage location assignment result */
data class StorageLocationAssignment(
        val id: Int,
        val partId: Int,
        val areaId: Int,
        val unitId: Int,
        val locationSubtype: String,
        val quantity: Int,
        val notes: String? = null,
        val details: Map<String, String> = emptyMap(),
        val createdAt: Long = System.currentTimeMillis()
)

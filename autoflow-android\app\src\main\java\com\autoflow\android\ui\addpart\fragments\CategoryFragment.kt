package com.autoflow.android.ui.addpart.fragments

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.OvershootInterpolator
import android.widget.*
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.autoflow.android.R
import com.autoflow.android.domain.models.CategoryResponse
import com.autoflow.android.ui.addpart.AddPartViewModel
import com.google.android.material.animation.ArgbEvaluator
import com.google.android.material.card.MaterialCardView
import com.google.android.material.chip.Chip
import com.google.android.material.chip.ChipGroup
import com.google.android.material.elevation.SurfaceColors
import com.google.android.material.motion.MotionLayout
import com.google.android.material.shape.CornerFamily
import com.google.android.material.shape.MaterialShapeDrawable
import com.google.android.material.shape.ShapeAppearanceModel
import com.google.android.material.textfield.TextInputLayout
import com.google.android.material.textview.MaterialTextView
import kotlinx.coroutines.launch

/**
 * Category Fragment - Step 2 of Add Part flow
 * Modern, sophisticated hierarchical category selection with Material Design 3 principles
 */
class CategoryFragment : Fragment() {

    companion object {
        private const val TAG = "CategoryFragment"
        private const val ANIMATION_DURATION = 300L
        private const val STAGGER_DELAY = 50L
    }

    private val viewModel: AddPartViewModel by activityViewModels()

    // UI Components
    private lateinit var instructionText: MaterialTextView
    private lateinit var categoryInputLayout: TextInputLayout
    private lateinit var selectedCategoryText: MaterialTextView
    private lateinit var dropdownTrigger: MaterialCardView
    private lateinit var chevronIcon: ImageView
    private lateinit var dropdownContent: MaterialCardView
    private lateinit var searchInput: EditText
    private lateinit var categoriesRecyclerView: RecyclerView
    private lateinit var loadingIndicator: ProgressBar
    private lateinit var categoryInfoText: MaterialTextView
    private lateinit var selectedCategoryChip: Chip
    private lateinit var chipGroup: ChipGroup

    // Category handling
    private lateinit var categoriesAdapter: ModernCategoriesAdapter
    private var allCategories = listOf<CategoryResponse>()
    private var isDropdownOpen = false
    private var selectedCategory: CategoryResponse? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_category, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        android.util.Log.d(TAG, "onViewCreated called")
        bindViews(view)
        setupRecyclerView()
        setupClickListeners()
        setupMaterialDesign3()
        observeViewModel()
        android.util.Log.d(TAG, "onViewCreated completed")
    }

    private fun bindViews(root: View) {
        instructionText = root.findViewById(R.id.text_instructions)
        categoryInputLayout = root.findViewById(R.id.category_input_layout)
        dropdownContent = root.findViewById(R.id.dropdown_content)
        searchInput = root.findViewById(R.id.search_input_edit)
        categoriesRecyclerView = root.findViewById(R.id.recycler_categories)
        loadingIndicator = root.findViewById(R.id.progress_categories)
        categoryInfoText = root.findViewById(R.id.text_info)
        chipGroup = root.findViewById(R.id.chip_group_selected)
        
        // Create modern dropdown trigger
        setupModernDropdownTrigger(root)
        
        // Search input listener with Material Design 3 styling
        setupSearchInput()
        
        // Setup clear search button
        setupClearSearchButton(root)
    }

    private fun setupClearSearchButton(root: View) {
        val clearSearchButton = root.findViewById<ImageView>(R.id.clear_search_button)
        
        clearSearchButton.setOnClickListener {
            searchInput.text?.clear()
            filterCategories("")
            clearSearchButton.visibility = View.GONE
        }
        
        // Show/hide clear button and handle search
        searchInput.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                clearSearchButton.visibility = if (s.isNullOrEmpty()) View.GONE else View.VISIBLE
                
                // Filter categories in real-time as user types
                val query = s?.toString()?.trim() ?: ""
                android.util.Log.d(TAG, "Search query: '$query'")
                filterCategories(query)
            }
            override fun afterTextChanged(s: Editable?) {}
        })
    }

    private fun setupModernDropdownTrigger(root: View) {
        // Create a modern card-based trigger
        dropdownTrigger = MaterialCardView(requireContext()).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            radius = resources.getDimensionPixelSize(R.dimen.corner_radius_large).toFloat()
            elevation = resources.getDimensionPixelSize(R.dimen.elevation_medium).toFloat()
            strokeWidth = 0
            cardBackgroundColor = ContextCompat.getColorStateList(context, R.color.surface_variant)
            
            // Add Material Design 3 shape appearance
            shapeAppearanceModel = ShapeAppearanceModel.builder()
                .setAllCorners(CornerFamily.ROUNDED, resources.getDimensionPixelSize(R.dimen.corner_radius_large).toFloat())
                .build()
        }

        // Create the trigger content
        val triggerContent = LinearLayout(requireContext()).apply {
            orientation = LinearLayout.HORIZONTAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
            setPadding(
                resources.getDimensionPixelSize(R.dimen.spacing_large),
                resources.getDimensionPixelSize(R.dimen.spacing_large),
                resources.getDimensionPixelSize(R.dimen.spacing_large),
                resources.getDimensionPixelSize(R.dimen.spacing_large)
            )
            gravity = android.view.Gravity.CENTER_VERTICAL
        }

        // Selected category text
        selectedCategoryText = MaterialTextView(requireContext()).apply {
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
            text = getString(R.string.select_category_placeholder)
            setTextAppearance(R.style.TextAppearance_Material3_BodyLarge)
            setTextColor(ContextCompat.getColor(context, R.color.on_surface_variant))
        }

        // Chevron icon with rotation animation
        chevronIcon = ImageView(requireContext()).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
            setImageResource(R.drawable.ic_expand_more)
            imageTintList = ContextCompat.getColorStateList(context, R.color.on_surface_variant)
        }

        triggerContent.addView(selectedCategoryText)
        triggerContent.addView(chevronIcon)
        dropdownTrigger.addView(triggerContent)

        // Replace the old input layout with our modern trigger
        val parent = categoryInputLayout.parent as ViewGroup
        val index = parent.indexOfChild(categoryInputLayout)
        parent.removeView(categoryInputLayout)
        parent.addView(dropdownTrigger, index)

        // Set click listener
        dropdownTrigger.setOnClickListener { toggleDropdown() }
    }

    private fun setupSearchInput() {
        // Search functionality is now handled in setupClearSearchButton
        // to avoid duplicate TextWatcher setup
    }

    private fun setupMaterialDesign3() {
        // Apply Material Design 3 surface colors
        val surfaceColor = SurfaceColors.SURFACE_1.getColor(requireContext())
        dropdownContent.setCardBackgroundColor(surfaceColor)
        
        // Apply modern corner radius
        dropdownContent.radius = resources.getDimensionPixelSize(R.dimen.corner_radius_extra_large).toFloat()
        
        // Apply modern elevation
        dropdownContent.elevation = resources.getDimensionPixelSize(R.dimen.elevation_high).toFloat()
    }

    private fun setupRecyclerView() {
        categoriesAdapter = ModernCategoriesAdapter { category -> selectCategory(category) }

        categoriesRecyclerView.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = categoriesAdapter
            overScrollMode = View.OVER_SCROLL_NEVER
        }
    }

    private fun setupClickListeners() {
        // Click listeners are set in createLayout()
    }

    private fun observeViewModel() {
        android.util.Log.d(TAG, "Setting up ViewModel observers")
        lifecycleScope.launch {
            viewModel.categories.collect { categories ->
                android.util.Log.d(TAG, "Categories received: ${categories.size} items")
                allCategories = categories
                val tree = buildHierarchicalCategories(categories)
                android.util.Log.d(TAG, "Built tree with ${tree.size} root categories")
                categoriesAdapter.updateCategories(tree)
            }
        }

        lifecycleScope.launch {
            viewModel.uiState.collect { uiState ->
                if (uiState.isLoadingCategories) {
                    showLoadingState()
                } else {
                    hideLoadingState()
                }
            }
        }

        lifecycleScope.launch {
            viewModel.formState.collect { formState ->
                if (formState.selectedCategory.isNotBlank()) {
                    updateSelectedCategoryDisplay(formState.selectedCategory)
                    showCategoryInfo(formState.requirePartNumber)
                }
            }
        }
    }

    private fun showLoadingState() {
        loadingIndicator.visibility = View.VISIBLE
        categoriesRecyclerView.visibility = View.GONE
        
        // Animate loading indicator
        loadingIndicator.alpha = 0f
        loadingIndicator.animate()
            .alpha(1f)
            .setDuration(ANIMATION_DURATION)
            .setInterpolator(AccelerateDecelerateInterpolator())
            .start()
    }

    private fun hideLoadingState() {
        loadingIndicator.animate()
            .alpha(0f)
            .setDuration(ANIMATION_DURATION / 2)
            .withEndAction {
                loadingIndicator.visibility = View.GONE
                categoriesRecyclerView.visibility = View.VISIBLE
                
                // Staggered animation for categories
                animateCategoriesAppearance()
            }
            .start()
    }

    private fun animateCategoriesAppearance() {
        val children = categoriesRecyclerView.children.toList()
        children.forEachIndexed { index, child ->
            child.alpha = 0f
            child.translationY = 20f
            
            child.animate()
                .alpha(1f)
                .translationY(0f)
                .setDuration(ANIMATION_DURATION)
                .setStartDelay(index * STAGGER_DELAY)
                .setInterpolator(OvershootInterpolator(0.8f))
                .start()
        }
    }

    private fun buildHierarchicalCategories(
        flatCategories: List<CategoryResponse>
    ): List<CategoryResponse> {
        android.util.Log.d(TAG, "Building hierarchy from ${flatCategories.size} flat categories")

        val categoryMap = mutableMapOf<Int, CategoryResponse>()
        flatCategories.forEach { category ->
            categoryMap[category.id] = category.copy(children = mutableListOf())
        }

        val rootCategories = mutableListOf<CategoryResponse>()
        flatCategories.forEach { category ->
            if (category.parentCategoryId != null) {
                categoryMap[category.parentCategoryId]?.let { parent ->
                    val child = categoryMap[category.id]!!
                    val updatedParent = parent.copy(children = parent.children + child)
                    categoryMap[parent.id] = updatedParent
                }
            } else {
                rootCategories.add(categoryMap[category.id]!!)
            }
        }

        return rootCategories.map { root -> categoryMap[root.id]!! }
    }

    private fun toggleDropdown() {
        android.util.Log.d(TAG, "toggleDropdown called, current state: $isDropdownOpen")
        isDropdownOpen = !isDropdownOpen
        
        if (isDropdownOpen) {
            openDropdown()
        } else {
            closeDropdown()
        }
    }

    private fun openDropdown() {
        // Animate chevron rotation
        chevronIcon.animate()
            .rotation(180f)
            .setDuration(ANIMATION_DURATION)
            .setInterpolator(AccelerateDecelerateInterpolator())
            .start()

        // Animate dropdown appearance
        dropdownContent.alpha = 0f
        dropdownContent.scaleX = 0.95f
        dropdownContent.scaleY = 0.95f
        dropdownContent.visibility = View.VISIBLE
        
        val animatorSet = AnimatorSet()
        animatorSet.playTogether(
            ObjectAnimator.ofFloat(dropdownContent, "alpha", 1f),
            ObjectAnimator.ofFloat(dropdownContent, "scaleX", 1f),
            ObjectAnimator.ofFloat(dropdownContent, "scaleY", 1f)
        )
        animatorSet.duration = ANIMATION_DURATION
        animatorSet.interpolator = OvershootInterpolator(0.8f)
        animatorSet.start()

        // Focus search input
        searchInput.requestFocus()
    }

    private fun closeDropdown() {
        // Animate chevron rotation back
        chevronIcon.animate()
            .rotation(0f)
            .setDuration(ANIMATION_DURATION)
            .setInterpolator(AccelerateDecelerateInterpolator())
            .start()

        // Animate dropdown disappearance
        val animatorSet = AnimatorSet()
        animatorSet.playTogether(
            ObjectAnimator.ofFloat(dropdownContent, "alpha", 0f),
            ObjectAnimator.ofFloat(dropdownContent, "scaleX", 0.95f),
            ObjectAnimator.ofFloat(dropdownContent, "scaleY", 0.95f)
        )
        animatorSet.duration = ANIMATION_DURATION / 2
        animatorSet.interpolator = AccelerateDecelerateInterpolator()
        animatorSet.withEndAction {
            dropdownContent.visibility = View.GONE
            dropdownContent.alpha = 1f
            dropdownContent.scaleX = 1f
            dropdownContent.scaleY = 1f
            
            // Clear search and reset categories
            searchInput.text?.clear()
            filterCategories("")
        }
        animatorSet.start()
    }

    private fun updateSelectedCategoryDisplay(categoryName: String) {
        selectedCategoryText.text = categoryName
        selectedCategoryText.setTextColor(ContextCompat.getColor(requireContext(), R.color.on_surface))
        
        // Update trigger background color
        val colorStateList = ContextCompat.getColorStateList(requireContext(), R.color.primary_container)
        dropdownTrigger.cardBackgroundColor = colorStateList
        
        // Add selected category chip
        addSelectedCategoryChip(categoryName)
    }

    private fun addSelectedCategoryChip(categoryName: String) {
        chipGroup.removeAllViews()
        
        val chip = Chip(requireContext()).apply {
            text = categoryName
            isCloseIconVisible = true
            chipBackgroundColor = ContextCompat.getColorStateList(context, R.color.primary_container)
            setTextColor(ContextCompat.getColor(context, R.color.on_primary_container))
            setCloseIconTint(ContextCompat.getColorStateList(context, R.color.on_primary_container))
            
            setOnCloseIconClickListener {
                clearSelectedCategory()
            }
        }
        
        chipGroup.addView(chip)
        
        // Animate chip appearance
        chip.alpha = 0f
        chip.scaleX = 0.8f
        chip.scaleY = 0.8f
        
        chip.animate()
            .alpha(1f)
            .scaleX(1f)
            .scaleY(1f)
            .setDuration(ANIMATION_DURATION)
            .setInterpolator(OvershootInterpolator(0.8f))
            .start()
    }

    private fun clearSelectedCategory() {
        selectedCategory = null
        selectedCategoryText.text = getString(R.string.select_category_placeholder)
        selectedCategoryText.setTextColor(ContextCompat.getColor(requireContext(), R.color.on_surface_variant))
        
        // Reset trigger background
        val colorStateList = ContextCompat.getColorStateList(requireContext(), R.color.surface_variant)
        dropdownTrigger.cardBackgroundColor = colorStateList
        
        // Remove chip
        chipGroup.removeAllViews()
        
        // Clear form
        viewModel.selectCategory(null)
        
        // Hide info text
        categoryInfoText.visibility = View.GONE
    }

    private fun selectCategory(category: CategoryResponse) {
        // Only allow selection of leaf categories
        if (category.children.isEmpty()) {
            selectedCategory = category
            viewModel.selectCategory(category)
            updateSelectedCategoryDisplay(category.label)
            closeDropdown()
        } else {
            // Show Material Design 3 snackbar or toast
            Toast.makeText(context, "Please select a specific subcategory", Toast.LENGTH_SHORT).show()
        }
    }

    private fun filterCategories(query: String) {
        android.util.Log.d(TAG, "Filtering categories with query: '$query', allCategories size: ${allCategories.size}")
        val filteredCategories =
            if (query.isEmpty()) {
                buildHierarchicalCategories(allCategories)
            } else {
                filterCategoriesRecursive(buildHierarchicalCategories(allCategories), query)
            }

        android.util.Log.d(TAG, "Filtered categories size: ${filteredCategories.size}")
        categoriesAdapter.updateCategories(filteredCategories)
    }

    private fun filterCategoriesRecursive(
        categories: List<CategoryResponse>,
        query: String
    ): List<CategoryResponse> {
        return categories.mapNotNull { category ->
            val matches = category.label.contains(query, ignoreCase = true)
            val filteredChildren = filterCategoriesRecursive(category.children, query)

            when {
                matches -> category.copy(children = filteredChildren)
                filteredChildren.isNotEmpty() -> category.copy(children = filteredChildren)
                else -> null
            }
        }
    }

    private fun showCategoryInfo(requiresPartNumber: Boolean) {
        val infoText =
            if (requiresPartNumber) {
                "This category requires a part number for AI compatibility analysis"
            } else {
                "This category allows manual vehicle selection"
            }

        categoryInfoText.text = infoText
        categoryInfoText.visibility = View.VISIBLE
        
        // Animate info text appearance
        categoryInfoText.alpha = 0f
        categoryInfoText.translationY = 10f
        
        categoryInfoText.animate()
            .alpha(1f)
            .translationY(0f)
            .setDuration(ANIMATION_DURATION)
            .setInterpolator(OvershootInterpolator(0.8f))
            .start()
    }
}

/** Modern RecyclerView adapter for hierarchical categories with Material Design 3 */
class ModernCategoriesAdapter(private val onCategorySelected: (CategoryResponse) -> Unit) :
    RecyclerView.Adapter<ModernCategoriesAdapter.CategoryViewHolder>() {

    private var categories = listOf<CategoryResponse>()
    private var flattenedCategories = listOf<Pair<CategoryResponse, Int>>()
    private val expandedCategories = mutableSetOf<Int>()

    fun updateCategories(newCategories: List<CategoryResponse>) {
        categories = newCategories
        expandedCategories.clear()
        
        fun expandAllWithChildren(cats: List<CategoryResponse>) {
            cats.forEach { cat ->
                if (cat.children.isNotEmpty()) {
                    expandedCategories.add(cat.id)
                    expandAllWithChildren(cat.children)
                }
            }
        }
        expandAllWithChildren(newCategories)
        
        flattenedCategories = flattenCategories(newCategories, 0)
        notifyDataSetChanged()
    }

    private fun flattenCategories(
        categories: List<CategoryResponse>,
        level: Int
    ): List<Pair<CategoryResponse, Int>> {
        val result = mutableListOf<Pair<CategoryResponse, Int>>()
        categories.forEach { category ->
            result.add(Pair(category, level))
            if (expandedCategories.contains(category.id) && category.children.isNotEmpty()) {
                result.addAll(flattenCategories(category.children, level + 1))
            }
        }
        return result
    }

    fun toggleCategory(categoryId: Int) {
        if (expandedCategories.contains(categoryId)) {
            expandedCategories.remove(categoryId)
        } else {
            expandedCategories.add(categoryId)
        }
        flattenedCategories = flattenCategories(categories, 0)
        notifyDataSetChanged()
    }

    fun isExpanded(categoryId: Int): Boolean = expandedCategories.contains(categoryId)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CategoryViewHolder {
        val cardView = MaterialCardView(parent.context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            radius = parent.context.resources.getDimensionPixelSize(R.dimen.corner_radius_medium).toFloat()
            elevation = parent.context.resources.getDimensionPixelSize(R.dimen.elevation_low).toFloat()
            strokeWidth = 0
            cardBackgroundColor = ContextCompat.getColorStateList(context, R.color.surface)
            
            // Material Design 3 shape appearance
            shapeAppearanceModel = ShapeAppearanceModel.builder()
                .setAllCorners(CornerFamily.ROUNDED, parent.context.resources.getDimensionPixelSize(R.dimen.corner_radius_medium).toFloat())
                .build()
        }

        val contentLayout = LinearLayout(parent.context).apply {
            orientation = LinearLayout.HORIZONTAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
            setPadding(
                parent.context.resources.getDimensionPixelSize(R.dimen.spacing_large),
                parent.context.resources.getDimensionPixelSize(R.dimen.spacing_medium),
                parent.context.resources.getDimensionPixelSize(R.dimen.spacing_large),
                parent.context.resources.getDimensionPixelSize(R.dimen.spacing_medium)
            )
            gravity = android.view.Gravity.CENTER_VERTICAL
        }

        val chevron = ImageView(parent.context).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
            setImageResource(R.drawable.ic_expand_more)
            imageTintList = ContextCompat.getColorStateList(context, R.color.on_surface_variant)
            visibility = View.GONE
            setPadding(0, 0, parent.context.resources.getDimensionPixelSize(R.dimen.spacing_medium), 0)
        }

        val categoryName = MaterialTextView(parent.context).apply {
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
            setTextAppearance(R.style.TextAppearance_Material3_BodyLarge)
        }

        contentLayout.addView(chevron)
        contentLayout.addView(categoryName)
        cardView.addView(contentLayout)

        return CategoryViewHolder(cardView, chevron, categoryName)
    }

    override fun onBindViewHolder(holder: CategoryViewHolder, position: Int) {
        val (category, level) = flattenedCategories[position]
        holder.bind(category, level, onCategorySelected)
    }

    override fun getItemCount(): Int = flattenedCategories.size

    class CategoryViewHolder(
        private val itemView: MaterialCardView,
        private val chevron: ImageView,
        private val categoryName: MaterialTextView
    ) : RecyclerView.ViewHolder(itemView) {

        fun bind(
            category: CategoryResponse,
            level: Int,
            onCategorySelected: (CategoryResponse) -> Unit
        ) {
            // Enhanced indentation with Material Design 3 spacing
            val baseIndent = 16
            val levelIndent = 32
            val paddingLeft = baseIndent + (level * levelIndent)
            itemView.setPadding(paddingLeft, 16, 16, 16)

            // Material Design 3 color scheme
            if (category.children.isNotEmpty()) {
                // Parent category styling
                itemView.cardBackgroundColor = ContextCompat.getColorStateList(context, R.color.surface_variant)
                categoryName.setTextColor(ContextCompat.getColor(context, R.color.on_surface_variant))
                categoryName.setTextAppearance(R.style.TextAppearance_Material3_BodyLarge)
                
                chevron.visibility = View.VISIBLE
                val adapter = bindingAdapter as? ModernCategoriesAdapter
                val isExpanded = adapter?.isExpanded(category.id) ?: false
                
                // Animate chevron rotation
                chevron.animate()
                    .rotation(if (isExpanded) 180f else 0f)
                    .setDuration(200)
                    .setInterpolator(AccelerateDecelerateInterpolator())
                    .start()
                
                categoryName.text = "${category.label} (${category.children.size})"
            } else {
                // Leaf category styling
                itemView.cardBackgroundColor = ContextCompat.getColorStateList(context, R.color.surface)
                categoryName.setTextColor(ContextCompat.getColor(context, R.color.on_surface))
                categoryName.setTextAppearance(R.style.TextAppearance_Material3_BodyMedium)
                
                chevron.visibility = View.GONE
                categoryName.text = category.label
            }

            // Set click listeners
            itemView.setOnClickListener { onCategorySelected(category) }

            // Add chevron click listener for expand/collapse
            if (category.children.isNotEmpty()) {
                chevron.setOnClickListener {
                    val adapter = bindingAdapter as? ModernCategoriesAdapter
                    adapter?.toggleCategory(category.id)
                }
            }
        }
    }
}

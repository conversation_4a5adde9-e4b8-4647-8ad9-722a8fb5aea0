package com.autoflow.android.ui.search

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.autoflow.android.R
import com.autoflow.android.data.api.Part
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import java.text.NumberFormat
import java.util.*

/** RecyclerView Adapter for displaying search results */
class SearchResultsAdapter(private val onPartClick: (Part) -> Unit) :
        RecyclerView.Adapter<SearchResultsAdapter.PartViewHolder>() {

    private var parts = listOf<Part>()
    private val currencyFormat = NumberFormat.getCurrencyInstance(Locale.US)

    fun updateResults(newParts: List<Part>) {
        parts = newParts
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PartViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_part, parent, false)
        return PartViewHolder(view)
    }

    override fun onBindViewHolder(holder: PartViewHolder, position: Int) {
        holder.bind(parts[position])
    }

    override fun getItemCount(): Int = parts.size

    inner class PartViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val partImage: ImageView = itemView.findViewById(R.id.partImage)
        private val partName: TextView = itemView.findViewById(R.id.partName)
        private val partNumber: TextView = itemView.findViewById(R.id.partNumber)
        private val partBrand: TextView = itemView.findViewById(R.id.partBrand)
        private val partCategory: TextView = itemView.findViewById(R.id.partCategory)
        private val partDescription: TextView = itemView.findViewById(R.id.partDescription)
        private val partPrice: TextView = itemView.findViewById(R.id.partPrice)
        private val stockIndicator: View = itemView.findViewById(R.id.stockIndicator)
        private val stockText: TextView = itemView.findViewById(R.id.stockText)
        private val viewDetailsButton: Button = itemView.findViewById(R.id.viewDetailsButton)

        fun bind(part: Part) {
            // Set part information
            partName.text = part.name
            partNumber.text = "Part #: ${part.partNumber}"
            partBrand.text = part.brand.ifEmpty { "Generic" }
            partCategory.text = part.category.ifEmpty { "Parts" }
            partDescription.text = part.description
            partPrice.text = currencyFormat.format(part.price)

            // Set stock information
            updateStockDisplay(part.stock)

            // Set brand visibility
            partBrand.visibility = if (part.brand.isNotEmpty()) View.VISIBLE else View.GONE
            partCategory.visibility = if (part.category.isNotEmpty()) View.VISIBLE else View.GONE

            // Load part image
            loadPartImage(part.imageUrl)

            // Set click listeners
            itemView.setOnClickListener { onPartClick(part) }

            viewDetailsButton.setOnClickListener { onPartClick(part) }
        }

        private fun updateStockDisplay(stock: Int) {
            when {
                stock > 10 -> {
                    stockIndicator.setBackgroundResource(R.drawable.stock_indicator_green)
                    stockText.text = "$stock in stock"
                    stockText.setTextColor(itemView.context.getColor(R.color.stock_green))
                }
                stock > 0 -> {
                    stockIndicator.setBackgroundResource(R.drawable.stock_indicator_yellow)
                    stockText.text = "Low stock ($stock)"
                    stockText.setTextColor(itemView.context.getColor(R.color.stock_yellow))
                }
                else -> {
                    stockIndicator.setBackgroundResource(R.drawable.stock_indicator_red)
                    stockText.text = "Out of stock"
                    stockText.setTextColor(itemView.context.getColor(R.color.stock_red))
                }
            }
        }

        private fun loadPartImage(imageUrl: String) {
            if (imageUrl.isNotEmpty()) {
                android.util.Log.d("SearchAdapter", "Loading image: $imageUrl")
                try {
                    Glide.with(itemView.context)
                            .load(imageUrl)
                            .placeholder(R.drawable.ic_part_placeholder)
                            .error(R.drawable.ic_part_placeholder)
                            .diskCacheStrategy(DiskCacheStrategy.ALL)
                            .into(partImage)
                } catch (e: Exception) {
                    android.util.Log.e("SearchAdapter", "Error loading image: ${e.message}")
                    partImage.setImageResource(R.drawable.ic_part_placeholder)
                }
            } else {
                android.util.Log.d("SearchAdapter", "No image URL provided, using placeholder")
                partImage.setImageResource(R.drawable.ic_part_placeholder)
            }
        }
    }
}

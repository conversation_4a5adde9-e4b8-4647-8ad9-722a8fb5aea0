package com.autoflow.android.ui.search

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.fragment.app.DialogFragment
import com.autoflow.android.R
import com.google.android.material.dialog.MaterialAlertDialogBuilder

/**
 * Dialog fragment for search filters
 */
class FiltersDialogFragment : DialogFragment() {

    companion object {
        private const val ARG_CURRENT_FILTERS = "current_filters"

        fun newInstance(currentFilters: SearchFilters): FiltersDialogFragment {
            val fragment = FiltersDialogFragment()
            val args = Bundle()
            args.putSerializable(ARG_CURRENT_FILTERS, currentFilters)
            fragment.arguments = args
            return fragment
        }
    }

    interface OnFiltersAppliedListener {
        fun onFiltersApplied(filters: SearchFilters)
    }

    private var listener: OnFiltersAppliedListener? = null
    private var currentFilters = SearchFilters()

    // UI Components
    private lateinit var clearAllFiltersButton: Button
    private lateinit var categoryBrakes: CheckBox
    private lateinit var categoryEngine: CheckBox
    private lateinit var categoryTransmission: CheckBox
    private lateinit var categorySuspension: CheckBox
    private lateinit var categoryElectrical: CheckBox
    private lateinit var brandVW: CheckBox
    private lateinit var brandAudi: CheckBox
    private lateinit var brandBMW: CheckBox
    private lateinit var brandMercedes: CheckBox
    private lateinit var minPriceInput: EditText
    private lateinit var maxPriceInput: EditText
    private lateinit var inStockOnlyCheckbox: CheckBox
    private lateinit var cancelButton: Button
    private lateinit var applyFiltersButton: Button

    fun setOnFiltersAppliedListener(listener: OnFiltersAppliedListener) {
        this.listener = listener
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            currentFilters = it.getSerializable(ARG_CURRENT_FILTERS) as? SearchFilters ?: SearchFilters()
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val view = LayoutInflater.from(requireContext()).inflate(R.layout.dialog_filters, null)
        initializeViews(view)
        setupClickListeners()
        populateCurrentFilters()

        return MaterialAlertDialogBuilder(requireContext())
            .setView(view)
            .create()
    }

    private fun initializeViews(view: View) {
        clearAllFiltersButton = view.findViewById(R.id.clearAllFiltersButton)
        categoryBrakes = view.findViewById(R.id.categoryBrakes)
        categoryEngine = view.findViewById(R.id.categoryEngine)
        categoryTransmission = view.findViewById(R.id.categoryTransmission)
        categorySuspension = view.findViewById(R.id.categorySuspension)
        categoryElectrical = view.findViewById(R.id.categoryElectrical)
        brandVW = view.findViewById(R.id.brandVW)
        brandAudi = view.findViewById(R.id.brandAudi)
        brandBMW = view.findViewById(R.id.brandBMW)
        brandMercedes = view.findViewById(R.id.brandMercedes)
        minPriceInput = view.findViewById(R.id.minPriceInput)
        maxPriceInput = view.findViewById(R.id.maxPriceInput)
        inStockOnlyCheckbox = view.findViewById(R.id.inStockOnlyCheckbox)
        cancelButton = view.findViewById(R.id.cancelButton)
        applyFiltersButton = view.findViewById(R.id.applyFiltersButton)
    }

    private fun setupClickListeners() {
        clearAllFiltersButton.setOnClickListener {
            clearAllFilters()
        }

        cancelButton.setOnClickListener {
            dismiss()
        }

        applyFiltersButton.setOnClickListener {
            applyFilters()
        }
    }

    private fun populateCurrentFilters() {
        // Set category checkboxes
        categoryBrakes.isChecked = currentFilters.categories.contains("Brakes")
        categoryEngine.isChecked = currentFilters.categories.contains("Engine")
        categoryTransmission.isChecked = currentFilters.categories.contains("Transmission")
        categorySuspension.isChecked = currentFilters.categories.contains("Suspension")
        categoryElectrical.isChecked = currentFilters.categories.contains("Electrical")

        // Set brand checkboxes
        brandVW.isChecked = currentFilters.brands.contains("VW")
        brandAudi.isChecked = currentFilters.brands.contains("Audi")
        brandBMW.isChecked = currentFilters.brands.contains("BMW")
        brandMercedes.isChecked = currentFilters.brands.contains("Mercedes-Benz")

        // Set price range
        currentFilters.priceMin?.let { minPriceInput.setText(it.toString()) }
        currentFilters.priceMax?.let { maxPriceInput.setText(it.toString()) }

        // Set stock checkbox
        inStockOnlyCheckbox.isChecked = currentFilters.inStockOnly
    }

    private fun clearAllFilters() {
        // Clear category checkboxes
        categoryBrakes.isChecked = false
        categoryEngine.isChecked = false
        categoryTransmission.isChecked = false
        categorySuspension.isChecked = false
        categoryElectrical.isChecked = false

        // Clear brand checkboxes
        brandVW.isChecked = false
        brandAudi.isChecked = false
        brandBMW.isChecked = false
        brandMercedes.isChecked = false

        // Clear price inputs
        minPriceInput.text.clear()
        maxPriceInput.text.clear()

        // Clear stock checkbox
        inStockOnlyCheckbox.isChecked = false
    }

    private fun applyFilters() {
        val categories = mutableListOf<String>()
        if (categoryBrakes.isChecked) categories.add("Brakes")
        if (categoryEngine.isChecked) categories.add("Engine")
        if (categoryTransmission.isChecked) categories.add("Transmission")
        if (categorySuspension.isChecked) categories.add("Suspension")
        if (categoryElectrical.isChecked) categories.add("Electrical")

        val brands = mutableListOf<String>()
        if (brandVW.isChecked) brands.add("VW")
        if (brandAudi.isChecked) brands.add("Audi")
        if (brandBMW.isChecked) brands.add("BMW")
        if (brandMercedes.isChecked) brands.add("Mercedes-Benz")

        val minPrice = minPriceInput.text.toString().toDoubleOrNull()
        val maxPrice = maxPriceInput.text.toString().toDoubleOrNull()

        val filters = SearchFilters(
            categories = categories,
            brands = brands,
            priceMin = minPrice,
            priceMax = maxPrice,
            inStockOnly = inStockOnlyCheckbox.isChecked
        )

        listener?.onFiltersApplied(filters)
        dismiss()
    }
}

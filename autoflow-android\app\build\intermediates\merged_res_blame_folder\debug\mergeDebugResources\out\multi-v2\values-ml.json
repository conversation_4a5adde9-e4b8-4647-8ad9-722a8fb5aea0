{"logs": [{"outputFile": "com.autoflow.android.app-mergeDebugResources-45:/values-ml/values-ml.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ed7078e8e71893b82f32857a328dfc89\\transformed\\biometric-1.1.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,259,382,510,639,769,905,1046,1148,1307,1453", "endColumns": "114,88,122,127,128,129,135,140,101,158,145,128", "endOffsets": "165,254,377,505,634,764,900,1041,1143,1302,1448,1577"}, "to": {"startLines": "48,49,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4558,4673,4926,5049,5177,5306,5436,5572,5713,5815,5974,6120", "endColumns": "114,88,122,127,128,129,135,140,101,158,145,128", "endOffsets": "4668,4757,5044,5172,5301,5431,5567,5708,5810,5969,6115,6244"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1c5f58891eb309bb4a04ac21b97fad24\\transformed\\core-1.10.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "38,39,40,41,42,43,44,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3525,3627,3730,3832,3936,4039,4140,10557", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "3622,3725,3827,3931,4034,4135,4257,10653"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\303aa64de72ab8a914eac82254eebb45\\transformed\\appcompat-1.6.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,318,429,520,625,747,825,900,991,1084,1185,1279,1379,1473,1568,1667,1758,1849,1931,2040,2144,2243,2355,2467,2588,2753,2854", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "207,313,424,515,620,742,820,895,986,1079,1180,1274,1374,1468,1563,1662,1753,1844,1926,2035,2139,2238,2350,2462,2583,2748,2849,2932"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,428,534,645,736,841,963,1041,1116,1207,1300,1401,1495,1595,1689,1784,1883,1974,2065,2147,2256,2360,2459,2571,2683,2804,2969,10474", "endColumns": "106,105,110,90,104,121,77,74,90,92,100,93,99,93,94,98,90,90,81,108,103,98,111,111,120,164,100,82", "endOffsets": "423,529,640,731,836,958,1036,1111,1202,1295,1396,1490,1590,1684,1779,1878,1969,2060,2142,2251,2355,2454,2566,2678,2799,2964,3065,10552"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd2d40d61e01e6ef2b0f2f90a3e8aca5\\transformed\\material-1.9.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,351,434,521,627,726,820,930,1022,1087,1186,1252,1312,1414,1476,1552,1610,1688,1753,1807,1924,1988,2052,2106,2186,2320,2406,2495,2631,2716,2804,2899,2957,3009,3075,3154,3236,3327,3403,3480,3557,3628,3735,3815,3912,4012,4086,4167,4272,4330,4397,4488,4580,4642,4706,4769,4872,4988,5093,5209,5271,5327", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,79,82,86,105,98,93,109,91,64,98,65,59,101,61,75,57,77,64,53,116,63,63,53,79,133,85,88,135,84,87,94,57,51,65,78,81,90,75,76,76,70,106,79,96,99,73,80,104,57,66,90,91,61,63,62,102,115,104,115,61,55,83", "endOffsets": "266,346,429,516,622,721,815,925,1017,1082,1181,1247,1307,1409,1471,1547,1605,1683,1748,1802,1919,1983,2047,2101,2181,2315,2401,2490,2626,2711,2799,2894,2952,3004,3070,3149,3231,3322,3398,3475,3552,3623,3730,3810,3907,4007,4081,4162,4267,4325,4392,4483,4575,4637,4701,4764,4867,4983,5088,5204,5266,5322,5406"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,50,51,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3070,3150,3233,3320,3426,4262,4356,4466,4762,4827,6249,6315,6375,6477,6539,6615,6673,6751,6816,6870,6987,7051,7115,7169,7249,7383,7469,7558,7694,7779,7867,7962,8020,8072,8138,8217,8299,8390,8466,8543,8620,8691,8798,8878,8975,9075,9149,9230,9335,9393,9460,9551,9643,9705,9769,9832,9935,10051,10156,10272,10334,10390", "endLines": "5,33,34,35,36,37,45,46,47,50,51,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113", "endColumns": "12,79,82,86,105,98,93,109,91,64,98,65,59,101,61,75,57,77,64,53,116,63,63,53,79,133,85,88,135,84,87,94,57,51,65,78,81,90,75,76,76,70,106,79,96,99,73,80,104,57,66,90,91,61,63,62,102,115,104,115,61,55,83", "endOffsets": "316,3145,3228,3315,3421,3520,4351,4461,4553,4822,4921,6310,6370,6472,6534,6610,6668,6746,6811,6865,6982,7046,7110,7164,7244,7378,7464,7553,7689,7774,7862,7957,8015,8067,8133,8212,8294,8385,8461,8538,8615,8686,8793,8873,8970,9070,9144,9225,9330,9388,9455,9546,9638,9700,9764,9827,9930,10046,10151,10267,10329,10385,10469"}}]}]}
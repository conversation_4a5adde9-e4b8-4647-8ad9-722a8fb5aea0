package com.autoflow.android.ui.storage

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.autoflow.android.data.api.ApiResult
import com.autoflow.android.data.repositories.AddPartRepository
import com.autoflow.android.domain.models.StorageArea
import com.autoflow.android.domain.models.StorageLocationFormData
import com.autoflow.android.domain.models.StorageUnit
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * ViewModel for Storage Location selection
 * Handles areas, units, and location saving
 */
class StorageLocationViewModel(
    private val repository: AddPartRepository
) : ViewModel() {
    
    // Storage areas and units
    private val _areas = MutableStateFlow<List<StorageArea>>(emptyList())
    val areas: StateFlow<List<StorageArea>> = _areas.asStateFlow()
    
    private val _units = MutableStateFlow<List<StorageUnit>>(emptyList())
    val units: StateFlow<List<StorageUnit>> = _units.asStateFlow()
    
    private val _filteredUnits = MutableStateFlow<List<StorageUnit>>(emptyList())
    val filteredUnits: StateFlow<List<StorageUnit>> = _filteredUnits.asStateFlow()
    
    // Selection state
    private val _selectedArea = MutableStateFlow<StorageArea?>(null)
    val selectedArea: StateFlow<StorageArea?> = _selectedArea.asStateFlow()
    
    private val _selectedUnit = MutableStateFlow<StorageUnit?>(null)
    val selectedUnit: StateFlow<StorageUnit?> = _selectedUnit.asStateFlow()
    
    private val _selectedSubtype = MutableStateFlow<String?>(null)
    val selectedSubtype: StateFlow<String?> = _selectedSubtype.asStateFlow()
    
    // UI state
    private val _uiState = MutableStateFlow(StorageLocationUiState())
    val uiState: StateFlow<StorageLocationUiState> = _uiState.asStateFlow()
    
    init {
        loadAreasAndUnits()
    }
    
    private fun loadAreasAndUnits() {
        viewModelScope.launch {
            updateUiState { it.copy(isLoading = true) }
            
            // Load areas
            when (val areasResult = repository.getStorageAreas()) {
                is ApiResult.Success -> {
                    _areas.value = areasResult.data
                }
                is ApiResult.Error -> {
                    updateUiState { 
                        it.copy(
                            isLoading = false,
                            error = "Failed to load storage areas: ${areasResult.message}"
                        )
                    }
                    return@launch
                }
                is ApiResult.Loading -> {
                    // Already handled above
                }
            }
            
            // Load all units
            when (val unitsResult = repository.getStorageUnits()) {
                is ApiResult.Success -> {
                    _units.value = unitsResult.data
                    _filteredUnits.value = emptyList() // Start with empty until area is selected
                    updateUiState { it.copy(isLoading = false) }
                }
                is ApiResult.Error -> {
                    updateUiState { 
                        it.copy(
                            isLoading = false,
                            error = "Failed to load storage units: ${unitsResult.message}"
                        )
                    }
                }
                is ApiResult.Loading -> {
                    // Already handled above
                }
            }
        }
    }
    
    fun selectArea(area: StorageArea) {
        _selectedArea.value = area
        
        // Filter units by selected area
        val filteredUnits = _units.value.filter { unit ->
            unit.areaId == area.id
        }
        _filteredUnits.value = filteredUnits
        
        // Clear unit selection when area changes
        _selectedUnit.value = null
    }
    
    fun selectUnit(unit: StorageUnit) {
        _selectedUnit.value = unit
    }
    
    fun selectSubtype(subtype: String) {
        _selectedSubtype.value = subtype
    }
    
    fun saveLocation(partId: Int, locationData: StorageLocationFormData) {
        viewModelScope.launch {
            updateUiState { it.copy(isLoading = true, error = null) }
            
            when (val result = repository.savePartLocation(partId, locationData)) {
                is ApiResult.Success -> {
                    updateUiState { 
                        it.copy(
                            isLoading = false,
                            isSuccess = true
                        )
                    }
                }
                is ApiResult.Error -> {
                    updateUiState { 
                        it.copy(
                            isLoading = false,
                            error = "Failed to save location: ${result.message}"
                        )
                    }
                }
                is ApiResult.Loading -> {
                    // Already handled above
                }
            }
        }
    }
    
    fun clearError() {
        updateUiState { it.copy(error = null) }
    }
    
    private fun updateUiState(update: (StorageLocationUiState) -> StorageLocationUiState) {
        _uiState.value = update(_uiState.value)
    }
}

/**
 * UI state for Storage Location screen
 */
data class StorageLocationUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val isSuccess: Boolean = false
)

package com.autoflow.android.domain.models

/**
 * Vehicle models for cascading vehicle selection
 * Mirrors web app's vehicle hierarchy: Brand → Model → Generation → Variation → Trim
 */

data class CarBrand(
    val id: Int,
    val name: String
) {
    override fun toString(): String = name
}

data class CarModel(
    val id: Int,
    val brandId: Int,
    val name: String
) {
    override fun toString(): String = name
}

data class CarGeneration(
    val id: Int,
    val modelId: Int,
    val name: String,
    val years: String = ""
) {
    override fun toString(): String = if (years.isNotBlank()) "$name ($years)" else name
}

data class CarVariation(
    val id: Int,
    val generationId: Int,
    val name: String
) {
    override fun toString(): String = name
}

data class CarTrim(
    val id: Int,
    val variationId: Int,
    val name: String,
    val engineCode: String = "",
    val fuelType: String = "",
    val transmission: String = ""
) {
    override fun toString(): String = name
}

/**
 * Vehicle compatibility data from AI analysis
 */
data class VehicleCompatibility(
    val brand: String,
    val model: String,
    val generation: String,
    val variation: String = "",
    val trim: String = "",
    val years: String = ""
)

/**
 * Engine compatibility data from AI analysis
 */
data class EngineCompatibility(
    val engineCode: String,
    val engineCapacity: String = "",
    val fuelType: String = "",
    val engineType: String = "",
    val power: String = "",
    val torque: String = ""
)

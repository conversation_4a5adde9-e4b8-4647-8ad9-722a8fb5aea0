#Wed Aug 20 03:28:15 EAT 2025
com.autoflow.android.app-main-5\:/anim/bounce_in.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\bounce_in.xml
com.autoflow.android.app-main-5\:/anim/fade_in.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\fade_in.xml
com.autoflow.android.app-main-5\:/anim/shake.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\shake.xml
com.autoflow.android.app-main-5\:/anim/slide_up.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_up.xml
com.autoflow.android.app-main-5\:/drawable/button_filled_mustard.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_filled_mustard.xml
com.autoflow.android.app-main-5\:/drawable/button_filled_teal.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_filled_teal.xml
com.autoflow.android.app-main-5\:/drawable/button_outline_mustard.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_outline_mustard.xml
com.autoflow.android.app-main-5\:/drawable/button_outline_teal.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_outline_teal.xml
com.autoflow.android.app-main-5\:/drawable/button_text_red.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_text_red.xml
com.autoflow.android.app-main-5\:/drawable/button_text_secondary.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_text_secondary.xml
com.autoflow.android.app-main-5\:/drawable/capture_button_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\capture_button_background.xml
com.autoflow.android.app-main-5\:/drawable/card_background_mustard.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\card_background_mustard.xml
com.autoflow.android.app-main-5\:/drawable/card_background_teal.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\card_background_teal.xml
com.autoflow.android.app-main-5\:/drawable/card_background_white.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\card_background_white.xml
com.autoflow.android.app-main-5\:/drawable/chip_background_mustard.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\chip_background_mustard.xml
com.autoflow.android.app-main-5\:/drawable/chip_background_teal.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\chip_background_teal.xml
com.autoflow.android.app-main-5\:/drawable/circle_button_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_button_background.xml
com.autoflow.android.app-main-5\:/drawable/circle_light_purple.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_light_purple.xml
com.autoflow.android.app-main-5\:/drawable/circle_purple.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_purple.xml
com.autoflow.android.app-main-5\:/drawable/dashboard_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\dashboard_background.xml
com.autoflow.android.app-main-5\:/drawable/dialog_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\dialog_background.xml
com.autoflow.android.app-main-5\:/drawable/dropdown_item_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\dropdown_item_background.xml
com.autoflow.android.app-main-5\:/drawable/dropdown_parent_item_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\dropdown_parent_item_background.xml
com.autoflow.android.app-main-5\:/drawable/floating_button_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\floating_button_background.xml
com.autoflow.android.app-main-5\:/drawable/floating_share_button_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\floating_share_button_background.xml
com.autoflow.android.app-main-5\:/drawable/header_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\header_background.xml
com.autoflow.android.app-main-5\:/drawable/ic_add.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_add.xml
com.autoflow.android.app-main-5\:/drawable/ic_arrow_back.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_arrow_back.xml
com.autoflow.android.app-main-5\:/drawable/ic_autoflow_logo.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_autoflow_logo.xml
com.autoflow.android.app-main-5\:/drawable/ic_camera.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_camera.xml
com.autoflow.android.app-main-5\:/drawable/ic_car_placeholder.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_car_placeholder.xml
com.autoflow.android.app-main-5\:/drawable/ic_categories.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_categories.xml
com.autoflow.android.app-main-5\:/drawable/ic_category.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_category.xml
com.autoflow.android.app-main-5\:/drawable/ic_check_circle.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_check_circle.xml
com.autoflow.android.app-main-5\:/drawable/ic_clear.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_clear.xml
com.autoflow.android.app-main-5\:/drawable/ic_close_small.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_close_small.xml
com.autoflow.android.app-main-5\:/drawable/ic_delete.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_delete.xml
com.autoflow.android.app-main-5\:/drawable/ic_edit.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_edit.xml
com.autoflow.android.app-main-5\:/drawable/ic_email.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_email.xml
com.autoflow.android.app-main-5\:/drawable/ic_error.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_error.xml
com.autoflow.android.app-main-5\:/drawable/ic_expand_more.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_expand_more.xml
com.autoflow.android.app-main-5\:/drawable/ic_filter.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_filter.xml
com.autoflow.android.app-main-5\:/drawable/ic_fingerprint.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_fingerprint.xml
com.autoflow.android.app-main-5\:/drawable/ic_history.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_history.xml
com.autoflow.android.app-main-5\:/drawable/ic_home.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_home.xml
com.autoflow.android.app-main-5\:/drawable/ic_image.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_image.xml
com.autoflow.android.app-main-5\:/drawable/ic_launcher.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher.xml
com.autoflow.android.app-main-5\:/drawable/ic_logout.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_logout.xml
com.autoflow.android.app-main-5\:/drawable/ic_menu.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_menu.xml
com.autoflow.android.app-main-5\:/drawable/ic_more_vert.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_more_vert.xml
com.autoflow.android.app-main-5\:/drawable/ic_part_placeholder.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_part_placeholder.xml
com.autoflow.android.app-main-5\:/drawable/ic_person.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_person.xml
com.autoflow.android.app-main-5\:/drawable/ic_search.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_search.xml
com.autoflow.android.app-main-5\:/drawable/ic_search_empty.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_search_empty.xml
com.autoflow.android.app-main-5\:/drawable/ic_settings.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_settings.xml
com.autoflow.android.app-main-5\:/drawable/ic_share.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_share.xml
com.autoflow.android.app-main-5\:/drawable/ic_sort.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_sort.xml
com.autoflow.android.app-main-5\:/drawable/ic_trending.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_trending.xml
com.autoflow.android.app-main-5\:/drawable/image_placeholder_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\image_placeholder_background.xml
com.autoflow.android.app-main-5\:/drawable/info_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\info_background.xml
com.autoflow.android.app-main-5\:/drawable/input_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\input_background.xml
com.autoflow.android.app-main-5\:/drawable/login_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\login_background.xml
com.autoflow.android.app-main-5\:/drawable/logo_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\logo_background.xml
com.autoflow.android.app-main-5\:/drawable/otp_digit_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\otp_digit_background.xml
com.autoflow.android.app-main-5\:/drawable/otp_illustration.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\otp_illustration.xml
com.autoflow.android.app-main-5\:/drawable/otp_illustration_simple.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\otp_illustration_simple.xml
com.autoflow.android.app-main-5\:/drawable/search_input_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\search_input_background.xml
com.autoflow.android.app-main-5\:/drawable/spinner_background.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\spinner_background.xml
com.autoflow.android.app-main-5\:/drawable/stock_indicator_green.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\stock_indicator_green.xml
com.autoflow.android.app-main-5\:/drawable/stock_indicator_red.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\stock_indicator_red.xml
com.autoflow.android.app-main-5\:/drawable/stock_indicator_yellow.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\stock_indicator_yellow.xml
com.autoflow.android.app-main-5\:/layout-land/activity_otp.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout-land\\activity_otp.xml
com.autoflow.android.app-main-5\:/layout/activity_add_part.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_add_part.xml
com.autoflow.android.app-main-5\:/layout/activity_camera_capture.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_camera_capture.xml
com.autoflow.android.app-main-5\:/layout/activity_category_selection.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_category_selection.xml
com.autoflow.android.app-main-5\:/layout/activity_dashboard.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_dashboard.xml
com.autoflow.android.app-main-5\:/layout/activity_image_crop.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_image_crop.xml
com.autoflow.android.app-main-5\:/layout/activity_login.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_login.xml
com.autoflow.android.app-main-5\:/layout/activity_otp.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_otp.xml
com.autoflow.android.app-main-5\:/layout/activity_part_detail.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_part_detail.xml
com.autoflow.android.app-main-5\:/layout/activity_part_update.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_part_update.xml
com.autoflow.android.app-main-5\:/layout/activity_search_results.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_search_results.xml
com.autoflow.android.app-main-5\:/layout/content_dashboard.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\content_dashboard.xml
com.autoflow.android.app-main-5\:/layout/dialog_filters.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_filters.xml
com.autoflow.android.app-main-5\:/layout/dialog_sort.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_sort.xml
com.autoflow.android.app-main-5\:/layout/footer_backend.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\footer_backend.xml
com.autoflow.android.app-main-5\:/layout/fragment_alternative_parts.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_alternative_parts.xml
com.autoflow.android.app-main-5\:/layout/fragment_basic_info.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_basic_info.xml
com.autoflow.android.app-main-5\:/layout/fragment_category.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_category.xml
com.autoflow.android.app-main-5\:/layout/fragment_compatible_vehicles.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_compatible_vehicles.xml
com.autoflow.android.app-main-5\:/layout/fragment_images.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_images.xml
com.autoflow.android.app-main-5\:/layout/fragment_pricing_inventory.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_pricing_inventory.xml
com.autoflow.android.app-main-5\:/layout/header_backend.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\header_backend.xml
com.autoflow.android.app-main-5\:/layout/item_category.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_category.xml
com.autoflow.android.app-main-5\:/layout/item_part.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_part.xml
com.autoflow.android.app-main-5\:/layout/item_part_image.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_part_image.xml
com.autoflow.android.app-main-5\:/layout/item_search_suggestion.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_search_suggestion.xml
com.autoflow.android.app-main-5\:/layout/layout_search_with_suggestions.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\layout_search_with_suggestions.xml
com.autoflow.android.app-main-5\:/menu/part_detail_menu.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\part_detail_menu.xml
com.autoflow.android.app-main-5\:/xml/file_paths.xml=C\:\\Users\\home\\Node\\autoflow\\autoflow-android\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\file_paths.xml

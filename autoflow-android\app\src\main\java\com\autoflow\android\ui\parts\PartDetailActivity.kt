package com.autoflow.android.ui.parts

import android.content.Intent
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.View
import android.widget.*
import androidx.appcompat.widget.PopupMenu
import androidx.core.content.FileProvider
import com.autoflow.android.R
import com.autoflow.android.ui.base.BaseBackendActivity
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import java.io.File
import java.io.FileOutputStream
import java.text.NumberFormat

class PartDetailActivity : BaseBackendActivity() {

    companion object {
        const val EXTRA_PART_ID = "extra_part_id"
        const val EXTRA_PART_NAME = "extra_part_name"
        const val EXTRA_PART_NUMBER = "extra_part_number"
        const val EXTRA_PART_DESCRIPTION = "extra_part_description"
        const val EXTRA_PART_PRICE = "extra_part_price"
        const val EXTRA_PART_STOCK = "extra_part_stock"
        const val EXTRA_PART_BRAND = "extra_part_brand"
        const val EXTRA_PART_CATEGORY = "extra_part_category"
        const val EXTRA_PART_IMAGE_URL = "extra_part_image_url"
    }

    private lateinit var partImage: ImageView
    private lateinit var partName: TextView
    private lateinit var partNumber: TextView
    private lateinit var partDescription: TextView
    private lateinit var partPrice: TextView
    private lateinit var partStock: TextView
    private lateinit var partBrand: TextView
    private lateinit var partCategory: TextView
    private lateinit var stockIndicator: View
    private lateinit var contactSellerButton: Button
    private lateinit var addToCartButton: Button
    private lateinit var shareButton: ImageButton
    private lateinit var menuButton: ImageButton

    // Part data for sharing and menu operations
    private var currentPartId: String = ""
    private var currentPartName: String = ""
    private var currentPartImageUrl: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setBackendContentView(R.layout.activity_part_detail)

        initializeViews()
        loadPartData()
        setupClickListeners()
    }

    private fun initializeViews() {
        partImage = findViewById(R.id.partImage)
        partName = findViewById(R.id.partName)
        partNumber = findViewById(R.id.partNumber)
        partDescription = findViewById(R.id.partDescription)
        partPrice = findViewById(R.id.partPrice)
        partStock = findViewById(R.id.partStock)
        partBrand = findViewById(R.id.partBrand)
        partCategory = findViewById(R.id.partCategory)
        stockIndicator = findViewById(R.id.stockIndicator)
        contactSellerButton = findViewById(R.id.contactSellerButton)
        addToCartButton = findViewById(R.id.addToCartButton)
        shareButton = findViewById(R.id.shareButton)
        menuButton = findViewById(R.id.menuButton)
    }

    private fun loadPartData() {
        // Store current part data for sharing and menu operations
        currentPartId = intent.getStringExtra(EXTRA_PART_ID) ?: ""
        currentPartName = intent.getStringExtra(EXTRA_PART_NAME) ?: "Unknown Part"
        currentPartImageUrl = intent.getStringExtra(EXTRA_PART_IMAGE_URL) ?: ""

        val partNumber = intent.getStringExtra(EXTRA_PART_NUMBER) ?: "N/A"
        val partDescription = intent.getStringExtra(EXTRA_PART_DESCRIPTION) ?: ""
        val partPrice = intent.getDoubleExtra(EXTRA_PART_PRICE, 0.0)
        val partStock = intent.getIntExtra(EXTRA_PART_STOCK, 0)
        val partBrand = intent.getStringExtra(EXTRA_PART_BRAND) ?: ""
        val partCategory = intent.getStringExtra(EXTRA_PART_CATEGORY) ?: ""

        // Set part information
        this.partName.text = currentPartName
        this.partNumber.text = "Part #: $partNumber"
        this.partDescription.text =
                if (partDescription.isNotEmpty()) partDescription else "No description available"
        this.partPrice.text = NumberFormat.getCurrencyInstance().format(partPrice)
        this.partStock.text = "$partStock in stock"

        // Set brand and category
        if (partBrand.isNotEmpty()) {
            this.partBrand.text = partBrand
            this.partBrand.visibility = android.view.View.VISIBLE
        } else {
            this.partBrand.visibility = android.view.View.GONE
        }

        if (partCategory.isNotEmpty()) {
            this.partCategory.text = partCategory
            this.partCategory.visibility = android.view.View.VISIBLE
        } else {
            this.partCategory.visibility = android.view.View.GONE
        }

        // Update stock indicator
        updateStockIndicator(partStock)

        // Load part image
        loadPartImage(currentPartImageUrl)
    }

    private fun loadPartImage(imageUrl: String) {
        if (imageUrl.isNotEmpty()) {
            Glide.with(this)
                    .load(imageUrl)
                    .placeholder(R.drawable.ic_part_placeholder)
                    .error(R.drawable.ic_part_placeholder)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .into(partImage)
        } else {
            partImage.setImageResource(R.drawable.ic_part_placeholder)
        }
    }

    private fun updateStockIndicator(stock: Int) {
        when {
            stock > 10 -> {
                stockIndicator.setBackgroundResource(R.drawable.stock_indicator_green)
                partStock.setTextColor(getColor(R.color.stock_green))
            }
            stock > 0 -> {
                stockIndicator.setBackgroundResource(R.drawable.stock_indicator_yellow)
                partStock.setTextColor(getColor(R.color.stock_yellow))
            }
            else -> {
                stockIndicator.setBackgroundResource(R.drawable.stock_indicator_red)
                partStock.setTextColor(getColor(R.color.stock_red))
                partStock.text = "Out of stock"
            }
        }
    }

    private fun setupClickListeners() {
        contactSellerButton.setOnClickListener {
            // TODO: Implement contact seller functionality
            Toast.makeText(this, "Contact seller feature coming soon", Toast.LENGTH_SHORT).show()
        }

        addToCartButton.setOnClickListener {
            // TODO: Implement add to cart functionality
            Toast.makeText(this, "Add to cart feature coming soon", Toast.LENGTH_SHORT).show()
        }

        shareButton.setOnClickListener { sharePartOnWhatsApp() }

        menuButton.setOnClickListener { showPartMenu() }
    }

    override fun onHamburgerMenuClick() {
        Toast.makeText(this, "Menu clicked", Toast.LENGTH_SHORT).show()
    }

    override fun onCategoriesMenuClick() {
        Toast.makeText(this, "Categories clicked", Toast.LENGTH_SHORT).show()
    }

    private fun sharePartOnWhatsApp() {
        if (currentPartImageUrl.isNotEmpty()) {
            // Load the image and share it
            Glide.with(this)
                    .asBitmap()
                    .load(currentPartImageUrl)
                    .into(
                            object : CustomTarget<Bitmap>() {
                                override fun onResourceReady(
                                        resource: Bitmap,
                                        transition: Transition<in Bitmap>?
                                ) {
                                    shareImageWithText(resource)
                                }

                                override fun onLoadCleared(placeholder: Drawable?) {
                                    // Share text only if image fails to load
                                    shareTextOnly()
                                }
                            }
                    )
        } else {
            // No image available, share text only
            shareTextOnly()
        }
    }

    private fun shareImageWithText(bitmap: Bitmap) {
        try {
            // Save bitmap to cache directory
            val cachePath = File(cacheDir, "images")
            cachePath.mkdirs()
            val file = File(cachePath, "shared_part_image.jpg")

            val fileOutputStream = FileOutputStream(file)
            bitmap.compress(Bitmap.CompressFormat.JPEG, 90, fileOutputStream)
            fileOutputStream.close()

            // Get URI for the file
            val imageUri = FileProvider.getUriForFile(this, "${packageName}.fileprovider", file)

            val shareText = buildString {
                appendLine("🔧 *${currentPartName}*")
                appendLine()
                appendLine("💰 Price: ${partPrice.text}")
                appendLine("📦 Stock: ${partStock.text}")
                if (partBrand.text.isNotEmpty()) {
                    appendLine("🏷️ Brand: ${partBrand.text}")
                }
                if (partCategory.text.isNotEmpty()) {
                    appendLine("📂 Category: ${partCategory.text}")
                }
                appendLine()
                appendLine("🛒 Get this part from Autoflow!")
                appendLine("https://autoflow.parts/parts/${currentPartId}")
            }

            val whatsappIntent =
                    Intent(Intent.ACTION_SEND).apply {
                        type = "image/*"
                        setPackage("com.whatsapp")
                        putExtra(Intent.EXTRA_STREAM, imageUri)
                        putExtra(Intent.EXTRA_TEXT, shareText)
                        addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    }

            if (whatsappIntent.resolveActivity(packageManager) != null) {
                startActivity(whatsappIntent)
            } else {
                // WhatsApp not installed, use generic share
                val genericShareIntent =
                        Intent(Intent.ACTION_SEND).apply {
                            type = "image/*"
                            putExtra(Intent.EXTRA_STREAM, imageUri)
                            putExtra(Intent.EXTRA_TEXT, shareText)
                            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                        }
                startActivity(Intent.createChooser(genericShareIntent, "Share part"))
            }
        } catch (e: Exception) {
            Toast.makeText(this, "Error sharing image", Toast.LENGTH_SHORT).show()
            shareTextOnly()
        }
    }

    private fun shareTextOnly() {
        try {
            val shareText = buildString {
                appendLine("🔧 *${currentPartName}*")
                appendLine()
                appendLine("💰 Price: ${partPrice.text}")
                appendLine("📦 Stock: ${partStock.text}")
                if (partBrand.text.isNotEmpty()) {
                    appendLine("🏷️ Brand: ${partBrand.text}")
                }
                if (partCategory.text.isNotEmpty()) {
                    appendLine("📂 Category: ${partCategory.text}")
                }
                appendLine()
                appendLine("🛒 Get this part from Autoflow!")
                appendLine("https://autoflow.parts/parts/${currentPartId}")
            }

            val whatsappIntent =
                    Intent(Intent.ACTION_SEND).apply {
                        type = "text/plain"
                        setPackage("com.whatsapp")
                        putExtra(Intent.EXTRA_TEXT, shareText)
                    }

            if (whatsappIntent.resolveActivity(packageManager) != null) {
                startActivity(whatsappIntent)
            } else {
                // WhatsApp not installed, use generic share
                val genericShareIntent =
                        Intent(Intent.ACTION_SEND).apply {
                            type = "text/plain"
                            putExtra(Intent.EXTRA_TEXT, shareText)
                            putExtra(Intent.EXTRA_SUBJECT, "Check out this auto part!")
                        }
                startActivity(Intent.createChooser(genericShareIntent, "Share part"))
            }
        } catch (e: Exception) {
            Toast.makeText(this, "Error sharing part", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showPartMenu() {
        val popup = PopupMenu(this, menuButton)
        popup.menuInflater.inflate(R.menu.part_detail_menu, popup.menu)

        popup.setOnMenuItemClickListener { menuItem ->
            when (menuItem.itemId) {
                R.id.menu_update_details -> {
                    updatePartDetails()
                    true
                }
                R.id.menu_manage_images -> {
                    managePartImages()
                    true
                }
                R.id.menu_delete_part -> {
                    deletePartConfirmation()
                    true
                }
                else -> false
            }
        }

        popup.show()
    }

    private fun updatePartDetails() {
        val intent =
                Intent(this, PartUpdateActivity::class.java).apply {
                    putExtra(PartUpdateActivity.EXTRA_PART_ID, currentPartId)
                    putExtra(PartUpdateActivity.EXTRA_PART_NAME, currentPartName)
                    putExtra(
                            PartUpdateActivity.EXTRA_PART_NUMBER,
                            intent.getStringExtra(EXTRA_PART_NUMBER)
                    )
                    putExtra(
                            PartUpdateActivity.EXTRA_PART_DESCRIPTION,
                            intent.getStringExtra(EXTRA_PART_DESCRIPTION)
                    )
                    putExtra(
                            PartUpdateActivity.EXTRA_PART_PRICE,
                            intent.getDoubleExtra(EXTRA_PART_PRICE, 0.0)
                    )
                    putExtra(
                            PartUpdateActivity.EXTRA_PART_STOCK,
                            intent.getIntExtra(EXTRA_PART_STOCK, 0)
                    )
                    putExtra(
                            PartUpdateActivity.EXTRA_PART_BRAND,
                            intent.getStringExtra(EXTRA_PART_BRAND)
                    )
                    putExtra(
                            PartUpdateActivity.EXTRA_PART_CATEGORY,
                            intent.getStringExtra(EXTRA_PART_CATEGORY)
                    )
                }
        startActivity(intent)
    }

    private fun managePartImages() {
        // TODO: Navigate to image management screen
        Toast.makeText(this, "Manage images feature coming soon", Toast.LENGTH_SHORT).show()
    }

    private fun deletePartConfirmation() {
        android.app.AlertDialog.Builder(this)
                .setTitle("Delete Part")
                .setMessage(
                        "Are you sure you want to delete this part? This action cannot be undone."
                )
                .setPositiveButton("Delete") { _, _ -> deletePart() }
                .setNegativeButton("Cancel", null)
                .setIcon(R.drawable.ic_delete)
                .show()
    }

    private fun deletePart() {
        // TODO: Implement actual part deletion API call
        Toast.makeText(this, "Part deletion feature coming soon", Toast.LENGTH_SHORT).show()

        // For now, just show success message and go back
        // In real implementation, make API call to delete part
        // if (deleteSuccess) {
        //     Toast.makeText(this, "Part deleted successfully", Toast.LENGTH_SHORT).show()
        //     finish()
        // }
    }
}

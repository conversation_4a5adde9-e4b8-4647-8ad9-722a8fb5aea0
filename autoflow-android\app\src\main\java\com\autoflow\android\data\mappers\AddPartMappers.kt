package com.autoflow.android.data.mappers

import com.autoflow.android.data.api.dto.*
import com.autoflow.android.domain.models.*

fun List<CategoryDto>.toCategoryTree(): List<CategoryResponse> {
    val byParent = this.groupBy { it.parent }
    fun build(parentId: Int?): List<CategoryResponse> {
        return (byParent[parentId] ?: emptyList()).sortedBy { it.label }.map { dto ->
            CategoryResponse(
                    id = dto.id,
                    label = dto.label,
                    parentCategoryId = dto.parent,
                    children = build(dto.id),
                    requiresPartNumber = dto.requirePartNumber,
                    hasAttributes = false
            )
        }
    }
    return build(null)
}

fun CompatibilityDto.toDomain(requestedPartNumber: String) =
        CompatibilityData(
                partName = partName,
                partNumber = requestedPartNumber,
                compatiblePartNumbers = compatiblePartNumbers,
                engineCompatibility =
                        engineCompatibility.map { dto ->
                            EngineCompatibility(
                                    engineCode = dto.engineCode,
                                    engineCapacity = dto.engineCapacity ?: "",
                                    fuelType = dto.fuelType ?: "",
                                    engineType = dto.engineType ?: ""
                            )
                        },
                vehicleCompatibility =
                        vehicleCompatibility.flatMap { v ->
                            val trims = v.trims ?: emptyList()
                            if (trims.isEmpty())
                                    listOf(
                                            VehicleCompatibility(
                                                    brand = v.brand,
                                                    model = v.model,
                                                    generation = v.generation,
                                                    variation = v.variation ?: "",
                                                    trim = "",
                                                    years = ""
                                            )
                                    )
                            else
                                    trims.map { trimName ->
                                        VehicleCompatibility(
                                                brand = v.brand,
                                                model = v.model,
                                                generation = v.generation,
                                                variation = v.variation ?: "",
                                                trim = trimName,
                                                years = ""
                                        )
                                    }
                        },
                partnumberGroup = partnumberGroup?.toInt()
        )

fun AttributeDto.toDomain(): CategoryAttribute =
        CategoryAttribute(
                id = id,
                attribute = attribute,
                inputType = inputType,
                isRequired = isRequired,
                dependsOnAttributeId = dependsOnAttributeId,
                dependsOnValue = dependsOnValue,
                options =
                        (options ?: emptyList()).map { opt ->
                            AttributeOption(id = opt.id, value = opt.value, attributeId = id)
                        }
        )

package com.autoflow.android.ui.addpart.fragments

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import com.autoflow.android.domain.models.CategoryAttribute
import com.autoflow.android.domain.models.CategoryAttributeValue
import com.autoflow.android.ui.addpart.AddPartViewModel
import kotlinx.coroutines.launch

/**
 * Attributes Fragment - Step 4 of Add Part flow Dynamic form generation based on category
 * attributes Mirrors web app's dynamic attribute handling
 */
class AttributesFragment : Fragment() {

    companion object {
        private const val TAG = "AttributesFragment"
    }

    private val viewModel: AddPartViewModel by activityViewModels()

    // UI Components
    private lateinit var instructionText: TextView
    private lateinit var attributesContainer: LinearLayout
    private lateinit var noAttributesText: TextView

    // Dynamic form fields
    private val attributeViews = mutableMapOf<Int, View>()
    private val attributeValues = mutableMapOf<Int, String>()

    override fun onCreateView(
            inflater: LayoutInflater,
            container: ViewGroup?,
            savedInstanceState: Bundle?
    ): View? {
        return createLayout()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        observeViewModel()
    }

    private fun createLayout(): ScrollView {
        val scrollView = ScrollView(requireContext())

        val mainLayout =
                LinearLayout(requireContext()).apply {
                    orientation = LinearLayout.VERTICAL
                    setPadding(16, 16, 16, 16)
                }

        // Instructions
        instructionText =
                TextView(requireContext()).apply {
                    text = "Fill in the attributes for this part category"
                    textSize = 16f
                    setPadding(0, 0, 0, 16)
                }

        // Attributes container (dynamic content)
        attributesContainer =
                LinearLayout(requireContext()).apply { orientation = LinearLayout.VERTICAL }

        // No attributes message
        noAttributesText =
                TextView(requireContext()).apply {
                    text = "No additional attributes required for this category"
                    textSize = 14f
                    setPadding(0, 16, 0, 16)
                    visibility = View.GONE
                }

        // Add all views
        mainLayout.addView(instructionText)
        mainLayout.addView(attributesContainer)
        mainLayout.addView(noAttributesText)

        scrollView.addView(mainLayout)
        return scrollView
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.categoryAttributes.collect { attributes ->
                generateAttributeFields(attributes)
            }
        }

        lifecycleScope.launch {
            viewModel.formState.collect { formState ->
                updateInstructions(formState.selectedCategory)
            }
        }
    }

    private fun updateInstructions(categoryName: String) {
        if (categoryName.isNotBlank()) {
            instructionText.text = "Fill in the attributes for $categoryName"
        }
    }

    private fun generateAttributeFields(attributes: List<CategoryAttribute>) {
        // Clear existing views
        attributesContainer.removeAllViews()
        attributeViews.clear()
        attributeValues.clear()

        if (attributes.isEmpty()) {
            noAttributesText.visibility = View.VISIBLE
            return
        } else {
            noAttributesText.visibility = View.GONE
        }

        // Generate fields for each attribute
        attributes.forEach { attribute ->
            val fieldView = createAttributeField(attribute)
            attributesContainer.addView(fieldView)
            attributeViews[attribute.id] = fieldView
        }
    }

    private fun createAttributeField(attribute: CategoryAttribute): LinearLayout {
        val fieldContainer =
                LinearLayout(requireContext()).apply {
                    orientation = LinearLayout.VERTICAL
                    setPadding(0, 0, 0, 16)
                }

        // Label
        val label =
                TextView(requireContext()).apply {
                    text = attribute.attribute + if (attribute.isRequired) " *" else ""
                    textSize = 16f
                    setTypeface(null, android.graphics.Typeface.BOLD)
                    setPadding(0, 0, 0, 8)
                }
        fieldContainer.addView(label)

        // Input field based on type
        val inputView =
                when (attribute.inputType.lowercase()) {
                    "text" -> createTextInput(attribute)
                    "select" -> createSelectInput(attribute)
                    "radio" -> createRadioInput(attribute)
                    "checkbox" -> createCheckboxInput(attribute)
                    else -> createTextInput(attribute) // Default to text
                }

        fieldContainer.addView(inputView)

        // Handle dependencies
        if (attribute.dependsOnAttributeId != null) {
            fieldContainer.visibility = View.GONE // Initially hidden
            // TODO: Implement dependency logic
        }

        return fieldContainer
    }

    private fun createTextInput(attribute: CategoryAttribute): EditText {
        return EditText(requireContext()).apply {
            hint = "Enter ${attribute.attribute.lowercase()}"
            setPadding(16, 16, 16, 16)

            addTextChangedListener(
                    object : TextWatcher {
                        override fun beforeTextChanged(
                                s: CharSequence?,
                                start: Int,
                                count: Int,
                                after: Int
                        ) {}
                        override fun onTextChanged(
                                s: CharSequence?,
                                start: Int,
                                before: Int,
                                count: Int
                        ) {}
                        override fun afterTextChanged(s: Editable?) {
                            attributeValues[attribute.id] = s.toString()
                            updateFormState()
                        }
                    }
            )
        }
    }

    private fun createSelectInput(attribute: CategoryAttribute): Spinner {
        val spinner = Spinner(requireContext())

        val options = mutableListOf("Select ${attribute.attribute.lowercase()}")
        options.addAll(attribute.options.map { it.value })

        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, options)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinner.adapter = adapter

        spinner.onItemSelectedListener =
                object : AdapterView.OnItemSelectedListener {
                    override fun onItemSelected(
                            parent: AdapterView<*>?,
                            view: View?,
                            position: Int,
                            id: Long
                    ) {
                        if (position > 0) {
                            val selectedOption = attribute.options[position - 1]
                            attributeValues[attribute.id] = selectedOption.value
                            updateFormState()
                        }
                    }
                    override fun onNothingSelected(parent: AdapterView<*>?) {}
                }

        return spinner
    }

    private fun createRadioInput(attribute: CategoryAttribute): RadioGroup {
        val radioGroup = RadioGroup(requireContext())

        attribute.options.forEach { option ->
            val radioButton =
                    RadioButton(requireContext()).apply {
                        text = option.value
                        id = option.id
                    }
            radioGroup.addView(radioButton)
        }

        radioGroup.setOnCheckedChangeListener { _, checkedId ->
            val selectedOption = attribute.options.find { it.id == checkedId }
            selectedOption?.let {
                attributeValues[attribute.id] = it.value
                updateFormState()
            }
        }

        return radioGroup
    }

    private fun createCheckboxInput(attribute: CategoryAttribute): LinearLayout {
        val checkboxContainer =
                LinearLayout(requireContext()).apply { orientation = LinearLayout.VERTICAL }

        val selectedValues = mutableSetOf<String>()

        attribute.options.forEach { option ->
            val checkbox =
                    CheckBox(requireContext()).apply {
                        text = option.value

                        setOnCheckedChangeListener { _, isChecked ->
                            if (isChecked) {
                                selectedValues.add(option.value)
                            } else {
                                selectedValues.remove(option.value)
                            }

                            attributeValues[attribute.id] = selectedValues.joinToString(",")
                            updateFormState()
                        }
                    }
            checkboxContainer.addView(checkbox)
        }

        return checkboxContainer
    }

    private fun updateFormState() {
        // Convert attribute values to CategoryAttributeValue objects
        val categoryAttributeValues =
                attributeValues.map { (attributeId, value) ->
                    CategoryAttributeValue(
                            id = 0, // Will be set by server
                            attributeId = attributeId,
                            value = value,
                            selectionValue = value
                    )
                }

        // Update ViewModel with new attribute values
        viewModel.setCategoryAttributeValues(categoryAttributeValues)
    }

    /** Validate all required attributes are filled */
    fun validateAttributes(): Boolean {
        val attributes = viewModel.categoryAttributes.value

        for (attribute in attributes) {
            if (attribute.isRequired) {
                val value = attributeValues[attribute.id]
                if (value.isNullOrBlank()) {
                    Toast.makeText(
                                    context,
                                    "${attribute.attribute} is required",
                                    Toast.LENGTH_SHORT
                            )
                            .show()
                    return false
                }
            }
        }

        return true
    }

    /** Get current attribute values for form submission */
    fun getAttributeValues(): List<CategoryAttributeValue> {
        return attributeValues.map { (attributeId, value) ->
            CategoryAttributeValue(
                    id = 0,
                    attributeId = attributeId,
                    value = value,
                    selectionValue = value
            )
        }
    }
}

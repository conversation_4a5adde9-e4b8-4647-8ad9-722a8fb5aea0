<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent_black">#FF1A1A1A</color>
    <color name="accent_mustard">#FFD69E2E</color>
    <color name="accent_red">#FFE53E3E</color>
    <color name="autoflow_background">#FFF5F5F5</color>
    <color name="autoflow_border">#FFCCCCCC</color>
    <color name="autoflow_card_background">#FFFFFFFF</color>
    <color name="autoflow_divider">#FFE0E0E0</color>
    <color name="autoflow_error">#FFE53E3E</color>
    <color name="autoflow_info">#FF38B2AC</color>
    <color name="autoflow_primary">#FF38B2AC</color>
    <color name="autoflow_primary_dark">#FF2C7A7B</color>
    <color name="autoflow_secondary">#FFD69E2E</color>
    <color name="autoflow_secondary_dark">#FFB7791F</color>
    <color name="autoflow_shadow">#1F000000</color>
    <color name="autoflow_success">#FF38B2AC</color>
    <color name="autoflow_surface">#FFFFFFFF</color>
    <color name="autoflow_text_hint">#FFA0AEC0</color>
    <color name="autoflow_text_primary">#FF1A1A1A</color>
    <color name="autoflow_text_secondary">#FF4A5568</color>
    <color name="autoflow_warning">#FFD69E2E</color>
    <color name="background_light">#F5F5F5</color>
    <color name="black">#FF000000</color>
    <color name="brand_black">#FF1A1A1A</color>
    <color name="brand_black_bg">#FF0F0F0F</color>
    <color name="brand_black_light">#FF2D2D2D</color>
    <color name="brand_mustard">#FFD69E2E</color>
    <color name="brand_mustard_bg">#FFFFFAF0</color>
    <color name="brand_mustard_dark">#FFB7791F</color>
    <color name="brand_mustard_light">#FFF6E05E</color>
    <color name="brand_primary">#FF38B2AC</color>
    <color name="brand_red">#FFE53E3E</color>
    <color name="brand_red_bg">#FFFED7D7</color>
    <color name="brand_red_dark">#FFC53030</color>
    <color name="brand_red_light">#FFFC8181</color>
    <color name="brand_teal">#FF38B2AC</color>
    <color name="brand_teal_bg">#FFE6FFFA</color>
    <color name="brand_teal_dark">#FF2C7A7B</color>
    <color name="brand_teal_light">#FF81E6D9</color>
    <color name="darker_gray">#FF666666</color>
    <color name="dropdown_background">#FFFFFFFF</color>
    <color name="dropdown_border">#FFE0E0E0</color>
    <color name="dropdown_chevron">#FF38B2AC</color>
    <color name="dropdown_item_background">#FFF8F9FA</color>
    <color name="dropdown_item_selected">#FFE6FFFA</color>
    <color name="dropdown_leaf_category">#FF1A1A1A</color>
    <color name="dropdown_parent_category">#FF38B2AC</color>
    <color name="dropdown_text_primary">#FF1A1A1A</color>
    <color name="dropdown_text_secondary">#FF4A5568</color>
    <color name="light_gray">#FFE0E0E0</color>
    <color name="on_primary_container">#FF1A1A1A</color>
    <color name="on_secondary_container">#FF1A1A1A</color>
    <color name="on_surface">#FF1A1A1A</color>
    <color name="on_surface_variant">#FF6B7280</color>
    <color name="outline">#FFD1D5DB</color>
    <color name="outline_variant">#FFE5E7EB</color>
    <color name="primary">#FF38B2AC</color>
    <color name="primary_container">#FFE6FFFA</color>
    <color name="purple_100">#FFE6FFFA</color>
    <color name="purple_200">#FFB2F5EA</color>
    <color name="purple_300">#FF81E6D9</color>
    <color name="purple_500">#FF38B2AC</color>
    <color name="secondary">#FFD69E2E</color>
    <color name="secondary_container">#FFFFFAF0</color>
    <color name="stock_green">#4CAF50</color>
    <color name="stock_red">#F44336</color>
    <color name="stock_yellow">#FF9800</color>
    <color name="surface">#FFFFFFFF</color>
    <color name="surface_variant">#FFF3F4F6</color>
    <color name="text_on_brand">#FFFFFFFF</color>
    <color name="text_on_dark">#FFFFFFFF</color>
    <color name="text_primary">#FF1A1A1A</color>
    <color name="text_secondary">#FF4A5568</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="chip_height">32dp</dimen>
    <dimen name="corner_radius_extra_large">20dp</dimen>
    <dimen name="corner_radius_full">28dp</dimen>
    <dimen name="corner_radius_large">12dp</dimen>
    <dimen name="corner_radius_medium">8dp</dimen>
    <dimen name="corner_radius_small">4dp</dimen>
    <dimen name="dropdown_item_height">56dp</dimen>
    <dimen name="dropdown_trigger_height">56dp</dimen>
    <dimen name="elevation_extra_high">12dp</dimen>
    <dimen name="elevation_high">6dp</dimen>
    <dimen name="elevation_low">1dp</dimen>
    <dimen name="elevation_medium">3dp</dimen>
    <dimen name="elevation_none">0dp</dimen>
    <dimen name="icon_size_large">32dp</dimen>
    <dimen name="icon_size_medium">24dp</dimen>
    <dimen name="icon_size_small">16dp</dimen>
    <dimen name="search_input_height">48dp</dimen>
    <dimen name="spacing_large">16dp</dimen>
    <dimen name="spacing_medium">12dp</dimen>
    <dimen name="spacing_small">8dp</dimen>
    <dimen name="spacing_xl">20dp</dimen>
    <dimen name="spacing_xs">4dp</dimen>
    <dimen name="spacing_xxl">24dp</dimen>
    <dimen name="spacing_xxxl">32dp</dimen>
    <dimen name="text_size_headline">28sp</dimen>
    <dimen name="text_size_large">16sp</dimen>
    <dimen name="text_size_medium">14sp</dimen>
    <dimen name="text_size_small">12sp</dimen>
    <dimen name="text_size_xl">20sp</dimen>
    <dimen name="text_size_xxl">24sp</dimen>
    <string name="app_name">Autoflow</string>
    <string name="cancel">Cancel</string>
    <string name="didnt_receive_code">Didn\'t receive code? </string>
    <string name="email_hint">Email</string>
    <string name="email_invalid">Please enter a valid email</string>
    <string name="email_required">Email is required</string>
    <string name="enter_verification_code">Enter your Verification Code</string>
    <string name="error_connection_failed">Connection failed. Please try again.</string>
    <string name="error_invalid_credentials">Invalid email or password. Please try again.</string>
    <string name="error_invalid_otp">Invalid OTP code. Please check and try again.</string>
    <string name="error_no_internet">No internet connection. Please check your network settings.</string>
    <string name="error_occurred">An error occurred</string>
    <string name="error_otp_expired">OTP code has expired. Please request a new one.</string>
    <string name="error_server_error">Server error. Please try again later.</string>
    <string name="error_timeout">Request timed out. Please try again.</string>
    <string name="error_too_many_requests">Too many requests. Please wait and try again.</string>
    <string name="error_unknown">An unexpected error occurred. Please try again.</string>
    <string name="error_user_not_found">User not found. Please check your credentials.</string>
    <string name="forgot_password">Forgot password?</string>
    <string name="invalid_code">Invalid verification code</string>
    <string name="loading">Loading...</string>
    <string name="login_button">LOGIN</string>
    <string name="login_title">Login to continue</string>
    <string name="ok">OK</string>
    <string name="password_hint">Password</string>
    <string name="password_required">Password is required</string>
    <string name="password_too_short">Password must be at least 6 characters</string>
    <string name="resend_again">Resend again</string>
    <string name="select_category_placeholder">Select a category</string>
    <string name="signing_in">Signing in...</string>
    <string name="try_again">Try Again</string>
    <string name="verification_code_sent">We sent a verification code\nto your email address</string>
    <string name="verification_failed">Verification failed</string>
    <string name="verify_account">Verify Account</string>
    <string name="verify_otp">Verify OTP</string>
    <string name="verifying">Verifying...</string>
    <style name="AutoflowButton" parent="Widget.Material3.Button">
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">16dp</item>
        <item name="android:minHeight">48dp</item>
    </style>
    <style name="AutoflowButton.Outlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">16dp</item>
        <item name="android:minHeight">48dp</item>
    </style>
    <style name="AutoflowButton.Tonal" parent="Widget.Material3.Button.TonalButton">
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">16dp</item>
        <item name="android:minHeight">48dp</item>
    </style>
    <style name="CategoryDropdownItem" parent="Widget.Material3.CardView">
        <item name="cardBackgroundColor">@color/surface</item>
        <item name="cardElevation">@dimen/elevation_low</item>
        <item name="cardCornerRadius">@dimen/corner_radius_medium</item>
        <item name="rippleColor">@color/primary</item>
    </style>
    <style name="CategoryDropdownParent" parent="Widget.Material3.CardView">
        <item name="cardBackgroundColor">@color/surface_variant</item>
        <item name="cardElevation">@dimen/elevation_low</item>
        <item name="cardCornerRadius">@dimen/corner_radius_medium</item>
        <item name="rippleColor">@color/primary</item>
    </style>
    <style name="CategoryDropdownTrigger" parent="Widget.Material3.CardView">
        <item name="cardBackgroundColor">@color/surface_variant</item>
        <item name="cardElevation">@dimen/elevation_medium</item>
        <item name="cardCornerRadius">@dimen/corner_radius_large</item>
        <item name="rippleColor">@color/primary</item>
    </style>
    <style name="LoginButton" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/brand_teal</item>
        <item name="android:textColor">@color/text_on_brand</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">0.1</item>
        <item name="cornerRadius">28dp</item>
        <item name="elevation">8dp</item>
        <item name="rippleColor">@color/brand_teal_light</item>
    </style>
    <style name="OTPDigitStyle">
        <item name="android:background">@drawable/otp_digit_background</item>
        <item name="android:gravity">center</item>
        <item name="android:inputType">number</item>
        <item name="android:maxLength">1</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:importantForAutofill">no</item>
        <item name="android:maxLines">1</item>
        <item name="android:singleLine">true</item>
        <item name="android:selectAllOnFocus">true</item>
    </style>
    <style name="Theme.AutoflowAndroid" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorPrimaryContainer">@color/primary_container</item>
        <item name="colorOnPrimaryContainer">@color/on_primary_container</item>
        
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="colorSecondaryContainer">@color/secondary_container</item>
        <item name="colorOnSecondaryContainer">@color/on_secondary_container</item>
        
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnSurface">@color/on_surface</item>
        <item name="colorSurfaceVariant">@color/surface_variant</item>
        <item name="colorOnSurfaceVariant">@color/on_surface_variant</item>
        
        <item name="colorOutline">@color/outline</item>
        <item name="colorOutlineVariant">@color/outline_variant</item>
        
        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightNavigationBar">true</item>
        
        
        <item name="colorSurfaceBright">@color/surface</item>
        <item name="colorSurfaceDim">@color/surface_variant</item>
        <item name="colorSurfaceContainer">@color/surface_variant</item>
        <item name="colorSurfaceContainerHigh">@color/surface</item>
        <item name="colorSurfaceContainerLow">@color/surface_variant</item>
        <item name="colorSurfaceContainerHighest">@color/surface</item>
    </style>
</resources>
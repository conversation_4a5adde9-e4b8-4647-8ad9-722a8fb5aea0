package com.autoflow.android.data.local

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.util.*

/**
 * Manager for search history storage and retrieval
 */
class SearchHistoryManager(context: Context) {

    companion object {
        private const val PREFS_NAME = "search_history_prefs"
        private const val KEY_SEARCH_HISTORY = "search_history"
        private const val MAX_HISTORY_SIZE = 20
    }

    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()

    /**
     * Add a search query to history
     */
    fun addSearchQuery(query: String) {
        if (query.isBlank()) return

        val currentHistory = getSearchHistory().toMutableList()
        
        // Remove if already exists to avoid duplicates
        currentHistory.removeAll { it.query.equals(query, ignoreCase = true) }
        
        // Add to the beginning
        currentHistory.add(0, SearchHistoryItem(query, System.currentTimeMillis()))
        
        // Limit size
        if (currentHistory.size > MAX_HISTORY_SIZE) {
            currentHistory.removeAt(currentHistory.size - 1)
        }
        
        saveSearchHistory(currentHistory)
    }

    /**
     * Get search history ordered by most recent first
     */
    fun getSearchHistory(): List<SearchHistoryItem> {
        val json = sharedPreferences.getString(KEY_SEARCH_HISTORY, null) ?: return emptyList()
        
        return try {
            val type = object : TypeToken<List<SearchHistoryItem>>() {}.type
            gson.fromJson<List<SearchHistoryItem>>(json, type) ?: emptyList()
        } catch (e: Exception) {
            emptyList()
        }
    }

    /**
     * Get search suggestions based on input
     */
    fun getSearchSuggestions(input: String, maxSuggestions: Int = 5): List<String> {
        if (input.isBlank()) {
            return getRecentSearches(maxSuggestions)
        }

        return getSearchHistory()
            .filter { it.query.contains(input, ignoreCase = true) }
            .take(maxSuggestions)
            .map { it.query }
    }

    /**
     * Get recent searches
     */
    fun getRecentSearches(maxCount: Int = 10): List<String> {
        return getSearchHistory()
            .take(maxCount)
            .map { it.query }
    }

    /**
     * Clear all search history
     */
    fun clearSearchHistory() {
        sharedPreferences.edit()
            .remove(KEY_SEARCH_HISTORY)
            .apply()
    }

    /**
     * Remove a specific search query from history
     */
    fun removeSearchQuery(query: String) {
        val currentHistory = getSearchHistory().toMutableList()
        currentHistory.removeAll { it.query.equals(query, ignoreCase = true) }
        saveSearchHistory(currentHistory)
    }

    /**
     * Get popular searches (most frequently searched)
     */
    fun getPopularSearches(maxCount: Int = 5): List<String> {
        val searchCounts = mutableMapOf<String, Int>()
        
        getSearchHistory().forEach { item ->
            val lowerQuery = item.query.lowercase()
            searchCounts[lowerQuery] = searchCounts.getOrDefault(lowerQuery, 0) + 1
        }
        
        return searchCounts.entries
            .sortedByDescending { it.value }
            .take(maxCount)
            .map { it.key }
    }

    private fun saveSearchHistory(history: List<SearchHistoryItem>) {
        val json = gson.toJson(history)
        sharedPreferences.edit()
            .putString(KEY_SEARCH_HISTORY, json)
            .apply()
    }
}

/**
 * Data class for search history items
 */
data class SearchHistoryItem(
    val query: String,
    val timestamp: Long
) {
    fun getFormattedDate(): String {
        val date = Date(timestamp)
        val now = Date()
        val diffInMillis = now.time - date.time
        val diffInHours = diffInMillis / (1000 * 60 * 60)
        val diffInDays = diffInHours / 24

        return when {
            diffInHours < 1 -> "Just now"
            diffInHours < 24 -> "${diffInHours}h ago"
            diffInDays < 7 -> "${diffInDays}d ago"
            else -> "${diffInDays / 7}w ago"
        }
    }
}

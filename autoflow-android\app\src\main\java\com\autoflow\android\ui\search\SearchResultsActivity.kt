package com.autoflow.android.ui.search

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.*
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.autoflow.android.R
import com.autoflow.android.data.api.Part
import com.autoflow.android.data.api.PartsApiService
import com.autoflow.android.ui.base.BaseBackendActivity
import com.autoflow.android.ui.parts.PartDetailActivity
import kotlinx.coroutines.launch

/** Search Results Activity with filters, sorting, and pagination */
class SearchResultsActivity : BaseBackendActivity() {

    companion object {
        private const val TAG = "SearchResultsActivity"
        const val EXTRA_SEARCH_QUERY = "search_query"
    }

    // UI Components
    private lateinit var searchInput: EditText
    private lateinit var clearSearchButton: ImageButton
    private lateinit var filtersButton: Button
    private lateinit var sortButton: Button
    private lateinit var resultsCountText: TextView
    private lateinit var activeFiltersContainer: View
    private lateinit var activeFiltersLayout: LinearLayout
    private lateinit var resultsRecyclerView: RecyclerView
    private lateinit var loadingLayout: View
    private lateinit var emptyLayout: View
    private lateinit var errorLayout: View
    private lateinit var errorMessageText: TextView
    private lateinit var retryButton: Button
    private lateinit var paginationLayout: LinearLayout
    private lateinit var previousPageButton: Button
    private lateinit var nextPageButton: Button
    private lateinit var pageInfoText: TextView

    // Data and Services
    private lateinit var partsApiService: PartsApiService
    private lateinit var searchAdapter: SearchResultsAdapter
    private var currentQuery: String = ""
    private var currentFilters = SearchFilters()
    private var currentSort = SortOption.RELEVANCE
    private var currentPage = 1
    private var totalPages = 1
    private var pageSize = 20
    private var isLoading = false
    private var totalResults = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setBackendContentView(R.layout.activity_search_results)

        partsApiService = PartsApiService(this)

        initializeViews()
        setupRecyclerView()
        setupSearchFunctionality()
        setupClickListeners()

        // Get initial search query from intent
        currentQuery = intent.getStringExtra(EXTRA_SEARCH_QUERY) ?: ""
        if (currentQuery.isNotEmpty()) {
            searchInput.setText(currentQuery)
            performSearch()
            // Only clear focus after search is performed
            clearSearchInputFocus()
        }
    }

    private fun initializeViews() {
        searchInput = findViewById(R.id.searchInput)
        clearSearchButton = findViewById(R.id.clearSearchButton)
        filtersButton = findViewById(R.id.filtersButton)
        sortButton = findViewById(R.id.sortButton)
        resultsCountText = findViewById(R.id.resultsCountText)
        activeFiltersContainer = findViewById(R.id.activeFiltersContainer)
        activeFiltersLayout = findViewById(R.id.activeFiltersLayout)
        resultsRecyclerView = findViewById(R.id.resultsRecyclerView)
        loadingLayout = findViewById(R.id.loadingLayout)
        emptyLayout = findViewById(R.id.emptyLayout)
        errorLayout = findViewById(R.id.errorLayout)
        errorMessageText = findViewById(R.id.errorMessageText)
        retryButton = findViewById(R.id.retryButton)
        paginationLayout = findViewById(R.id.paginationLayout)
        previousPageButton = findViewById(R.id.previousPageButton)
        nextPageButton = findViewById(R.id.nextPageButton)
        pageInfoText = findViewById(R.id.pageInfoText)
    }

    private fun setupRecyclerView() {
        searchAdapter = SearchResultsAdapter { part ->
            // Handle part click - navigate to part details
            onPartClicked(part)
        }

        resultsRecyclerView.apply {
            layoutManager = LinearLayoutManager(this@SearchResultsActivity)
            adapter = searchAdapter

            // Clear focus from search input when scrolling
            setOnScrollChangeListener { _, _, _, _, _ -> clearSearchInputFocus() }
        }

        // Clear focus when clicking on the results area (but allow editing when tapping input)
        resultsRecyclerView.setOnTouchListener { _, _ ->
            if (searchInput.hasFocus()) {
                clearSearchInputFocus()
            }
            false // Return false to allow normal touch handling
        }
    }

    private fun setupSearchFunctionality() {
        searchInput.addTextChangedListener(
                object : TextWatcher {
                    override fun beforeTextChanged(
                            s: CharSequence?,
                            start: Int,
                            count: Int,
                            after: Int
                    ) {}

                    override fun onTextChanged(
                            s: CharSequence?,
                            start: Int,
                            before: Int,
                            count: Int
                    ) {
                        val query = s.toString().trim()
                        clearSearchButton.visibility =
                                if (query.isNotEmpty()) View.VISIBLE else View.GONE
                    }

                    override fun afterTextChanged(s: Editable?) {}
                }
        )

        searchInput.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == android.view.inputmethod.EditorInfo.IME_ACTION_SEARCH) {
                currentQuery = searchInput.text.toString().trim()
                if (currentQuery.isNotEmpty()) {
                    currentPage = 1 // Reset to first page for new search
                    performSearch()
                }
                true
            } else {
                false
            }
        }
    }

    private fun setupClickListeners() {
        clearSearchButton.setOnClickListener {
            searchInput.text.clear()
            currentQuery = ""
            searchAdapter.updateResults(emptyList())
            showEmptyState()
        }

        filtersButton.setOnClickListener { showFiltersDialog() }

        sortButton.setOnClickListener { showSortDialog() }

        retryButton.setOnClickListener { performSearch() }

        previousPageButton.setOnClickListener {
            if (currentPage > 1) {
                currentPage--
                performSearch()
            }
        }

        nextPageButton.setOnClickListener {
            if (currentPage < totalPages) {
                currentPage++
                performSearch()
            }
        }
    }

    private fun performSearch() {
        if (currentQuery.isEmpty()) {
            showEmptyState()
            return
        }

        if (isLoading) return
        isLoading = true

        showLoadingState()

        lifecycleScope.launch {
            try {
                val searchResponse =
                        partsApiService.searchParts(
                                query = currentQuery,
                                filters = currentFilters,
                                sortBy = currentSort,
                                page = currentPage,
                                pageSize = pageSize
                        )

                handleSearchResults(searchResponse)
            } catch (e: Exception) {
                showErrorState("Search failed: ${e.message}")
            } finally {
                isLoading = false
            }
        }
    }

    private fun handleSearchResults(searchResponse: com.autoflow.android.data.api.SearchResponse) {
        // Update pagination info
        totalResults = searchResponse.totalResults
        totalPages = searchResponse.totalPages
        currentPage = searchResponse.currentPage

        if (searchResponse.results.isEmpty()) {
            showEmptyState()
        } else {
            showResultsState(searchResponse.results)
        }

        updateResultsCount(searchResponse.results.size, searchResponse.totalResults)
        updateActiveFilters()
        updatePagination()
    }

    private fun showLoadingState() {
        loadingLayout.visibility = View.VISIBLE
        resultsRecyclerView.visibility = View.GONE
        emptyLayout.visibility = View.GONE
        errorLayout.visibility = View.GONE
    }

    private fun showResultsState(results: List<Part>) {
        searchAdapter.updateResults(results)
        resultsRecyclerView.visibility = View.VISIBLE
        loadingLayout.visibility = View.GONE
        emptyLayout.visibility = View.GONE
        errorLayout.visibility = View.GONE
    }

    private fun showEmptyState() {
        emptyLayout.visibility = View.VISIBLE
        resultsRecyclerView.visibility = View.GONE
        loadingLayout.visibility = View.GONE
        errorLayout.visibility = View.GONE
    }

    private fun showErrorState(message: String) {
        errorMessageText.text = message
        errorLayout.visibility = View.VISIBLE
        resultsRecyclerView.visibility = View.GONE
        loadingLayout.visibility = View.GONE
        emptyLayout.visibility = View.GONE
    }

    private fun updateResultsCount(currentCount: Int, totalCount: Int = currentCount) {
        resultsCountText.text =
                when {
                    totalCount == 0 -> "No results"
                    totalCount == 1 -> "1 result"
                    currentCount == totalCount -> "$totalCount results"
                    else -> "Showing $currentCount of $totalCount results"
                }
    }

    private fun updateActiveFilters() {
        // TODO: Implement active filters display
        val hasActiveFilters = currentFilters.hasActiveFilters()
        activeFiltersContainer.visibility = if (hasActiveFilters) View.VISIBLE else View.GONE
    }

    private fun updatePagination() {
        // Show pagination if there are multiple pages
        paginationLayout.visibility = if (totalPages > 1) View.VISIBLE else View.GONE

        // Update page info
        pageInfoText.text = "Page $currentPage of $totalPages"

        // Update button states
        previousPageButton.isEnabled = currentPage > 1
        nextPageButton.isEnabled = currentPage < totalPages
    }

    private fun showFiltersDialog() {
        val filtersDialog = FiltersDialogFragment.newInstance(currentFilters)
        filtersDialog.setOnFiltersAppliedListener(
                object : FiltersDialogFragment.OnFiltersAppliedListener {
                    override fun onFiltersApplied(filters: SearchFilters) {
                        currentFilters = filters
                        currentPage = 1 // Reset to first page when filters change
                        performSearch()
                    }
                }
        )
        filtersDialog.show(supportFragmentManager, "filters_dialog")
    }

    private fun showSortDialog() {
        val sortDialog = SortDialogFragment.newInstance(currentSort)
        sortDialog.setOnSortAppliedListener(
                object : SortDialogFragment.OnSortAppliedListener {
                    override fun onSortApplied(sortOption: SortOption) {
                        currentSort = sortOption
                        sortButton.text = "Sort: ${sortOption.displayName}"
                        currentPage = 1 // Reset to first page when sorting changes
                        performSearch()
                    }
                }
        )
        sortDialog.show(supportFragmentManager, "sort_dialog")
    }

    private fun onPartClicked(part: Part) {
        // Navigate to PartDetailActivity
        val intent =
                Intent(this, PartDetailActivity::class.java).apply {
                    putExtra(PartDetailActivity.EXTRA_PART_ID, part.id)
                    putExtra(PartDetailActivity.EXTRA_PART_NAME, part.name)
                    putExtra(PartDetailActivity.EXTRA_PART_NUMBER, part.partNumber)
                    putExtra(PartDetailActivity.EXTRA_PART_DESCRIPTION, part.description)
                    putExtra(PartDetailActivity.EXTRA_PART_PRICE, part.price)
                    putExtra(PartDetailActivity.EXTRA_PART_STOCK, part.stock)
                    putExtra(PartDetailActivity.EXTRA_PART_BRAND, part.brand)
                    putExtra(PartDetailActivity.EXTRA_PART_CATEGORY, part.category)
                    putExtra(PartDetailActivity.EXTRA_PART_IMAGE_URL, part.imageUrl)
                }
        startActivity(intent)
    }

    private fun showPartDetailsDialog(part: Part) {
        val dialog =
                android.app.AlertDialog.Builder(this)
                        .setTitle(part.name)
                        .setMessage(buildPartDetailsMessage(part))
                        .setPositiveButton("Close") { dialog, _ -> dialog.dismiss() }
                        .setNeutralButton("Contact Seller") { _, _ ->
                            // TODO: Implement contact seller functionality
                            Toast.makeText(
                                            this,
                                            "Contact seller feature coming soon",
                                            Toast.LENGTH_SHORT
                                    )
                                    .show()
                        }
                        .create()

        dialog.show()
    }

    private fun buildPartDetailsMessage(part: Part): String {
        return buildString {
            appendLine("Part Number: ${part.partNumber}")
            if (part.description.isNotEmpty()) {
                appendLine("Description: ${part.description}")
            }
            appendLine("Price: ${java.text.NumberFormat.getCurrencyInstance().format(part.price)}")
            appendLine("Stock: ${part.stock} available")
            if (part.brand.isNotEmpty()) {
                appendLine("Brand: ${part.brand}")
            }
            if (part.category.isNotEmpty()) {
                appendLine("Category: ${part.category}")
            }
        }
    }

    override fun onHamburgerMenuClick() {
        // Handle hamburger menu
        Toast.makeText(this, "Menu clicked", Toast.LENGTH_SHORT).show()
    }

    override fun onCategoriesMenuClick() {
        // Handle categories menu
        Toast.makeText(this, "Categories clicked", Toast.LENGTH_SHORT).show()
    }

    private fun clearSearchInputFocus() {
        searchInput.clearFocus()
        val inputMethodManager =
                getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        inputMethodManager.hideSoftInputFromWindow(searchInput.windowToken, 0)
    }
}

// Data classes for search functionality
data class SearchFilters(
        val categories: List<String> = emptyList(),
        val brands: List<String> = emptyList(),
        val priceMin: Double? = null,
        val priceMax: Double? = null,
        val inStockOnly: Boolean = false
) : java.io.Serializable {
    fun hasActiveFilters(): Boolean {
        return categories.isNotEmpty() ||
                brands.isNotEmpty() ||
                priceMin != null ||
                priceMax != null ||
                inStockOnly
    }
}

enum class SortOption(val displayName: String) : java.io.Serializable {
    RELEVANCE("Relevance"),
    PRICE_LOW_TO_HIGH("Price: Low to High"),
    PRICE_HIGH_TO_LOW("Price: High to Low"),
    NAME_A_TO_Z("Name: A to Z"),
    NAME_Z_TO_A("Name: Z to A"),
    NEWEST_FIRST("Newest First")
}

package com.autoflow.android.ui.addpart.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import com.autoflow.android.domain.models.*
import com.autoflow.android.ui.addpart.AddPartViewModel
import kotlinx.coroutines.launch

/**
 * Vehicle Selection Fragment - Step 3B of Add Part flow (for non-PN categories)
 * Cascading vehicle selection: Brand → Model → Generation → Variation → Trim
 * Mirrors web app's vehicle selection dropdowns
 */
class VehicleSelectionFragment : Fragment() {
    
    companion object {
        private const val TAG = "VehicleSelectionFragment"
    }
    
    private val viewModel: AddPartViewModel by activityViewModels()
    
    // UI Components
    private lateinit var instructionText: TextView
    private lateinit var brandSpinner: Spinner
    private lateinit var modelSpinner: Spinner
    private lateinit var generationSpinner: Spinner
    private lateinit var variationSpinner: Spinner
    private lateinit var trimSpinner: Spinner
    private lateinit var optionalPartNumberSection: LinearLayout
    private lateinit var partNumberInput: EditText
    private lateinit var selectionSummary: TextView
    
    // Adapters
    private lateinit var brandsAdapter: ArrayAdapter<CarBrand>
    private lateinit var modelsAdapter: ArrayAdapter<CarModel>
    private lateinit var generationsAdapter: ArrayAdapter<CarGeneration>
    private lateinit var variationsAdapter: ArrayAdapter<CarVariation>
    private lateinit var trimsAdapter: ArrayAdapter<CarTrim>
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return createLayout()
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupSpinners()
        setupClickListeners()
        observeViewModel()
    }
    
    private fun createLayout(): ScrollView {
        val scrollView = ScrollView(requireContext())
        
        val mainLayout = LinearLayout(requireContext()).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(16, 16, 16, 16)
        }
        
        // Instructions
        instructionText = TextView(requireContext()).apply {
            text = "Select the vehicle that this part fits"
            textSize = 16f
            setPadding(0, 0, 0, 16)
        }
        
        // Brand selection
        val brandSection = createSpinnerSection("Brand *", "Select brand")
        brandSpinner = brandSection.second
        
        // Model selection
        val modelSection = createSpinnerSection("Model *", "Select model")
        modelSpinner = modelSection.second
        
        // Generation selection
        val generationSection = createSpinnerSection("Generation *", "Select generation")
        generationSpinner = generationSection.second
        
        // Variation selection
        val variationSection = createSpinnerSection("Variation *", "Select variation")
        variationSpinner = variationSection.second
        
        // Trim selection
        val trimSection = createSpinnerSection("Trim *", "Select trim")
        trimSpinner = trimSection.second
        
        // Optional part number section (appears after trim selection)
        optionalPartNumberSection = LinearLayout(requireContext()).apply {
            orientation = LinearLayout.VERTICAL
            visibility = View.GONE
            setPadding(0, 16, 0, 0)
        }
        
        val partNumberLabel = TextView(requireContext()).apply {
            text = "Part Number (Optional)"
            textSize = 16f
            setTypeface(null, android.graphics.Typeface.BOLD)
            setPadding(0, 0, 0, 8)
        }
        
        partNumberInput = EditText(requireContext()).apply {
            hint = "Enter part number if known"
            setPadding(16, 16, 16, 16)
        }
        
        optionalPartNumberSection.addView(partNumberLabel)
        optionalPartNumberSection.addView(partNumberInput)
        
        // Selection summary
        selectionSummary = TextView(requireContext()).apply {
            text = ""
            textSize = 14f
            setPadding(0, 16, 0, 0)
            setBackgroundResource(android.R.drawable.dialog_frame)
            setPadding(16, 16, 16, 16)
            visibility = View.GONE
        }
        
        // Add all sections to main layout
        mainLayout.addView(instructionText)
        mainLayout.addView(brandSection.first)
        mainLayout.addView(modelSection.first)
        mainLayout.addView(generationSection.first)
        mainLayout.addView(variationSection.first)
        mainLayout.addView(trimSection.first)
        mainLayout.addView(optionalPartNumberSection)
        mainLayout.addView(selectionSummary)
        
        scrollView.addView(mainLayout)
        return scrollView
    }
    
    private fun createSpinnerSection(labelText: String, hintText: String): Pair<LinearLayout, Spinner> {
        val section = LinearLayout(requireContext()).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(0, 0, 0, 16)
        }
        
        val label = TextView(requireContext()).apply {
            text = labelText
            textSize = 16f
            setTypeface(null, android.graphics.Typeface.BOLD)
            setPadding(0, 0, 0, 8)
        }
        
        val spinner = Spinner(requireContext()).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
        }
        
        section.addView(label)
        section.addView(spinner)
        
        return Pair(section, spinner)
    }
    
    private fun setupSpinners() {
        // Brands adapter
        brandsAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, mutableListOf<CarBrand>())
        brandsAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        brandSpinner.adapter = brandsAdapter
        
        // Models adapter
        modelsAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, mutableListOf<CarModel>())
        modelsAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        modelSpinner.adapter = modelsAdapter
        
        // Generations adapter
        generationsAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, mutableListOf<CarGeneration>())
        generationsAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        generationSpinner.adapter = generationsAdapter
        
        // Variations adapter
        variationsAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, mutableListOf<CarVariation>())
        variationsAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        variationSpinner.adapter = variationsAdapter
        
        // Trims adapter
        trimsAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, mutableListOf<CarTrim>())
        trimsAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        trimSpinner.adapter = trimsAdapter
    }
    
    private fun setupClickListeners() {
        brandSpinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                if (position > 0) {
                    val selectedBrand = brandsAdapter.getItem(position)
                    selectedBrand?.let { viewModel.selectBrand(it) }
                }
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }
        
        modelSpinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                if (position > 0) {
                    val selectedModel = modelsAdapter.getItem(position)
                    selectedModel?.let { viewModel.selectModel(it) }
                }
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }
        
        generationSpinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                if (position > 0) {
                    val selectedGeneration = generationsAdapter.getItem(position)
                    selectedGeneration?.let { viewModel.selectGeneration(it) }
                }
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }
        
        variationSpinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                if (position > 0) {
                    val selectedVariation = variationsAdapter.getItem(position)
                    selectedVariation?.let { viewModel.selectVariation(it) }
                }
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }
        
        trimSpinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                if (position > 0) {
                    val selectedTrim = trimsAdapter.getItem(position)
                    selectedTrim?.let { 
                        viewModel.selectTrim(it)
                        showOptionalPartNumberSection()
                        updateSelectionSummary()
                    }
                }
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }
    }
    
    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.brands.collect { brands ->
                brandsAdapter.clear()
                brandsAdapter.add(CarBrand(0, "Select brand"))
                brandsAdapter.addAll(brands)
                brandsAdapter.notifyDataSetChanged()
            }
        }
        
        lifecycleScope.launch {
            viewModel.models.collect { models ->
                modelsAdapter.clear()
                modelsAdapter.add(CarModel(0, 0, "Select model"))
                modelsAdapter.addAll(models)
                modelsAdapter.notifyDataSetChanged()
            }
        }
        
        lifecycleScope.launch {
            viewModel.generations.collect { generations ->
                generationsAdapter.clear()
                generationsAdapter.add(CarGeneration(0, 0, "Select generation"))
                generationsAdapter.addAll(generations)
                generationsAdapter.notifyDataSetChanged()
            }
        }
        
        lifecycleScope.launch {
            viewModel.variations.collect { variations ->
                variationsAdapter.clear()
                variationsAdapter.add(CarVariation(0, 0, "Select variation"))
                variationsAdapter.addAll(variations)
                variationsAdapter.notifyDataSetChanged()
            }
        }
        
        lifecycleScope.launch {
            viewModel.trims.collect { trims ->
                trimsAdapter.clear()
                trimsAdapter.add(CarTrim(0, 0, "Select trim"))
                trimsAdapter.addAll(trims)
                trimsAdapter.notifyDataSetChanged()
            }
        }
    }
    
    private fun showOptionalPartNumberSection() {
        optionalPartNumberSection.visibility = View.VISIBLE
    }
    
    private fun updateSelectionSummary() {
        val formState = viewModel.formState.value
        val summary = buildString {
            append("Selected Vehicle:\n")
            append("${formState.modelName} ${formState.generationName} ${formState.generationYears}\n")
            append("${formState.variationName} ${formState.trimName}")
        }
        
        selectionSummary.text = summary
        selectionSummary.visibility = View.VISIBLE
    }
}

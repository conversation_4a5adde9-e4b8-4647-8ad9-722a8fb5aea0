package com.autoflow.android.ui.base

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.autoflow.android.R

/** Base activity for all backend screens that includes the common header */
abstract class BaseBackendActivity : AppCompatActivity() {

    protected lateinit var hamburgerMenuButton: ImageButton
    protected lateinit var categoriesMenuButton: ImageButton
    private lateinit var contentContainer: LinearLayout
    private lateinit var footerHomeButton: View
    private lateinit var footerSearchButton: View
    private lateinit var footerProfileButton: View

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupBaseLayout()
        initializeHeaderElements()
        initializeFooterElements()
        setupHeaderClickListeners()
        setupFooterClickListeners()
    }

    private fun setupBaseLayout() {
        // Create main container
        val mainContainer =
                LinearLayout(this).apply {
                    orientation = LinearLayout.VERTICAL
                    layoutParams =
                            LinearLayout.LayoutParams(
                                    LinearLayout.LayoutParams.MATCH_PARENT,
                                    LinearLayout.LayoutParams.MATCH_PARENT
                            )
                }

        // Inflate and add header
        val headerView =
                LayoutInflater.from(this).inflate(R.layout.header_backend, mainContainer, false)
        mainContainer.addView(headerView)

        // Create content container for child activities
        contentContainer =
                LinearLayout(this).apply {
                    orientation = LinearLayout.VERTICAL
                    layoutParams =
                            LinearLayout.LayoutParams(
                                    LinearLayout.LayoutParams.MATCH_PARENT,
                                    0,
                                    1f // weight = 1 to fill remaining space
                            )
                }
        mainContainer.addView(contentContainer)

        // Inflate and add footer
        val footerView =
                LayoutInflater.from(this).inflate(R.layout.footer_backend, mainContainer, false)
        mainContainer.addView(footerView)

        setContentView(mainContainer)
    }

    private fun initializeHeaderElements() {
        hamburgerMenuButton = findViewById(R.id.hamburgerMenuButton)
        categoriesMenuButton = findViewById(R.id.categoriesMenuButton)
    }

    private fun initializeFooterElements() {
        footerHomeButton = findViewById(R.id.footerHomeButton)
        footerSearchButton = findViewById(R.id.footerSearchButton)
        footerProfileButton = findViewById(R.id.footerProfileButton)
    }

    private fun setupHeaderClickListeners() {
        hamburgerMenuButton.setOnClickListener { onHamburgerMenuClick() }

        categoriesMenuButton.setOnClickListener { onCategoriesMenuClick() }
    }

    private fun setupFooterClickListeners() {
        footerHomeButton.setOnClickListener { onFooterHomeClick() }
        footerSearchButton.setOnClickListener { onFooterSearchClick() }
        footerProfileButton.setOnClickListener { onFooterProfileClick() }
    }

    /**
     * Set the content view for the child activity This will be added to the content container below
     * the header
     */
    protected fun setBackendContentView(layoutResID: Int) {
        LayoutInflater.from(this).inflate(layoutResID, contentContainer, true)
    }

    /** Override this method to handle hamburger menu clicks */
    protected open fun onHamburgerMenuClick() {
        // Default implementation - show toast
        Toast.makeText(this, "Hamburger menu clicked", Toast.LENGTH_SHORT).show()
        // TODO: Implement navigation drawer or menu
    }

    /** Override this method to handle categories menu clicks */
    protected open fun onCategoriesMenuClick() {
        // Default implementation - show toast
        Toast.makeText(this, "Categories menu clicked", Toast.LENGTH_SHORT).show()
        // TODO: Implement categories menu
    }

    /** Get the content container for manual view addition */
    protected fun getContentContainer(): LinearLayout {
        return contentContainer
    }

    /** Handle footer home button clicks */
    private fun onFooterHomeClick() {
        // Navigate to dashboard if not already there
        if (this::class.java.simpleName != "DashboardActivity") {
            try {
                val intent =
                        Intent(
                                this,
                                Class.forName("com.autoflow.android.ui.dashboard.DashboardActivity")
                        )
                intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
                startActivity(intent)
            } catch (e: Exception) {
                Toast.makeText(this, "Navigation error", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /** Handle footer search button clicks */
    private fun onFooterSearchClick() {
        // Navigate to search results if not already there
        if (this::class.java.simpleName != "SearchResultsActivity") {
            try {
                val intent =
                        Intent(
                                this,
                                Class.forName(
                                        "com.autoflow.android.ui.search.SearchResultsActivity"
                                )
                        )
                startActivity(intent)
            } catch (e: Exception) {
                Toast.makeText(this, "Navigation error", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /** Handle footer profile button clicks */
    private fun onFooterProfileClick() {
        // TODO: Navigate to profile activity when implemented
        Toast.makeText(this, "Profile feature coming soon", Toast.LENGTH_SHORT).show()
    }
}

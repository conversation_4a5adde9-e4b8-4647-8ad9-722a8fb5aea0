package com.autoflow.android.data.api

import com.autoflow.android.core.auth.AuthManager

/** Helper to create Retrofit services with the shared ApiClient */
object RetrofitServices {
    private fun retrofit(auth: AuthManager) = ApiClient.getRetrofit(auth, useStaging = false)

    fun categories(auth: AuthManager): CategoriesApi =
            retrofit(auth).create(CategoriesApi::class.java)
    fun vehicles(auth: AuthManager): VehiclesApi = retrofit(auth).create(VehiclesApi::class.java)
    fun parts(auth: AuthManager): PartsApi = retrofit(auth).create(PartsApi::class.java)
    fun images(auth: AuthManager): ImagesApi = retrofit(auth).create(ImagesApi::class.java)
    fun storage(auth: AuthManager): StorageApi = retrofit(auth).create(StorageApi::class.java)
}

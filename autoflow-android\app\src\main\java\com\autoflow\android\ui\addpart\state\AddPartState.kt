package com.autoflow.android.ui.addpart.state

import com.autoflow.android.domain.models.*

data class PartImageUiState(
        val url: String? = null,
        val localUri: String? = null,
        val isMain: Boolean = false,
        val isUploading: Boolean = false,
        val progress: Int = 0,
        val error: String? = null
)

data class AddPartState(
        val currentStepIndex: Int = 0,
        val images: List<PartImageUiState> = emptyList(),
        val selectedCategory: Int? = null,
        val categoryAttributes: List<CategoryAttribute> = emptyList(),
        val requirePartNumber: Boolean = false,
        val partNumberInput: String = "",
        val compatibilityData: CompatibilityData? = null,
        val pnCooldownUntilMillis: Long = 0L,
        val vehicleSelection: VehicleSelectionIds = VehicleSelectionIds(),
        val attributeValues: Map<Int, Any> = emptyMap(),
        val condition: PartCondition = PartCondition.NEW,
        val newStock: StockPrice? = null,
        val usedStock: StockPrice? = null,
        val titlePreview: String? = null,
        val descriptionPreview: String? = null,
        val createdPartId: Int? = null,
        val createdPartTitle: String? = null,
        val isLoading: Boolean = false,
        val errorMessage: String? = null
)

data class VehicleSelectionIds(
        val brandId: Int? = null,
        val modelId: Int? = null,
        val generationId: Int? = null,
        val variationId: Int? = null,
        val trimId: Int? = null
)

data class StockPrice(val stock: Int, val price: Double, val discountedPrice: Double?)

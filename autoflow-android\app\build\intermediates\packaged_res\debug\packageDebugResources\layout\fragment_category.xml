<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".ui.addpart.fragments.CategoryFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- Instruction -->
        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/text_instructions"
            style="@style/TextAppearance.Material3.BodyLarge"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Select the category that best describes your part"
            android:textColor="@color/text_primary"
            android:paddingBottom="16dp"
            android:textAlignment="center"/>

        <!-- Category input (trigger) - This will be replaced programmatically -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/category_input_layout"
            style="@style/Widget.Material3.TextInputLayout.FilledBox.Dense"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Category"
            app:endIconMode="dropdown_menu"
            app:boxCornerRadiusTopStart="12dp"
            app:boxCornerRadiusTopEnd="12dp"
            app:boxCornerRadiusBottomStart="12dp"
            app:boxCornerRadiusBottomEnd="12dp"
            app:boxStrokeColor="@color/dropdown_parent_category"
            app:hintTextColor="@color/dropdown_parent_category"
            app:endIconTint="@color/dropdown_parent_category">

            <com.google.android.material.textfield.MaterialAutoCompleteTextView
                android:id="@+id/input_category"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:cursorVisible="false"
                android:inputType="none"
                android:dropDownHeight="0dp"
                android:clickable="true"
                android:textColor="@color/dropdown_text_primary"/>
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Selected Category Chips -->
        <com.google.android.material.chip.ChipGroup
            android:id="@+id/chip_group_selected"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:visibility="gone"
            app:singleSelection="true"
            app:chipSpacingHorizontal="8dp"
            app:chipSpacingVertical="4dp"
            tools:visibility="visible"/>

        <!-- Dropdown content -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/dropdown_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:visibility="gone"
            app:cardCornerRadius="20dp"
            app:cardElevation="12dp"
            app:cardBackgroundColor="@color/dropdown_background"
            app:strokeColor="@color/dropdown_border"
            app:strokeWidth="1dp"
            app:cardUseCompatPadding="true"
            tools:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp"
                android:background="@color/dropdown_background">

                <!-- Search Header -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_search"
                        android:layout_marginEnd="12dp"
                        android:alpha="0.6"
                        app:tint="@color/dropdown_text_secondary"/>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/search_input_layout"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox.Dense"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:hint="Search categories..."
                        app:boxStrokeColor="@color/dropdown_parent_category"
                        app:hintTextColor="@color/dropdown_parent_category"
                        app:boxBackgroundColor="@color/dropdown_item_background"
                        app:boxCornerRadiusTopStart="12dp"
                        app:boxCornerRadiusTopEnd="12dp"
                        app:boxCornerRadiusBottomStart="12dp"
                        app:boxCornerRadiusBottomEnd="12dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/search_input_edit"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:imeOptions="actionDone"
                            android:textColor="@color/dropdown_text_primary"
                            android:background="@null"
                            android:padding="16dp"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <ImageView
                        android:id="@+id/clear_search_button"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_clear"
                        android:layout_marginStart="12dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:alpha="0.6"
                        app:tint="@color/dropdown_text_secondary"
                        android:visibility="gone"/>
                </LinearLayout>

                <!-- Categories List -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_categories"
                    android:layout_width="match_parent"
                    android:layout_height="300dp"
                    android:overScrollMode="never"
                    android:background="@color/dropdown_item_background"
                    android:padding="8dp"
                    android:clipToPadding="false"
                    android:scrollbars="vertical"
                    android:fadeScrollbars="false"
                    tools:listitem="@android:layout/simple_list_item_1"/>

                <!-- Loading Indicator -->
                <com.google.android.material.progressindicator.LinearProgressIndicator
                    android:id="@+id/progress_categories"
                    style="@style/Widget.Material3.LinearProgressIndicator"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:layout_marginTop="16dp"
                    app:indicatorColor="@color/dropdown_parent_category"
                    app:trackColor="@color/dropdown_border"/>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Info text -->
        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/text_info"
            style="@style/TextAppearance.Material3.BodyMedium"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:textColor="@color/text_secondary"
            android:visibility="gone"
            android:textAlignment="center"
            android:padding="16dp"
            android:background="@drawable/info_background"
            tools:text="This category requires a part number for AI compatibility analysis"
            tools:visibility="visible"/>

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>


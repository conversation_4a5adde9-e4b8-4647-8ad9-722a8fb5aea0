import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/client';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();

    // Fetch all active categories from the database
    const { data: categoriesRaw, error } = await supabase
      .from('car_part_categories')
      .select('*')
      .eq('isActive', true)
      .order('label');

    if (error) {
      console.error('Error fetching categories:', error);
      return NextResponse.json(
        { error: 'Failed to fetch categories', details: error.message },
        { status: 500 }
      );
    }

    // Map parent_category_id to parent for Android compatibility
    const categories = categoriesRaw?.map(cat => ({
      id: cat.id,
      label: cat.label,
      parent: cat.parent_category_id,  // ← Map parent_category_id to parent
      requirePartNumber: cat.requirePartNumber || false
    })) || [];

    // For Android app, return flat categories with proper parent field mapping
    // The Android app will build the hierarchy client-side
    return NextResponse.json(categories);

  } catch (error) {
    console.error('Unexpected error in categories API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

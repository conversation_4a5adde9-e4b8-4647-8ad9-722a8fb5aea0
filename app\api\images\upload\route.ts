// app/api/images/upload/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { checkApiAuth } from '@/app/utils/apiAuth';

export async function POST(request: NextRequest) {
  try {
    // Require authentication (cookies or Bearer token)
    const { authenticated, supabase, errorResponse } = await checkApiAuth(request);
    if (!authenticated || !supabase) {
      return errorResponse!;
    }

    const finalSupabase = supabase;

    // Parse multipart form data
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { error: 'File must be an image' },
        { status: 400 }
      );
    }

    // Generate unique filename
    const timestamp = Date.now();
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const filename = `part_image_${timestamp}.${fileExtension}`;

    // Convert file to buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Upload to Supabase Storage (same bucket as web)
    const { data: uploadData, error: uploadError } = await finalSupabase!.storage
      .from('car-part-images')
      .upload(filename, buffer, {
        contentType: file.type,
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) {
      console.error('Supabase upload error:', uploadError);
      return NextResponse.json(
        { error: 'Failed to upload image', details: uploadError.message },
        { status: 500 }
      );
    }

    // Get the public URL
    const { data: { publicUrl } } = finalSupabase!.storage
      .from('car-part-images')
      .getPublicUrl(filename);

    return NextResponse.json({
      imageUrl: publicUrl,
      success: true,
      message: 'Image uploaded successfully'
    });

  } catch (error) {
    console.error('Image upload API error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to upload image';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

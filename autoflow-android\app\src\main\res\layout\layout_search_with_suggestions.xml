<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <!-- Search Input Container -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:orientation="horizontal"
        android:background="@drawable/search_input_background"
        android:gravity="center_vertical"
        android:paddingHorizontal="16dp">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_search"
            android:layout_marginEnd="12dp" />

        <EditText
            android:id="@+id/searchInputWithSuggestions"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@android:color/transparent"
            android:hint="Search for products, parts, or SKUs..."
            android:textColorHint="@color/text_secondary"
            android:textColor="@color/text_primary"
            android:textSize="14sp"
            android:inputType="text"
            android:imeOptions="actionSearch"
            android:maxLines="1"
            android:singleLine="true" />

        <ImageButton
            android:id="@+id/clearSearchButtonWithSuggestions"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_clear"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Clear search"
            android:visibility="gone"
            android:padding="4dp" />

    </LinearLayout>

    <!-- Suggestions Dropdown -->
    <androidx.cardview.widget.CardView
        android:id="@+id/suggestionsCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:visibility="gone"
        app:cardCornerRadius="8dp"
        app:cardElevation="8dp"
        app:cardBackgroundColor="@android:color/white"
        xmlns:app="http://schemas.android.com/apk/res-auto">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:maxHeight="300dp">

            <!-- Suggestions Header -->
            <LinearLayout
                android:id="@+id/suggestionsHeader"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:padding="16dp"
                android:visibility="gone">

                <TextView
                    android:id="@+id/suggestionsHeaderText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Recent searches"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/clearHistoryButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Clear all"
                    android:textColor="@color/brand_red"
                    android:textSize="12sp"
                    android:background="?attr/selectableItemBackground"
                    android:padding="4dp" />

            </LinearLayout>

            <!-- Suggestions RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/suggestionsRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:maxHeight="240dp"
                android:scrollbars="vertical" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</LinearLayout>

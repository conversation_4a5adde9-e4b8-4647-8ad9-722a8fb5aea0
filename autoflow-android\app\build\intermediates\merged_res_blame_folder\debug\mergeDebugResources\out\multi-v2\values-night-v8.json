{"logs": [{"outputFile": "com.autoflow.android.app-mergeDebugResources-45:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\303aa64de72ab8a914eac82254eebb45\\transformed\\appcompat-1.6.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "2,3,4,5,6,7,8,40", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,3812", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,3896"}}, {"source": "C:\\Users\\<USER>\\Node\\autoflow\\autoflow-android\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "7", "endColumns": "12", "endOffsets": "414"}, "to": {"startLines": "9", "startColumns": "4", "startOffsets": "687", "endLines": "14", "endColumns": "12", "endOffsets": "1046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd2d40d61e01e6ef2b0f2f90a3e8aca5\\transformed\\material-1.9.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1042,1166,1268,1370,1486,1588,1702,1830,1946,2068,2204,2324,2458,2578,2690,2816,2933,3057,3187,3309,3447,3581,3697", "endColumns": "74,110,88,100,106,106,98,106,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1037,1161,1263,1365,1481,1583,1697,1825,1941,2063,2199,2319,2453,2573,2685,2811,2928,3052,3182,3304,3442,3576,3692,3812"}, "to": {"startLines": "15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,41,42,43,44,45,46,47,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1051,1126,1237,1326,1427,1534,1641,1740,1847,1950,2038,2162,2264,2366,2482,2584,2698,2826,2942,3064,3200,3320,3454,3574,3686,3901,4018,4142,4272,4394,4532,4666,4782", "endColumns": "74,110,88,100,106,106,98,106,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "1121,1232,1321,1422,1529,1636,1735,1842,1945,2033,2157,2259,2361,2477,2579,2693,2821,2937,3059,3195,3315,3449,3569,3681,3807,4013,4137,4267,4389,4527,4661,4777,4897"}}]}]}
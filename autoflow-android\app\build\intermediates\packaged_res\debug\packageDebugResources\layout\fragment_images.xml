<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".ui.addpart.fragments.ImagesFragment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:dividerPadding="8dp">

            <!-- Instruction -->
            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/text_instructions"
                style="@style/TextAppearance.Material3.BodyLarge"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Add photos of your part. The first image will be the main image."
                android:textColor="@color/text_primary"
                android:paddingBottom="12dp"/>

            <!-- Preview Card with 1:1 Aspect Ratio -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_preview"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginBottom="16dp"
                app:cardUseCompatPadding="true"
                app:cardCornerRadius="16dp"
                app:layout_constraintDimensionRatio="1:1"
                app:strokeColor="@color/light_gray"
                app:strokeWidth="1dp"
                app:cardElevation="3dp">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_gravity="center"
                    android:layout_weight="1">

                    <ImageView
                        android:id="@+id/image_preview"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:contentDescription="Cropped image preview"
                        android:scaleType="centerCrop"
                        tools:src="@drawable/ic_menu_gallery"/>

                    <!-- Upload overlay -->
                    <FrameLayout
                        android:id="@+id/overlay_upload"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="#80000000"
                        android:visibility="gone">

                        <com.google.android.material.progressindicator.CircularProgressIndicator
                            android:id="@+id/progress_circular"
                            style="@style/Widget.Material3.CircularProgressIndicator"
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:layout_gravity="center"
                            app:indicatorSize="48dp"
                            app:trackThickness="4dp"/>
                    </FrameLayout>
                </FrameLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Thumbnails Grid -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_thumbnails"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:overScrollMode="never"
                tools:listitem="@layout/item_part_image"/>

            <!-- Linear progress + status -->
            <com.google.android.material.progressindicator.LinearProgressIndicator
                android:id="@+id/progress_linear"
                style="@style/Widget.Material3.LinearProgressIndicator"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"/>

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/text_status"
                style="@style/TextAppearance.Material3.BodyMedium"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/text_secondary"
                android:paddingTop="8dp"
                android:visibility="gone"
                tools:text="Uploading image..."/>

            <!-- Actions -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:orientation="horizontal">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_camera"
                    style="@style/Widget.Material3.Button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Camera"
                    app:icon="@android:drawable/ic_menu_camera"
                    app:iconGravity="textStart"
                    app:iconPadding="8dp"/>

                <Space
                    android:layout_width="12dp"
                    android:layout_height="1dp"/>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_gallery"
                    style="@style/Widget.Material3.Button.TonalButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Gallery"
                    app:icon="@android:drawable/ic_menu_gallery"
                    app:iconGravity="textStart"
                    app:iconPadding="8dp"/>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_retake"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Retake"/>

                <Space
                    android:layout_width="12dp"
                    android:layout_height="1dp"/>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_continue"
                    style="@style/Widget.Material3.Button"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Continue"/>
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>


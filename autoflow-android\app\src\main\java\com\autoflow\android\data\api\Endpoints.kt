package com.autoflow.android.data.api

import com.autoflow.android.data.api.dto.*
import okhttp3.MultipartBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

// Retrofit contracts matching server endpoints

interface CategoriesApi {
    // Android should mirror Next.js routes
    @GET("/api/categories") suspend fun getCategories(): Response<List<CategoryDto>>

    // Some deployments may expose categories under /api/parts/categories
    @GET("/api/parts/categories")
    suspend fun getSimpleCategories(): Response<List<SimpleCategoryDto>>

    // Debug helper for capturing unexpected HTML/error pages
    @GET("/api/categories") suspend fun getCategoriesRaw(): Response<okhttp3.ResponseBody>

    @GET("/api/categories/{id}/attributes")
    suspend fun getCategoryAttributes(@Path("id") id: Int): Response<List<AttributeDto>>
}

interface VehiclesApi {
    @GET("/api/car/models")
    suspend fun getModels(@Query("brandId") brandId: Int): Response<List<CarModelDto>>

    @GET("/api/car/generations")
    suspend fun getGenerations(@Query("modelId") modelId: Int): Response<List<CarGenerationDto>>

    @GET("/api/car/variations")
    suspend fun getVariations(
            @Query("generationId") generationId: Int
    ): Response<List<CarVariationDto>>

    @GET("/api/car/trims")
    suspend fun getTrims(@Query("variationId") variationId: Int): Response<List<CarTrimDto>>
}

interface PartsApi {
    @POST("/api/parts/check-compatibility")
    suspend fun checkCompatibility(@Body body: CheckCompatibilityBody): Response<CompatibilityDto>

    @POST("/api/parts/generate-title")
    suspend fun generateTitle(@Body body: GenerateTitleBody): Response<TitleDto>

    @POST("/api/parts/generate-description")
    suspend fun generateDescription(@Body body: GenerateDescriptionBody): Response<DescriptionDto>

    @POST("/api/parts/create")
    suspend fun createPart(
            @Header("Idempotency-Key") idempotencyKey: String,
            @Body body: CreatePartBody
    ): Response<CreatePartDto>
}

interface ImagesApi {
    @Multipart
    @POST("/api/images/upload")
    suspend fun uploadImage(
            @retrofit2.http.Part filePart: MultipartBody.Part
    ): Response<ImageUploadDto>
}

interface StorageApi {
    @GET("/api/storage/areas") suspend fun getAreas(): Response<List<StorageAreaDto>>

    @GET("/api/storage/units")
    suspend fun getUnits(@Query("areaId") areaId: Int?): Response<List<StorageUnitDto>>

    @POST("/api/parts/{partId}/locations")
    suspend fun saveLocation(
            @Path("partId") partId: Int,
            @Body body: SaveLocationBody
    ): Response<Unit>
}

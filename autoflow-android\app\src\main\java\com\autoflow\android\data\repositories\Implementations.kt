package com.autoflow.android.data.repositories

import android.content.Context
import android.net.Uri
import android.util.Log
import com.autoflow.android.core.auth.AuthManager
import com.autoflow.android.data.api.ApiResult
import com.autoflow.android.data.api.RetrofitServices
import com.autoflow.android.data.api.dto.*
import java.io.File
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import okio.buffer

class ImagesRepositoryImpl(private val context: Context, private val auth: AuthManager) :
        ImagesRepository {
    override suspend fun uploadImage(
            uri: Uri,
            filename: String,
            onProgress: (Int) -> Unit
    ): ApiResult<String> {
        return withContext(Dispatchers.IO) {
            try {
                // Ensure we have a valid bearer token before uploading
                val token = auth.getAuthToken()
                Log.d(
                        "ImagesRepository",
                        "Token check: token=${if (token.isNullOrBlank()) "NULL/BLANK" else "PRESENT(${token.take(20)}...)"}"
                )
                if (token.isNullOrBlank()) {
                    Log.w("ImagesRepository", "No auth token present. Aborting upload.")
                    return@withContext ApiResult.Error(
                            SecurityException("Unauthorized"),
                            "Session expired. Please sign in again."
                    )
                }

                val input =
                        context.contentResolver.openInputStream(uri)
                                ?: return@withContext ApiResult.Error(
                                        Exception("File not found"),
                                        "File not found"
                                )
                val temp = File.createTempFile("upload_", filename, context.cacheDir)
                temp.outputStream().use { out -> input.copyTo(out) }

                // Detect MIME type
                val contentResolverMime = context.contentResolver.getType(uri)
                val guessedMime = java.net.URLConnection.guessContentTypeFromName(filename)
                val mime = (contentResolverMime ?: guessedMime ?: "image/*").toMediaTypeOrNull()

                val requestBody = ProgressRequestBody(temp.asRequestBody(mime), onProgress)
                val part = MultipartBody.Part.createFormData("file", filename, requestBody)

                val resp = RetrofitServices.images(auth).uploadImage(part)
                Log.d(
                        "ImagesRepository",
                        "Upload response code=${resp.code()} success=${resp.isSuccessful}"
                )
                if (!resp.isSuccessful) {
                    val errorStr = runCatching { resp.errorBody()?.string() }.getOrNull()
                    Log.e("ImagesRepository", "Upload failed body=$errorStr")
                }

                if (resp.isSuccessful) {
                    ApiResult.Success(resp.body()!!.imageUrl)
                } else {
                    ApiResult.Error(
                            Exception("Upload failed ${resp.code()}"),
                            resp.errorBody()?.string() ?: "Upload failed"
                    )
                }
            } catch (e: Exception) {
                Log.e("ImagesRepository", "Upload exception: ${e.message}", e)
                ApiResult.Error(e, e.message ?: "Upload failed")
            }
        }
    }
}

class CategoriesRepositoryImpl(private val auth: AuthManager) : CategoriesRepository {
    override suspend fun getCategories(): ApiResult<List<CategoryDto>> = safeWrap {
        RetrofitServices.categories(auth).getCategories()
    }
    override suspend fun getAttributes(categoryId: Int): ApiResult<List<AttributeDto>> = safeWrap {
        RetrofitServices.categories(auth).getCategoryAttributes(categoryId)
    }
}

class VehiclesRepositoryImpl(private val auth: AuthManager) : VehiclesRepository {
    override suspend fun getModels(brandId: Int) = safeWrap {
        RetrofitServices.vehicles(auth).getModels(brandId)
    }
    override suspend fun getGenerations(modelId: Int) = safeWrap {
        RetrofitServices.vehicles(auth).getGenerations(modelId)
    }
    override suspend fun getVariations(generationId: Int) = safeWrap {
        RetrofitServices.vehicles(auth).getVariations(generationId)
    }
    override suspend fun getTrims(variationId: Int) = safeWrap {
        RetrofitServices.vehicles(auth).getTrims(variationId)
    }
}

class CompatibilityRepositoryImpl(private val auth: AuthManager) : CompatibilityRepository {
    override suspend fun checkCompatibility(body: CheckCompatibilityBody) = safeWrap {
        RetrofitServices.parts(auth).checkCompatibility(body)
    }
}

class PartsRepositoryImpl(private val auth: AuthManager) : PartsRepository {
    override suspend fun generateTitle(body: GenerateTitleBody) = safeWrap {
        RetrofitServices.parts(auth).generateTitle(body)
    }
    override suspend fun generateDescription(body: GenerateDescriptionBody) = safeWrap {
        RetrofitServices.parts(auth).generateDescription(body)
    }
    override suspend fun createPart(idempotencyKey: String, body: CreatePartBody) = safeWrap {
        RetrofitServices.parts(auth).createPart(idempotencyKey, body)
    }
}

class StorageRepositoryImpl(private val auth: AuthManager) : StorageRepository {
    override suspend fun getAreas() = safeWrap { RetrofitServices.storage(auth).getAreas() }
    override suspend fun getUnits(areaId: Int?) = safeWrap {
        RetrofitServices.storage(auth).getUnits(areaId)
    }
    override suspend fun saveLocation(partId: Int, body: SaveLocationBody) = safeWrap {
        RetrofitServices.storage(auth).saveLocation(partId, body)
    }
}

// helper to convert retrofit Response<T> into ApiResult<T>
private suspend fun <T> safeWrap(call: suspend () -> retrofit2.Response<T>): ApiResult<T> {
    return try {
        val resp = call()
        if (resp.isSuccessful) {
            val body = resp.body()
            if (body != null) ApiResult.Success(body)
            else ApiResult.Error(IllegalStateException("Empty body"), "Empty body")
        } else if (resp.code() == 401) {
            ApiResult.Error(SecurityException("Unauthorized"), "Unauthorized")
        } else {
            ApiResult.Error(
                    Exception("HTTP ${resp.code()}"),
                    resp.errorBody()?.string() ?: "HTTP ${resp.code()}"
            )
        }
    } catch (e: Exception) {
        ApiResult.Error(e, e.message ?: "Network error")
    }
}

// RequestBody wrapper to report progress
private class ProgressRequestBody(
        private val delegate: okhttp3.RequestBody,
        private val onProgress: (Int) -> Unit
) : okhttp3.RequestBody() {
    override fun contentType() = delegate.contentType()
    override fun contentLength() = delegate.contentLength()
    override fun writeTo(sink: okio.BufferedSink) {
        val totalBytes = contentLength()
        var bytesWritten = 0L
        val countingSink =
                object : okio.ForwardingSink(sink) {
                    override fun write(source: okio.Buffer, byteCount: Long) {
                        super.write(source, byteCount)
                        bytesWritten += byteCount
                        if (totalBytes > 0) {
                            onProgress(((bytesWritten * 100) / totalBytes).toInt())
                        }
                    }
                }
        val buffered = countingSink.buffer()
        delegate.writeTo(buffered)
        buffered.flush()
    }
}

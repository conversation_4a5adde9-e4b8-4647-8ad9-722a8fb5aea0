package com.autoflow.android.core.auth

import android.content.Context
import android.content.SharedPreferences
import android.util.Base64
import android.util.Log
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import com.autoflow.android.data.api.ApiResult
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST

/** Secure authentication manager for Add Part flow Handles JWT tokens and user session state */
class AuthManager(private val context: Context) {

    companion object {
        private const val PREFS_NAME = "autoflow_auth_prefs"
        private const val KEY_AUTH_TOKEN = "auth_token"
        private const val KEY_REFRESH_TOKEN = "refresh_token"
        private const val KEY_USER_ID = "user_id"
        private const val KEY_USER_NAME = "user_name"
        private const val KEY_USER_ROLE = "user_role"
        private const val KEY_TOKEN_EXPIRY = "token_expiry"

        // Supabase project settings (match AuthRepository)
        private const val SUPABASE_URL = "https://excgraelqcvcdsnlvrtv.supabase.co"
        private const val SUPABASE_ANON_KEY =
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV4Y2dyYWVscWN2Y2Rzbmx2cnR2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NjcyNjAsImV4cCI6MjA1NDU0MzI2MH0.vPmu8B_MO6Nfl5LSum9WBYbOjlG8HO5L7AormN48RAQ"
    }

    private val masterKey =
            MasterKey.Builder(context).setKeyScheme(MasterKey.KeyScheme.AES256_GCM).build()

    private val encryptedPrefs: SharedPreferences =
            EncryptedSharedPreferences.create(
                    context,
                    PREFS_NAME,
                    masterKey,
                    EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                    EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            )

    private val _authState = MutableStateFlow(AuthState())
    val authState: StateFlow<AuthState> = _authState.asStateFlow()

    private val _isLoggedIn = MutableStateFlow(false)
    val isLoggedIn: StateFlow<Boolean> = _isLoggedIn.asStateFlow()

    // Emit when a token refresh succeeds (epoch millis)
    private val _tokenRefreshedAt = MutableStateFlow<Long?>(null)
    val tokenRefreshedAt: StateFlow<Long?> = _tokenRefreshedAt.asStateFlow()

    // True when user action is required to re-authenticate
    private val _reauthRequired = MutableStateFlow(false)
    val reauthRequired: StateFlow<Boolean> = _reauthRequired.asStateFlow()

    init {
        android.util.Log.d("AuthManager", "AuthManager instance created: ${this.hashCode()}")
        loadAuthState()
    }

    /** Get current auth token for API calls */
    fun getAuthToken(): String? {
        val token = encryptedPrefs.getString(KEY_AUTH_TOKEN, null)
        val expiry = encryptedPrefs.getLong(KEY_TOKEN_EXPIRY, 0)

        if (token != null && System.currentTimeMillis() < expiry) {
            android.util.Log.d(
                    "AuthManager",
                    "Using encrypted token (expires: ${java.util.Date(expiry)})"
            )
            return token
        }

        // Fallback: read Supabase session saved by AuthRepository (unencrypted SharedPreferences)
        return try {
            val legacyPrefs: SharedPreferences =
                    context.getSharedPreferences("autoflow_auth", Context.MODE_PRIVATE)
            val sessionJson = legacyPrefs.getString("session", null)
            if (!sessionJson.isNullOrBlank()) {
                val obj = JSONObject(sessionJson)
                val accessToken = obj.optString("access_token")
                if (accessToken.isNotBlank()) {
                    // Validate JWT exp
                    val exp = decodeJwtExp(accessToken)
                    if (exp != null && exp * 1000L > System.currentTimeMillis()) {
                        android.util.Log.d(
                                "AuthManager",
                                "Using Supabase session token (exp: ${java.util.Date(exp * 1000L)})"
                        )
                        return accessToken
                    } else {
                        android.util.Log.w("AuthManager", "Supabase token expired")
                        // Don't try to refresh here - let the authenticator handle it
                        // This avoids NetworkOnMainThreadException when called from interceptor
                        null
                    }
                } else {
                    android.util.Log.w("AuthManager", "No access_token in session")
                    null
                }
            } else {
                android.util.Log.w("AuthManager", "No session found in legacy prefs")
                null
            }
        } catch (e: Exception) {
            android.util.Log.e("AuthManager", "Error reading legacy session", e)
            null
        }
    }

    /**
     * Returns true if we currently have a non-expired bearer token (encrypted or Supabase session).
     */
    fun hasValidToken(): Boolean = getAuthToken() != null

    /** Returns JWT exp (epoch seconds) if available for the current token. */
    fun getTokenExpiryEpochSeconds(): Long? = getAuthToken()?.let { decodeJwtExp(it) }

    /** Decode a JWT and extract exp (epoch seconds). No signature verification. */
    private fun decodeJwtExp(jwt: String): Long? {
        return try {
            val parts = jwt.split(".")
            if (parts.size < 2) return null
            val payload =
                    String(
                            Base64.decode(
                                    parts[1],
                                    Base64.URL_SAFE or Base64.NO_PADDING or Base64.NO_WRAP
                            )
                    )
            val json = JSONObject(payload)
            if (json.has("exp")) json.getLong("exp") else null
        } catch (_: Exception) {
            null
        }
    }

    /** Get current user ID for part creation */
    fun getUserId(): Int? {
        return encryptedPrefs.getInt(KEY_USER_ID, -1).takeIf { it != -1 }
    }

    /** Get current user info */
    fun getCurrentUser(): UserInfo? {
        val userId = getUserId() ?: return null
        val userName = encryptedPrefs.getString(KEY_USER_NAME, null) ?: return null
        val userRole = encryptedPrefs.getString(KEY_USER_ROLE, null) ?: return null

        return UserInfo(id = userId, name = userName, role = userRole)
    }

    /** Save authentication data after successful login */
    fun saveAuthData(
            token: String,
            refreshToken: String? = null,
            userId: Int,
            userName: String,
            userRole: String,
            expiryTimeMillis: Long =
                    System.currentTimeMillis() + (24 * 60 * 60 * 1000) // 24 hours default
    ) {
        encryptedPrefs.edit().apply {
            putString(KEY_AUTH_TOKEN, token)
            refreshToken?.let { putString(KEY_REFRESH_TOKEN, it) }
            putInt(KEY_USER_ID, userId)
            putString(KEY_USER_NAME, userName)
            putString(KEY_USER_ROLE, userRole)
            putLong(KEY_TOKEN_EXPIRY, expiryTimeMillis)
            apply()
        }

        updateAuthState()
    }

    /** Clear all authentication data */
    fun clearAuth() {
        encryptedPrefs.edit().clear().apply()
        updateAuthState()
    }

    /** Check if user is authenticated and token is valid */
    fun isAuthenticated(): Boolean {
        return getAuthToken() != null && getUserId() != null
    }

    /** Load auth state from encrypted preferences */
    private fun loadAuthState() {
        updateAuthState()
    }
    /** Time in millis when the current token expires, if known */
    fun getExpiryTimeMillis(): Long? {
        val encExpiry = encryptedPrefs.getLong(KEY_TOKEN_EXPIRY, 0L)
        if (encExpiry > 0) return encExpiry
        return try {
            val legacy = context.getSharedPreferences("autoflow_auth", Context.MODE_PRIVATE)
            val session = legacy.getString("session", null) ?: return null
            val obj = JSONObject(session)
            val access = obj.optString("access_token")
            val exp = access.takeIf { it.isNotBlank() }?.let { decodeJwtExp(it) } ?: return null
            exp * 1000L
        } catch (_: Exception) {
            null
        }
    }

    /** Update auth state flow */
    private fun updateAuthState() {
        val isAuth = isAuthenticated()
        val user = getCurrentUser()

        _authState.value =
                AuthState(
                        isAuthenticated = isAuth,
                        user = user,
                        token = if (isAuth) getAuthToken() else null
                )

        _isLoggedIn.value = isAuth
    }

    /** Refresh token if available (non-blocking helper used in some flows) */
    suspend fun refreshToken(): Boolean {
        return kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.IO) {
            refreshTokenBlocking() != null
        }
    }

    // Refresh token synchronously for OkHttp authenticator retry
    fun refreshTokenBlocking(): String? {
        synchronized(this) {
            return try {
                val legacyPrefs: SharedPreferences =
                        context.getSharedPreferences("autoflow_auth", Context.MODE_PRIVATE)
                val sessionJson = legacyPrefs.getString("session", null) ?: return null
                val obj = JSONObject(sessionJson)
                val refreshToken = obj.optString("refresh_token")
                if (refreshToken.isNullOrBlank()) return null

                // Supabase refresh via auth/v1/token?grant_type=refresh_token
                val url = "$SUPABASE_URL/auth/v1/token?grant_type=refresh_token"
                val body =
                        "{\"refresh_token\":\"$refreshToken\"}".toRequestBody(
                                "application/json".toMediaType()
                        )
                val req =
                        okhttp3.Request.Builder()
                                .url(url)
                                .post(body)
                                .addHeader("apikey", SUPABASE_ANON_KEY)
                                .addHeader("Content-Type", "application/json")
                                .build()
                val resp = okhttp3.OkHttpClient().newCall(req).execute()
                if (!resp.isSuccessful) return null
                val respStr = resp.body?.string() ?: return null
                resp.close()
                val refreshed = JSONObject(respStr)
                val newAccess = refreshed.optString("access_token")
                val newRefresh = refreshed.optString("refresh_token", refreshToken)
                val expiresIn = refreshed.optLong("expires_in", 3600L)
                if (newAccess.isNullOrBlank()) return null

                // Persist into encrypted prefs with expiry
                val expiryMillis = System.currentTimeMillis() + expiresIn * 1000L
                encryptedPrefs
                        .edit()
                        .putString(KEY_AUTH_TOKEN, newAccess)
                        .putString(KEY_REFRESH_TOKEN, newRefresh)
                        .putLong(KEY_TOKEN_EXPIRY, expiryMillis)
                        .apply()

                updateAuthState()
                _tokenRefreshedAt.value = System.currentTimeMillis()
                android.util.Log.d(
                        "AuthManager",
                        "Token refreshed; expires=${'$'}{java.util.Date(expiryMillis)}"
                )
                newAccess
            } catch (e: Exception) {
                android.util.Log.e(
                        "AuthManager",
                        "refreshTokenBlocking error: ${'$'}{e.message}",
                        e
                )
                null
            }
        }
    }
}

/** Authentication state data class */
data class AuthState(
        val isAuthenticated: Boolean = false,
        val user: UserInfo? = null,
        val token: String? = null,
        val isLoading: Boolean = false,
        val error: String? = null
)

/** User information data class */
data class UserInfo(val id: Int, val name: String, val role: String, val email: String? = null)

/** Login request/response models */
data class LoginRequest(val email: String, val password: String)

data class LoginResponse(
        val success: Boolean,
        val token: String? = null,
        val refreshToken: String? = null,
        val user: UserInfo? = null,
        val expiresIn: Long? = null,
        val message: String = ""
)

/** Auth API service interface */
interface AuthApiService {
    @POST("api/auth/login") suspend fun login(@Body request: LoginRequest): Response<LoginResponse>

    @POST("api/auth/refresh")
    suspend fun refreshToken(@Body refreshToken: String): Response<LoginResponse>

    @POST("api/auth/logout") suspend fun logout(): Response<Unit>
}

/** Auth repository for handling authentication operations */
class AuthRepository(
        private val authManager: AuthManager,
        private val authApiService: AuthApiService
) {

    suspend fun login(email: String, password: String): ApiResult<UserInfo> {
        return try {
            val response = authApiService.login(LoginRequest(email, password))

            if (response.isSuccessful) {
                val loginResponse = response.body()

                if (loginResponse?.success == true &&
                                loginResponse.token != null &&
                                loginResponse.user != null
                ) {
                    // Save auth data
                    val expiryTime =
                            System.currentTimeMillis() +
                                    (loginResponse.expiresIn ?: (24 * 60 * 60 * 1000))

                    authManager.saveAuthData(
                            token = loginResponse.token,
                            refreshToken = loginResponse.refreshToken,
                            userId = loginResponse.user.id,
                            userName = loginResponse.user.name,
                            userRole = loginResponse.user.role,
                            expiryTimeMillis = expiryTime
                    )

                    ApiResult.Success(loginResponse.user)
                } else {
                    ApiResult.Error(
                            Exception("Login failed"),
                            loginResponse?.message ?: "Invalid credentials"
                    )
                }
            } else {
                ApiResult.Error(Exception("HTTP ${response.code()}"), response.message())
            }
        } catch (e: Exception) {
            ApiResult.Error(e, e.message ?: "Network error")
        }
    }

    suspend fun logout(): ApiResult<Unit> {
        return try {
            authApiService.logout()
            authManager.clearAuth()
            ApiResult.Success(Unit)
        } catch (e: Exception) {
            // Clear local auth even if API call fails
            authManager.clearAuth()
            ApiResult.Success(Unit)
        }
    }

    fun isLoggedIn(): Boolean = authManager.isAuthenticated()

    fun getCurrentUser(): UserInfo? = authManager.getCurrentUser()
}

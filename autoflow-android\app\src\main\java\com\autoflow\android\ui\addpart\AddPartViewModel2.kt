package com.autoflow.android.ui.addpart

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.autoflow.android.core.auth.AuthManager
import com.autoflow.android.core.util.IdempotencyKeyProvider
import com.autoflow.android.data.api.ApiResult
import com.autoflow.android.data.api.dto.CheckCompatibilityBody
import com.autoflow.android.data.api.dto.CreatePartBody
import com.autoflow.android.data.api.dto.CreatePartImageDto
import com.autoflow.android.data.api.dto.StockPriceDto
import com.autoflow.android.data.api.dto.VehiclesPayloadDto
import com.autoflow.android.data.mappers.toDomain
import com.autoflow.android.data.repositories.*
import com.autoflow.android.domain.models.CompatibilityData
import com.autoflow.android.ui.addpart.state.AddPartState
import com.autoflow.android.ui.addpart.state.PartImageUiState
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class AddPartViewModel2(
        private val imagesRepo: ImagesRepository,
        private val categoriesRepo: CategoriesRepository,
        private val vehiclesRepo: VehiclesRepository,
        private val compatibilityRepo: CompatibilityRepository,
        private val partsRepo: PartsRepository,
        private val storageRepo: StorageRepository,
        private val authManager: AuthManager
) : ViewModel() {

    private val _state = MutableStateFlow(AddPartState())
    val state: StateFlow<AddPartState> = _state.asStateFlow()

    fun addLocalImage(uri: String) {
        _state.value =
                _state.value.copy(
                        images =
                                _state.value.images +
                                        PartImageUiState(localUri = uri, isUploading = true)
                )
    }

    fun uploadImageAt(index: Int, filename: String) {
        val item = _state.value.images.getOrNull(index) ?: return
        if (item.localUri == null) return
        viewModelScope.launch {
            when (val res =
                            imagesRepo.uploadImage(
                                    android.net.Uri.parse(item.localUri),
                                    filename
                            ) { p ->
                                val list = _state.value.images.toMutableList()
                                list[index] = list[index].copy(progress = p, isUploading = p < 100)
                                _state.value = _state.value.copy(images = list)
                            }
            ) {
                is ApiResult.Success -> {
                    val list = _state.value.images.toMutableList()
                    list[index] =
                            list[index].copy(url = res.data, isUploading = false, progress = 100)
                    _state.value = _state.value.copy(images = list)
                }
                is ApiResult.Error -> {
                    val list = _state.value.images.toMutableList()
                    list[index] = list[index].copy(error = res.message, isUploading = false)
                    _state.value = _state.value.copy(images = list)
                }
                else -> {}
            }
        }
    }

    fun selectCategory(categoryId: Int, requirePartNumber: Boolean) {
        _state.value =
                _state.value.copy(
                        selectedCategory = categoryId,
                        requirePartNumber = requirePartNumber
                )
        // Prefetch attributes
        viewModelScope.launch {
            when (val res = categoriesRepo.getAttributes(categoryId)) {
                is ApiResult.Success ->
                        _state.value =
                                _state.value.copy(
                                        categoryAttributes = res.data.map { it.toDomain() }
                                )
                is ApiResult.Error -> _state.value = _state.value.copy(errorMessage = res.message)
                else -> {}
            }
        }
    }

    fun checkPartNumber() {
        val now = System.currentTimeMillis()
        if (_state.value.pnCooldownUntilMillis > now) return
        val pn = _state.value.partNumberInput
        val category = _state.value.selectedCategory ?: return
        _state.value = _state.value.copy(isLoading = true)
        viewModelScope.launch {
            when (val res =
                            compatibilityRepo.checkCompatibility(
                                    CheckCompatibilityBody(pn, category)
                            )
            ) {
                is ApiResult.Success -> {
                    val domain: CompatibilityData = res.data.toDomain(pn)
                    _state.value =
                            _state.value.copy(
                                    compatibilityData = domain,
                                    isLoading = false,
                                    pnCooldownUntilMillis = now + 30_000
                            )
                }
                is ApiResult.Error ->
                        _state.value =
                                _state.value.copy(isLoading = false, errorMessage = res.message)
                else -> {}
            }
        }
    }

    fun submitPart(createdBy: Int) {
        val st = _state.value
        val categoryId = st.selectedCategory ?: return
        if (st.images.none { it.url != null }) return
        viewModelScope.launch {
            _state.value = _state.value.copy(isLoading = true, errorMessage = null)

            // Generate title & description in parallel
            val titleDeferred = async {
                partsRepo.generateTitle(
                        com.autoflow.android.data.api.dto.GenerateTitleBody(
                                categoryId = categoryId,
                                images = st.images.mapNotNull { it.url },
                                attributes = st.attributeValues.mapKeys { it.key.toString() }
                        )
                )
            }
            val descDeferred = async {
                partsRepo.generateDescription(
                        com.autoflow.android.data.api.dto.GenerateDescriptionBody(
                                categoryId = categoryId,
                                title = st.titlePreview ?: "",
                                attributes = st.attributeValues.mapKeys { it.key.toString() }
                        )
                )
            }

            val titleRes = titleDeferred.await()
            val descRes = descDeferred.await()
            val title =
                    (titleRes as? ApiResult.Success<com.autoflow.android.data.api.dto.TitleDto>)
                            ?.data
                            ?.title
            val description =
                    (descRes as?
                                    ApiResult.Success<
                                            com.autoflow.android.data.api.dto.DescriptionDto>)
                            ?.data
                            ?.description

            if (title == null || description == null) {
                _state.value =
                        _state.value.copy(
                                isLoading = false,
                                errorMessage = "Failed to generate title/description"
                        )
                return@launch
            }

            val idempotencyKey = IdempotencyKeyProvider.newKey()
            val body =
                    CreatePartBody(
                            category_id = categoryId,
                            title = title,
                            description = description,
                            partnumber_group = st.compatibilityData?.partnumberGroup?.toLong(),
                            createdBy = createdBy,
                            images =
                                    st.images.mapNotNull { it.url }.mapIndexed { idx, url ->
                                        CreatePartImageDto(
                                                url = url,
                                                isMain = st.images.getOrNull(idx)?.isMain
                                                                ?: (idx == 0)
                                        )
                                    },
                            condition = st.condition.name,
                            new =
                                    st.newStock?.let {
                                        StockPriceDto(it.stock, it.price, it.discountedPrice)
                                    },
                            used =
                                    st.usedStock?.let {
                                        StockPriceDto(it.stock, it.price, it.discountedPrice)
                                    },
                            attributes =
                                    st.attributeValues.map { (k, v) ->
                                        com.autoflow.android.data.api.dto.AttributeValueDto(
                                                attribute_id = k,
                                                value = v.toString()
                                        )
                                    },
                            vehicles =
                                    st.vehicleSelection.trimId?.let {
                                        VehiclesPayloadDto("manual", null, it)
                                    }
                                            ?: st.compatibilityData?.let {
                                                VehiclesPayloadDto(
                                                        "compatibility",
                                                        it.vehicleCompatibility.map { vc ->
                                                            com.autoflow.android.data.api.dto
                                                                    .VehicleCompatibilityDto(
                                                                            vc.brand,
                                                                            vc.model,
                                                                            vc.generation,
                                                                            vc.variation.ifBlank {
                                                                                null
                                                                            },
                                                                            vc.trim
                                                                                    .takeIf {
                                                                                        it.isNotBlank()
                                                                                    }
                                                                                    ?.let {
                                                                                        listOf(it)
                                                                                    }
                                                                    )
                                                        },
                                                        null
                                                )
                                            },
                            engines =
                                    st.compatibilityData?.engineCompatibility?.map { it.engineCode }
                    )

            when (val created = partsRepo.createPart(idempotencyKey, body)) {
                is ApiResult.Success -> {
                    _state.value =
                            _state.value.copy(
                                    createdPartId = created.data.partId,
                                    createdPartTitle = created.data.title,
                                    isLoading = false
                            )
                }
                is ApiResult.Error -> {
                    _state.value =
                            _state.value.copy(isLoading = false, errorMessage = created.message)
                }
                else -> {}
            }
        }
    }
}

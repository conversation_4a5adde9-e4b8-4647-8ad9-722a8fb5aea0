package com.autoflow.android.domain.models

/** Category models for hierarchical category selection Mirrors web app's category structure */
data class CategoryResponse(
        val id: Int,
        val label: String,
        val parentCategoryId: Int? = null,
        val children: List<CategoryResponse> = emptyList(),
        val requiresPartNumber: Boolean = false,
        val hasAttributes: Boolean = false
)

data class CategoryAttribute(
        val id: Int,
        val attribute: String,
        val inputType: String, // "text", "select", "radio", "checkbox"
        val isRequired: Boolean = false,
        val dependsOnAttributeId: Int? = null,
        val dependsOnValue: String? = null,
        val options: List<AttributeOption> = emptyList()
)

data class AttributeOption(
        val id: Int,
        val value: String,
        val attributeId: Int,
        val displayOrder: Int = 0
)

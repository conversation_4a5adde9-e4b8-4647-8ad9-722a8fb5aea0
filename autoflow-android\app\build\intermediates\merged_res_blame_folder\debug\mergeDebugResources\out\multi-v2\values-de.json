{"logs": [{"outputFile": "com.autoflow.android.app-mergeDebugResources-45:/values-de/values-de.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ed7078e8e71893b82f32857a328dfc89\\transformed\\biometric-1.1.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,259,378,510,645,786,908,1065,1164,1310,1498", "endColumns": "108,94,118,131,134,140,121,156,98,145,187,127", "endOffsets": "159,254,373,505,640,781,903,1060,1159,1305,1493,1621"}, "to": {"startLines": "48,49,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4531,4640,4895,5014,5146,5281,5422,5544,5701,5800,5946,6134", "endColumns": "108,94,118,131,134,140,121,156,98,145,187,127", "endOffsets": "4635,4730,5009,5141,5276,5417,5539,5696,5795,5941,6129,6257"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\303aa64de72ab8a914eac82254eebb45\\transformed\\appcompat-1.6.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,438,536,648,734,840,955,1033,1108,1200,1294,1390,1491,1598,1698,1802,1900,1998,2095,2177,2288,2390,2488,2595,2698,2802,2958,10382", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "433,531,643,729,835,950,1028,1103,1195,1289,1385,1486,1593,1693,1797,1895,1993,2090,2172,2283,2385,2483,2590,2693,2797,2953,3055,10459"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fd2d40d61e01e6ef2b0f2f90a3e8aca5\\transformed\\material-1.9.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,374,463,547,637,719,820,942,1023,1089,1183,1253,1312,1420,1486,1555,1613,1685,1749,1803,1931,1991,2053,2107,2185,2322,2414,2498,2643,2727,2813,2903,2960,3011,3077,3151,3233,3326,3400,3478,3550,3624,3716,3798,3887,3976,4050,4128,4214,4269,4336,4416,4500,4562,4626,4689,4796,4900,4999,5105,5166,5221", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,90,88,83,89,81,100,121,80,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,83,144,83,85,89,56,50,65,73,81,92,73,77,71,73,91,81,88,88,73,77,85,54,66,79,83,61,63,62,106,103,98,105,60,54,81", "endOffsets": "278,369,458,542,632,714,815,937,1018,1084,1178,1248,1307,1415,1481,1550,1608,1680,1744,1798,1926,1986,2048,2102,2180,2317,2409,2493,2638,2722,2808,2898,2955,3006,3072,3146,3228,3321,3395,3473,3545,3619,3711,3793,3882,3971,4045,4123,4209,4264,4331,4411,4495,4557,4621,4684,4791,4895,4994,5100,5161,5216,5298"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,50,51,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3060,3151,3240,3324,3414,4227,4328,4450,4735,4801,6262,6332,6391,6499,6565,6634,6692,6764,6828,6882,7010,7070,7132,7186,7264,7401,7493,7577,7722,7806,7892,7982,8039,8090,8156,8230,8312,8405,8479,8557,8629,8703,8795,8877,8966,9055,9129,9207,9293,9348,9415,9495,9579,9641,9705,9768,9875,9979,10078,10184,10245,10300", "endLines": "5,33,34,35,36,37,45,46,47,50,51,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113", "endColumns": "12,90,88,83,89,81,100,121,80,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,83,144,83,85,89,56,50,65,73,81,92,73,77,71,73,91,81,88,88,73,77,85,54,66,79,83,61,63,62,106,103,98,105,60,54,81", "endOffsets": "328,3146,3235,3319,3409,3491,4323,4445,4526,4796,4890,6327,6386,6494,6560,6629,6687,6759,6823,6877,7005,7065,7127,7181,7259,7396,7488,7572,7717,7801,7887,7977,8034,8085,8151,8225,8307,8400,8474,8552,8624,8698,8790,8872,8961,9050,9124,9202,9288,9343,9410,9490,9574,9636,9700,9763,9870,9974,10073,10179,10240,10295,10377"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1c5f58891eb309bb4a04ac21b97fad24\\transformed\\core-1.10.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3496,3594,3696,3796,3896,4004,4109,10464", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "3589,3691,3791,3891,3999,4104,4222,10560"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0e54198a82394605fc9efe0eed5d1d5f\\transformed\\jetified-ucrop-2.2.8\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,106,172,230,286,336", "endColumns": "50,65,57,55,49,49", "endOffsets": "101,167,225,281,331,381"}, "to": {"startLines": "116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4", "startOffsets": "10565,10616,10682,10740,10796,10846", "endColumns": "50,65,57,55,49,49", "endOffsets": "10611,10677,10735,10791,10841,10891"}}]}]}
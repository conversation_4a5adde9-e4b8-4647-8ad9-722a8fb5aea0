import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/client';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const categoryId = parseInt(params.id);
    
    if (isNaN(categoryId)) {
      return NextResponse.json(
        { error: 'Invalid category ID' },
        { status: 400 }
      );
    }

    const supabase = createClient();

    // Fetch category attributes (note: table has no is_required column)
    const { data: attributesRaw, error: attributesError } = await supabase
      .from('parts_category_attributes')
      .select(`
        id,
        attribute,
        input_type,
        depends_on_attribute_id,
        depends_on_option_id
      `)
      .eq('category_id', categoryId)
      .order('attribute');

    if (attributesError) {
      console.error('Error fetching category attributes:', attributesError);
      return NextResponse.json(
        { error: 'Failed to fetch category attributes', details: attributesError.message },
        { status: 500 }
      );
    }

    const attributes = (attributesRaw || []).map((a) => ({
      id: a.id,
      attribute: a.attribute,
      input_type: a.input_type,
      is_required: false, // DB has no explicit required flag currently
      depends_on_attribute_id: a.depends_on_attribute_id,
      depends_on_option_id: a.depends_on_option_id,
    }));

    // Fetch options for attributes that need them and remap keys to Android DTO
    const attributesWithOptions = await Promise.all(
      attributes.map(async (attribute) => {
        if (['radio', 'dropdown', 'checkbox'].includes(attribute.inputType)) {
          const { data: options, error: optionsError } = await supabase
            .from('parts_category_attribute_input_option')
            .select(`
              id,
              option_value,
              attribute_id
            `)
            .eq('attribute_id', attribute.id)
            .order('option_value');

          if (optionsError) {
            console.error('Error fetching attribute options:', optionsError);
            return { ...attribute, options: [] };
          }

          const mappedOptions = (options || []).map((o) => ({ id: o.id, value: o.option_value }));
          return { ...attribute, options: mappedOptions };
        }

        return { ...attribute, options: [] };
      })
    );

    // Return a plain array to match Retrofit interface
    return NextResponse.json(attributesWithOptions);

  } catch (error) {
    console.error('Unexpected error in category attributes API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

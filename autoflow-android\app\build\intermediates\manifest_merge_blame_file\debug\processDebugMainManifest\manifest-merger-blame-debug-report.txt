1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.autoflow.android"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="33" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
13-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:7:5-72
13-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:7:22-69
14    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
14-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:8:5-74
14-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:8:22-71
15
16    <!-- Camera and Storage permissions -->
17    <uses-permission android:name="android.permission.CAMERA" />
17-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:11:5-65
17-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:11:22-62
18    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:12:5-80
18-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:12:22-77
19    <uses-permission
19-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:13:5-14:38
20        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
20-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:13:22-78
21        android:maxSdkVersion="28" />
21-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:14:9-35
22
23    <!-- Camera feature -->
24    <uses-feature
24-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:17:5-85
25        android:name="android.hardware.camera"
25-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:17:19-57
26        android:required="false" />
26-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:17:58-82
27    <uses-feature
27-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:18:5-95
28        android:name="android.hardware.camera.autofocus"
28-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:18:19-67
29        android:required="false" />
29-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:18:68-92
30
31    <permission
31-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
32        android:name="com.autoflow.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
32-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
33        android:protectionLevel="signature" />
33-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
34
35    <uses-permission android:name="com.autoflow.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
35-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
35-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
36
37    <application
37-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:20:5-154:19
38        android:allowBackup="true"
38-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:21:9-35
39        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
39-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
40        android:debuggable="true"
41        android:extractNativeLibs="false"
42        android:icon="@drawable/ic_launcher"
42-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:22:9-45
43        android:label="@string/app_name"
43-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:23:9-41
44        android:supportsRtl="true"
44-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:24:9-35
45        android:testOnly="true"
46        android:theme="@style/Theme.AutoflowAndroid" >
46-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:25:9-53
47
48        <!-- Main Activity - Entry Point -->
49        <activity
49-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:28:9-37:20
50            android:name="com.autoflow.android.MainActivity"
50-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:29:13-41
51            android:exported="true"
51-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:30:13-36
52            android:label="@string/app_name"
52-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:31:13-45
53            android:theme="@style/Theme.AutoflowAndroid" >
53-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:32:13-57
54            <intent-filter>
54-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:33:13-36:29
55                <action android:name="android.intent.action.MAIN" />
55-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:34:17-69
55-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:34:25-66
56
57                <category android:name="android.intent.category.LAUNCHER" />
57-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:35:17-77
57-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:35:27-74
58            </intent-filter>
59        </activity>
60
61        <!-- Authentication Activities -->
62        <activity
62-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:40:9-43:60
63            android:name="com.autoflow.android.ui.auth.LoginActivity"
63-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:41:13-50
64            android:exported="false"
64-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:42:13-37
65            android:theme="@style/Theme.AutoflowAndroid" />
65-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:43:13-57
66        <activity
66-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:45:9-48:60
67            android:name="com.autoflow.android.ui.auth.OTPActivity"
67-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:46:13-48
68            android:exported="false"
68-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:47:13-37
69            android:theme="@style/Theme.AutoflowAndroid" />
69-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:48:13-57
70
71        <!-- Dashboard Activity -->
72        <activity
72-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:51:9-54:60
73            android:name="com.autoflow.android.ui.dashboard.DashboardActivity"
73-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:52:13-59
74            android:exported="false"
74-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:53:13-37
75            android:theme="@style/Theme.AutoflowAndroid" />
75-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:54:13-57
76
77        <!-- Search Activities -->
78        <activity
78-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:57:9-60:60
79            android:name="com.autoflow.android.ui.search.SearchResultsActivity"
79-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:58:13-60
80            android:exported="false"
80-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:59:13-37
81            android:theme="@style/Theme.AutoflowAndroid" />
81-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:60:13-57
82
83        <!-- Parts Management Activities -->
84        <activity
84-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:63:9-66:60
85            android:name="com.autoflow.android.ui.parts.PartsListActivity"
85-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:64:13-55
86            android:exported="false"
86-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:65:13-37
87            android:theme="@style/Theme.AutoflowAndroid" />
87-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:66:13-57
88        <activity
88-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:68:9-72:77
89            android:name="com.autoflow.android.ui.parts.PartDetailActivity"
89-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:69:13-56
90            android:exported="false"
90-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:70:13-37
91            android:parentActivityName="com.autoflow.android.ui.search.SearchResultsActivity"
91-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:72:13-74
92            android:theme="@style/Theme.AutoflowAndroid" />
92-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:71:13-57
93        <activity
93-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:74:9-78:73
94            android:name="com.autoflow.android.ui.parts.PartUpdateActivity"
94-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:75:13-56
95            android:exported="false"
95-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:76:13-37
96            android:parentActivityName="com.autoflow.android.ui.parts.PartDetailActivity"
96-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:78:13-70
97            android:theme="@style/Theme.AutoflowAndroid" />
97-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:77:13-57
98
99        <!-- Part Creation Flow Activities -->
100        <activity
100-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:81:9-85:76
101            android:name="com.autoflow.android.ui.parts.AddPartActivity"
101-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:82:13-53
102            android:exported="false"
102-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:83:13-37
103            android:parentActivityName="com.autoflow.android.ui.dashboard.DashboardActivity"
103-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:85:13-73
104            android:theme="@style/Theme.AutoflowAndroid" />
104-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:84:13-57
105        <activity
105-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:87:9-91:76
106            android:name="com.autoflow.android.ui.parts.CameraCaptureActivity"
106-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:88:13-59
107            android:exported="false"
107-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:89:13-37
108            android:parentActivityName="com.autoflow.android.ui.dashboard.DashboardActivity"
108-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:91:13-73
109            android:theme="@style/Theme.AutoflowAndroid" />
109-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:90:13-57
110        <activity
110-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:93:9-97:76
111            android:name="com.autoflow.android.ui.parts.ImageCropActivity"
111-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:94:13-55
112            android:exported="false"
112-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:95:13-37
113            android:parentActivityName="com.autoflow.android.ui.parts.CameraCaptureActivity"
113-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:97:13-73
114            android:theme="@style/Theme.AutoflowAndroid" />
114-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:96:13-57
115        <activity
115-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:99:9-103:72
116            android:name="com.autoflow.android.ui.parts.CategorySelectionActivity"
116-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:100:13-63
117            android:exported="false"
117-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:101:13-37
118            android:parentActivityName="com.autoflow.android.ui.parts.ImageCropActivity"
118-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:103:13-69
119            android:theme="@style/Theme.AutoflowAndroid" />
119-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:102:13-57
120        <activity
120-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:105:9-109:72
121            android:name="com.autoflow.android.ui.parts.SimpleCategorySelectionActivity"
121-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:106:13-69
122            android:exported="false"
122-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:107:13-37
123            android:parentActivityName="com.autoflow.android.ui.parts.ImageCropActivity"
123-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:109:13-69
124            android:theme="@style/Theme.AutoflowAndroid" />
124-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:108:13-57
125        <activity
125-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:111:9-115:72
126            android:name="com.autoflow.android.ui.parts.HierarchicalCategorySelectionActivity"
126-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:112:13-75
127            android:exported="false"
127-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:113:13-37
128            android:parentActivityName="com.autoflow.android.ui.parts.ImageCropActivity"
128-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:115:13-69
129            android:theme="@style/Theme.AutoflowAndroid" />
129-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:114:13-57
130        <activity
130-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:117:9-121:72
131            android:name="com.autoflow.android.ui.parts.WebReplicaCategorySelectionActivity"
131-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:118:13-73
132            android:exported="false"
132-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:119:13-37
133            android:parentActivityName="com.autoflow.android.ui.parts.ImageCropActivity"
133-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:121:13-69
134            android:theme="@style/Theme.AutoflowAndroid" />
134-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:120:13-57
135
136        <!-- Complete Add Part Flow Activities -->
137        <activity
137-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:124:9-128:72
138            android:name="com.autoflow.android.ui.addpart.AddPartActivity"
138-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:125:13-55
139            android:exported="false"
139-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:126:13-37
140            android:parentActivityName="com.autoflow.android.ui.parts.ImageCropActivity"
140-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:128:13-69
141            android:theme="@style/Theme.AutoflowAndroid" />
141-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:127:13-57
142        <activity
142-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:130:9-134:72
143            android:name="com.autoflow.android.ui.storage.StorageLocationActivity"
143-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:131:13-63
144            android:exported="false"
144-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:132:13-37
145            android:parentActivityName="com.autoflow.android.ui.addpart.AddPartActivity"
145-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:134:13-69
146            android:theme="@style/Theme.AutoflowAndroid" />
146-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:133:13-57
147
148        <!-- UCrop Activity for image cropping -->
149        <activity
149-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:137:9-141:72
150            android:name="com.yalantis.ucrop.UCropActivity"
150-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:138:13-60
151            android:exported="false"
151-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:139:13-37
152            android:screenOrientation="portrait"
152-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:140:13-49
153            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
153-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:141:13-69
154
155        <!-- FileProvider for sharing images -->
156        <provider
157            android:name="androidx.core.content.FileProvider"
157-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:145:13-62
158            android:authorities="com.autoflow.android.fileprovider"
158-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:146:13-64
159            android:exported="false"
159-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:147:13-37
160            android:grantUriPermissions="true" >
160-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:148:13-47
161            <meta-data
161-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:149:13-151:54
162                android:name="android.support.FILE_PROVIDER_PATHS"
162-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:150:17-67
163                android:resource="@xml/file_paths" />
163-->C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\AndroidManifest.xml:151:17-51
164        </provider>
165
166        <service
166-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\894a783dca4aa815eb545af54b4b48b6\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:24:9-33:19
167            android:name="androidx.camera.core.impl.MetadataHolderService"
167-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\894a783dca4aa815eb545af54b4b48b6\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:25:13-75
168            android:enabled="false"
168-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\894a783dca4aa815eb545af54b4b48b6\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:26:13-36
169            android:exported="false" >
169-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\894a783dca4aa815eb545af54b4b48b6\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:27:13-37
170            <meta-data
170-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\894a783dca4aa815eb545af54b4b48b6\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:30:13-32:89
171                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
171-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\894a783dca4aa815eb545af54b4b48b6\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:31:17-103
172                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
172-->[androidx.camera:camera-camera2:1.2.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\894a783dca4aa815eb545af54b4b48b6\transformed\jetified-camera-camera2-1.2.3\AndroidManifest.xml:32:17-86
173        </service>
174
175        <provider
175-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe349995b136e85f6a005f5d89fa32f9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
176            android:name="androidx.startup.InitializationProvider"
176-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe349995b136e85f6a005f5d89fa32f9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
177            android:authorities="com.autoflow.android.androidx-startup"
177-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe349995b136e85f6a005f5d89fa32f9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
178            android:exported="false" >
178-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe349995b136e85f6a005f5d89fa32f9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
179            <meta-data
179-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe349995b136e85f6a005f5d89fa32f9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
180                android:name="androidx.emoji2.text.EmojiCompatInitializer"
180-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe349995b136e85f6a005f5d89fa32f9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
181                android:value="androidx.startup" />
181-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe349995b136e85f6a005f5d89fa32f9\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
182            <meta-data
182-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ece72575c0e3a0c9796d85af5708f2d2\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
183                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
183-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ece72575c0e3a0c9796d85af5708f2d2\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
184                android:value="androidx.startup" />
184-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ece72575c0e3a0c9796d85af5708f2d2\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
185            <meta-data
185-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
186                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
186-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
187                android:value="androidx.startup" />
187-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
188        </provider>
189
190        <receiver
190-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
191            android:name="androidx.profileinstaller.ProfileInstallReceiver"
191-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
192            android:directBootAware="false"
192-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
193            android:enabled="true"
193-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
194            android:exported="true"
194-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
195            android:permission="android.permission.DUMP" >
195-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
196            <intent-filter>
196-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
197                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
197-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
197-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
198            </intent-filter>
199            <intent-filter>
199-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
200                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
200-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
200-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
201            </intent-filter>
202            <intent-filter>
202-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
203                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
203-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
203-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
204            </intent-filter>
205            <intent-filter>
205-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
206                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
206-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
206-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
207            </intent-filter>
208        </receiver>
209    </application>
210
211</manifest>

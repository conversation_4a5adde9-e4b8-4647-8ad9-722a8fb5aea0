package com.autoflow.android.data.api

import android.content.Context
import android.util.Log
import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.HttpURLConnection
import java.net.URL
import java.net.URLEncoder
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject

data class SearchResponse(
        val results: List<Part>,
        val totalResults: Int,
        val currentPage: Int,
        val totalPages: Int,
        val pageSize: Int
)

/** API Service for parts search functionality */
class PartsApiService(private val context: Context) {

    companion object {
        private const val TAG = "PartsApiService"
        // Use the correct production API URL - same as web version
        private const val BASE_URL = "https://autoflow.parts"
        private const val SEARCH_ENDPOINT = "/api/parts/search"

        private const val PARTS_ENDPOINT = "/api/parts"
    }

    /** Search for parts using the API with filters and sorting */
    suspend fun searchParts(
            query: String,
            filters: com.autoflow.android.ui.search.SearchFilters =
                    com.autoflow.android.ui.search.SearchFilters(),
            sortBy: com.autoflow.android.ui.search.SortOption =
                    com.autoflow.android.ui.search.SortOption.RELEVANCE,
            page: Int = 1,
            pageSize: Int = 20
    ): SearchResponse =
            withContext(Dispatchers.IO) {
                try {
                    Log.d(
                            TAG,
                            "Searching for parts with query: $query, filters: $filters, sort: $sortBy"
                    )

                    // Build query parameters with pagination
                    val queryParams = buildQueryParameters(query, filters, sortBy, page, pageSize)
                    val urlString = "$BASE_URL$SEARCH_ENDPOINT?$queryParams"

                    val url = URL(urlString)
                    val connection = url.openConnection() as HttpURLConnection

                    connection.apply {
                        requestMethod = "GET"
                        setRequestProperty("Content-Type", "application/json")
                        setRequestProperty("Accept", "application/json")
                        setRequestProperty("User-Agent", "AutoflowAndroid/1.0")
                        // Add authentication headers
                        addAuthenticationHeaders(this)
                        connectTimeout = 15000
                        readTimeout = 15000
                    }

                    val responseCode = connection.responseCode
                    Log.d(TAG, "API Request URL: $urlString")
                    Log.d(TAG, "API Response Code: $responseCode")

                    if (responseCode == HttpURLConnection.HTTP_OK) {
                        val response =
                                BufferedReader(InputStreamReader(connection.inputStream)).use {
                                        reader ->
                                    reader.readText()
                                }

                        Log.d(TAG, "API Response: $response")
                        val searchResult = parseSearchResponse(response, page, pageSize, query)
                        Log.d(
                                TAG,
                                "Successfully parsed ${searchResult.results.size} parts, total: ${searchResult.totalResults}"
                        )
                        searchResult
                    } else {
                        val errorResponse =
                                try {
                                    BufferedReader(InputStreamReader(connection.errorStream)).use {
                                            reader ->
                                        reader.readText()
                                    }
                                } catch (e: Exception) {
                                    "No error response available"
                                }
                        Log.e(
                                TAG,
                                "API Error - Response Code: $responseCode, Error: $errorResponse"
                        )

                        // Return empty search response if API fails
                        Log.d(TAG, "API failed, returning empty search response")
                        SearchResponse(
                                results = emptyList(),
                                totalResults = 0,
                                currentPage = page,
                                totalPages = 0,
                                pageSize = pageSize
                        )
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error searching parts: ${e.message}", e)
                    Log.e(TAG, "Exception type: ${e.javaClass.simpleName}")
                    Log.e(TAG, "Stack trace: ", e)
                    // Return empty search response on error
                    SearchResponse(
                            results = emptyList(),
                            totalResults = 0,
                            currentPage = page,
                            totalPages = 0,
                            pageSize = pageSize
                    )
                }
            }

    /** Parse the JSON response from the parts API - matching web version format */
    private fun parseSearchResponse(
            jsonResponse: String,
            currentPage: Int,
            pageSize: Int,
            query: String
    ): SearchResponse {
        return try {
            Log.d(TAG, "Parsing response: ${jsonResponse.take(500)}...") // Log first 500 chars

            val jsonObject = JSONObject(jsonResponse)

            // Log all available keys in the response for debugging
            Log.d(TAG, "Available keys in API response: ${jsonObject.keys().asSequence().toList()}")

            // Log the complete pagination section of the response
            Log.d(TAG, "Complete pagination data from API:")
            Log.d(TAG, "  - totalParts: ${jsonObject.opt("totalParts")}")
            Log.d(TAG, "  - totalPages: ${jsonObject.opt("totalPages")}")
            Log.d(TAG, "  - currentPage: ${jsonObject.opt("currentPage")}")
            Log.d(TAG, "  - isSearchResult: ${jsonObject.opt("isSearchResult")}")

            // Verify backend provides pagination fields
            val hasNewPaginationFormat =
                    jsonObject.has("totalParts") && jsonObject.has("totalPages")
            if (hasNewPaginationFormat) {
                Log.d(TAG, "✅ Backend provides accurate pagination data")
            } else {
                Log.w(TAG, "⚠️ Backend missing pagination fields - using fallback")
            }

            // Check if response has error
            if (jsonObject.has("error")) {
                Log.e(TAG, "API returned error: ${jsonObject.getString("error")}")
                return SearchResponse(
                        results = emptyList(),
                        totalResults = 0,
                        currentPage = currentPage,
                        totalPages = 0,
                        pageSize = pageSize
                )
            }

            // Extract pagination metadata from API response (try multiple field names)
            val totalResults =
                    jsonObject.optInt(
                            "totalParts", // Primary field name used by backend
                            jsonObject.optInt(
                                    "total",
                                    jsonObject.optInt("totalResults", jsonObject.optInt("count", 0))
                            )
                    )
            val totalPages = jsonObject.optInt("totalPages", jsonObject.optInt("total_pages", 0))
            val currentPageFromApi =
                    jsonObject.optInt(
                            "currentPage",
                            jsonObject.optInt(
                                    "current_page",
                                    jsonObject.optInt("page", currentPage)
                            )
                    )

            // Log raw values from API response for debugging
            Log.d(
                    TAG,
                    "Raw API values - totalParts: ${jsonObject.optInt("totalParts", -1)}, " +
                            "total: ${jsonObject.optInt("total", -1)}, " +
                            "totalResults: ${jsonObject.optInt("totalResults", -1)}, " +
                            "count: ${jsonObject.optInt("count", -1)}"
            )
            Log.d(
                    TAG,
                    "Raw API values - totalPages: ${jsonObject.optInt("totalPages", -1)}, " +
                            "total_pages: ${jsonObject.optInt("total_pages", -1)}"
            )
            Log.d(
                    TAG,
                    "Raw API values - currentPage: ${jsonObject.optInt("currentPage", -1)}, " +
                            "current_page: ${jsonObject.optInt("current_page", -1)}, " +
                            "page: ${jsonObject.optInt("page", -1)}"
            )

            Log.d(
                    TAG,
                    "API Pagination Info - Total: $totalResults, Pages: $totalPages, Current: $currentPageFromApi"
            )
            Log.d(TAG, "Request Pagination - Page: $currentPage, PageSize: $pageSize")

            val partsArray = jsonObject.getJSONArray("parts")
            Log.d(TAG, "Parts array length: ${partsArray.length()}, Page size: $pageSize")
            Log.d(
                    TAG,
                    "Found ${partsArray.length()} parts in response, total: $totalResults, pages: $totalPages"
            )

            // Additional check: if we got exactly pageSize results and no total was provided,
            // we might need to make a separate call to get the actual total count
            if (totalResults == 0 && partsArray.length() == pageSize) {
                Log.w(
                        TAG,
                        "Got full page but no total count from API. Pagination may be inaccurate."
                )
            }

            val parts = mutableListOf<Part>()
            for (i in 0 until partsArray.length()) {
                val partJson = partsArray.getJSONObject(i)

                // Log each part for debugging
                Log.d(TAG, "Parsing part $i: ${partJson.toString()}")

                val part =
                        Part(
                                id = partJson.optString("id", ""),
                                name =
                                        partJson.optString(
                                                "title",
                                                partJson.optString("name", "Unnamed Part")
                                        ),
                                partNumber =
                                        partJson.optString(
                                                "partNumber",
                                                partJson.optString(
                                                        "actualPartNumber",
                                                        partJson.optString("partnumber", "N/A")
                                                )
                                        ),
                                description = partJson.optString("description", ""),
                                price = partJson.optDouble("price", 0.0),
                                stock = partJson.optInt("stock", 0),
                                category = partJson.optString("category", ""),
                                brand = partJson.optString("brand", ""),
                                imageUrl =
                                        partJson.optString(
                                                "imageUrl",
                                                partJson.optString(
                                                        "thumbnailUrl",
                                                        partJson.optString("image_url", "")
                                                )
                                        )
                        )
                parts.add(part)
                Log.d(TAG, "Added part: ${part.name} (${part.partNumber})")
            }

            Log.d(TAG, "Successfully parsed ${parts.size} parts from API response")

            // Use pagination info from API response - backend now provides accurate counts
            val finalTotalResults =
                    if (totalResults > 0) {
                        Log.d(TAG, "✅ Using API-provided total results: $totalResults")
                        totalResults
                    } else {
                        Log.w(
                                TAG,
                                "⚠️ API didn't provide totalParts, using parts count as fallback: ${parts.size}"
                        )
                        parts.size
                    }
            val finalTotalPages =
                    if (totalPages > 0) {
                        totalPages
                    } else {
                        // Intelligent fallback calculation
                        if (finalTotalResults > 0) {
                            kotlin.math.ceil(finalTotalResults.toDouble() / pageSize).toInt()
                        } else if (parts.size == pageSize && currentPage == 1) {
                            // If we got a full page of results on page 1, there might be more pages
                            // This is a heuristic - we can't know for sure without the total
                            Log.w(
                                    TAG,
                                    "API didn't provide total count. Got full page, assuming more pages exist."
                            )
                            2 // Assume at least 2 pages if we got a full first page
                        } else if (parts.size < pageSize) {
                            // If we got less than a full page, this is likely the last page
                            currentPage
                        } else {
                            1 // Default to 1 page
                        }
                    }

            Log.d(
                    TAG,
                    "Final Pagination - Total: $finalTotalResults, Pages: $finalTotalPages, Current: $currentPageFromApi"
            )

            // Pagination summary
            Log.d(
                    TAG,
                    "Search: '$query' → ${parts.size} parts on page $currentPageFromApi of $finalTotalPages (${finalTotalResults} total)"
            )

            SearchResponse(
                    results = parts,
                    totalResults = finalTotalResults,
                    currentPage = currentPageFromApi,
                    totalPages = finalTotalPages,
                    pageSize = pageSize
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing parts response: ${e.message}", e)
            Log.e(TAG, "Response was: $jsonResponse")
            SearchResponse(
                    results = emptyList(),
                    totalResults = 0,
                    currentPage = currentPage,
                    totalPages = 0,
                    pageSize = pageSize
            )
        }
    }

    /** Build query parameters for the API request - matching web version format */
    private fun buildQueryParameters(
            query: String,
            filters: com.autoflow.android.ui.search.SearchFilters,
            sortBy: com.autoflow.android.ui.search.SortOption,
            page: Int = 1,
            limit: Int = 20
    ): String {
        val params = mutableListOf<String>()

        // Add search query (same parameter name as web version)
        if (query.isNotBlank()) {
            params.add("query=${URLEncoder.encode(query, "UTF-8")}")
        }

        // Add pagination (same as web version)
        params.add("page=$page")
        params.add("limit=$limit")

        // Add category filters
        if (filters.categories.isNotEmpty()) {
            params.add(
                    "categories=${URLEncoder.encode(filters.categories.joinToString(","), "UTF-8")}"
            )
        }

        // Add brand filters
        if (filters.brands.isNotEmpty()) {
            params.add("brands=${URLEncoder.encode(filters.brands.joinToString(","), "UTF-8")}")
        }

        // Add price range
        filters.priceMin?.let { params.add("price_min=$it") }
        filters.priceMax?.let { params.add("price_max=$it") }

        // Add stock filter
        if (filters.inStockOnly) {
            params.add("in_stock_only=true")
        }

        // Add sorting (same format as web version)
        if (sortBy != com.autoflow.android.ui.search.SortOption.RELEVANCE) {
            params.add("sort=${getSortParameter(sortBy)}")
        }

        return params.joinToString("&")
    }

    /** Convert sort option to API parameter */
    private fun getSortParameter(sortBy: com.autoflow.android.ui.search.SortOption): String {
        return when (sortBy) {
            com.autoflow.android.ui.search.SortOption.RELEVANCE -> ""
            com.autoflow.android.ui.search.SortOption.PRICE_LOW_TO_HIGH -> "price-low"
            com.autoflow.android.ui.search.SortOption.PRICE_HIGH_TO_LOW -> "price-high"
            com.autoflow.android.ui.search.SortOption.NAME_A_TO_Z -> "name-asc"
            com.autoflow.android.ui.search.SortOption.NAME_Z_TO_A -> "name-desc"
            com.autoflow.android.ui.search.SortOption.NEWEST_FIRST -> "newest"
        }
    }

    /** Add authentication headers to the request */
    private fun addAuthenticationHeaders(connection: HttpURLConnection) {
        // TODO: Get actual auth token from AuthRepository
        // For now, we'll use a placeholder or skip authentication
        // connection.setRequestProperty("Authorization", "Bearer $authToken")

        // Add user agent and other headers
        connection.setRequestProperty("User-Agent", "Autoflow-Android/1.0")
        connection.setRequestProperty("X-Platform", "Android")
    }

    /** Fetch categories from database */
    suspend fun fetchCategories(): Result<List<Category>> =
            withContext(Dispatchers.IO) {
                try {
                    Log.d(TAG, "Fetching categories from API")

                    val url = URL("$BASE_URL/api/categories")
                    val connection = url.openConnection() as HttpURLConnection

                    connection.apply {
                        requestMethod = "GET"
                        setRequestProperty("Content-Type", "application/json")
                        setRequestProperty("Accept", "application/json")
                        setRequestProperty("User-Agent", "AutoflowAndroid/1.0")
                        connectTimeout = 10000
                        readTimeout = 10000
                    }

                    addAuthenticationHeaders(connection)

                    val responseCode = connection.responseCode
                    Log.d(TAG, "Categories API response code: $responseCode")

                    if (responseCode == HttpURLConnection.HTTP_OK) {
                        val response = connection.inputStream.bufferedReader().use { it.readText() }
                        Log.d(TAG, "Categories API response: $response")

                        val categories = parseCategoriesResponse(response)
                        Result.success(categories)
                    } else {
                        val errorResponse =
                                connection.errorStream?.bufferedReader()?.use { it.readText() }
                                        ?: "Unknown error"
                        Log.e(TAG, "Categories API error: $errorResponse")
                        Result.failure(Exception("Failed to fetch categories: $responseCode"))
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error fetching categories", e)
                    Result.failure(e)
                }
            }

    /** Fetch hierarchical categories from API - exactly like web version */
    suspend fun fetchHierarchicalCategories(): Result<List<HierarchicalCategory>> =
            withContext(Dispatchers.IO) {
                try {
                    Log.d(TAG, "Fetching hierarchical categories from API")

                    val url = URL("$BASE_URL/api/categories")
                    val connection = url.openConnection() as HttpURLConnection

                    connection.apply {
                        requestMethod = "GET"
                        setRequestProperty("Content-Type", "application/json")
                        setRequestProperty("Accept", "application/json")
                        setRequestProperty("User-Agent", "AutoflowAndroid/1.0")
                        connectTimeout = 10000
                        readTimeout = 10000
                    }

                    addAuthenticationHeaders(connection)

                    val responseCode = connection.responseCode
                    Log.d(TAG, "Hierarchical categories API response code: $responseCode")

                    if (responseCode == HttpURLConnection.HTTP_OK) {
                        val response = connection.inputStream.bufferedReader().use { it.readText() }
                        Log.d(TAG, "Hierarchical categories API response: $response")

                        val hierarchicalCategories = parseHierarchicalCategoriesResponse(response)
                        Result.success(hierarchicalCategories)
                    } else {
                        val errorResponse =
                                connection.errorStream?.bufferedReader()?.use { it.readText() }
                                        ?: "Unknown error"
                        Log.e(TAG, "Hierarchical categories API error: $errorResponse")
                        Result.failure(
                                Exception("Failed to fetch hierarchical categories: $responseCode")
                        )
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error fetching hierarchical categories", e)
                    Result.failure(e)
                }
            }

    /** Fetch category attributes from database */
    suspend fun fetchCategoryAttributes(categoryId: Int): Result<List<CategoryAttribute>> =
            withContext(Dispatchers.IO) {
                try {
                    Log.d(TAG, "Fetching attributes for category: $categoryId")

                    val url = URL("$BASE_URL/api/categories/$categoryId/attributes")
                    val connection = url.openConnection() as HttpURLConnection

                    connection.apply {
                        requestMethod = "GET"
                        setRequestProperty("Content-Type", "application/json")
                        connectTimeout = 10000
                        readTimeout = 10000
                    }

                    addAuthenticationHeaders(connection)

                    val responseCode = connection.responseCode
                    Log.d(TAG, "Category attributes API response code: $responseCode")

                    if (responseCode == HttpURLConnection.HTTP_OK) {
                        val response = connection.inputStream.bufferedReader().use { it.readText() }
                        Log.d(TAG, "Category attributes API response: $response")

                        val attributes = parseCategoryAttributesResponse(response)
                        Result.success(attributes)
                    } else {
                        val errorResponse =
                                connection.errorStream?.bufferedReader()?.use { it.readText() }
                                        ?: "Unknown error"
                        Log.e(TAG, "Category attributes API error: $errorResponse")
                        Result.failure(
                                Exception("Failed to fetch category attributes: $responseCode")
                        )
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error fetching category attributes", e)
                    Result.failure(e)
                }
            }

    /** Parse categories API response */
    private fun parseCategoriesResponse(response: String): List<Category> {
        return try {
            val jsonObject = JSONObject(response)
            val categoriesArray = jsonObject.getJSONArray("categories")

            val categories = mutableListOf<Category>()
            for (i in 0 until categoriesArray.length()) {
                val categoryJson = categoriesArray.getJSONObject(i)
                categories.add(
                        Category(
                                id = categoryJson.getInt("id"),
                                label = categoryJson.getString("label"),
                                parentId =
                                        if (categoryJson.isNull("parent_category_id")) null
                                        else categoryJson.getInt("parent_category_id"),
                                requirePartNumber =
                                        categoryJson.optBoolean("requirePartNumber", true),
                                isActive = categoryJson.optBoolean("isActive", true),
                                level = categoryJson.optInt("level", 0)
                        )
                )
            }
            categories
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing categories response", e)
            emptyList()
        }
    }

    /** Parse hierarchical categories API response - exactly like web version */
    private fun parseHierarchicalCategoriesResponse(response: String): List<HierarchicalCategory> {
        return try {
            val jsonObject = JSONObject(response)
            val hierarchicalArray = jsonObject.getJSONArray("hierarchical")

            val categories = mutableListOf<HierarchicalCategory>()
            for (i in 0 until hierarchicalArray.length()) {
                val categoryJson = hierarchicalArray.getJSONObject(i)
                categories.add(parseHierarchicalCategory(categoryJson, 0))
            }
            categories
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing hierarchical categories response", e)
            emptyList()
        }
    }

    private fun parseHierarchicalCategory(
            categoryJson: JSONObject,
            level: Int
    ): HierarchicalCategory {
        val children = mutableListOf<HierarchicalCategory>()

        if (categoryJson.has("children")) {
            val childrenArray = categoryJson.getJSONArray("children")
            for (i in 0 until childrenArray.length()) {
                val childJson = childrenArray.getJSONObject(i)
                children.add(parseHierarchicalCategory(childJson, level + 1))
            }
        }

        return HierarchicalCategory(
                id = categoryJson.getInt("id"),
                label = categoryJson.getString("label"),
                parentId =
                        if (categoryJson.isNull("parent_category_id")) null
                        else categoryJson.getInt("parent_category_id"),
                requirePartNumber = categoryJson.optBoolean("requirePartNumber", true),
                isActive = categoryJson.optBoolean("isActive", true),
                level = level,
                children = children
        )
    }

    /** Parse category attributes API response */
    private fun parseCategoryAttributesResponse(response: String): List<CategoryAttribute> {
        return try {
            val jsonObject = JSONObject(response)
            val attributesArray = jsonObject.getJSONArray("attributes")

            val attributes = mutableListOf<CategoryAttribute>()
            for (i in 0 until attributesArray.length()) {
                val attributeJson = attributesArray.getJSONObject(i)

                // Parse options if they exist
                val options = mutableListOf<AttributeOption>()
                if (attributeJson.has("options")) {
                    val optionsArray = attributeJson.getJSONArray("options")
                    for (j in 0 until optionsArray.length()) {
                        val optionJson = optionsArray.getJSONObject(j)
                        options.add(
                                AttributeOption(
                                        id = optionJson.getInt("id"),
                                        optionValue = optionJson.getString("option_value"),
                                        attributeId = optionJson.getInt("attribute_id")
                                )
                        )
                    }
                }

                attributes.add(
                        CategoryAttribute(
                                id = attributeJson.getInt("id"),
                                attribute = attributeJson.getString("attribute"),
                                inputType = attributeJson.getString("input_type"),
                                isRequired = attributeJson.optBoolean("is_required", false),
                                dependsOnAttributeId =
                                        if (attributeJson.isNull("depends_on_attribute_id")) null
                                        else attributeJson.getInt("depends_on_attribute_id"),
                                dependsOnOptionId =
                                        if (attributeJson.isNull("depends_on_option_id")) null
                                        else attributeJson.getInt("depends_on_option_id"),
                                options = options
                        )
                )
            }
            attributes
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing category attributes response", e)
            emptyList()
        }
    }
}

/** Data class representing a part */
data class Part(
        val id: String,
        val name: String,
        val partNumber: String,
        val description: String,
        val price: Double,
        val stock: Int,
        val category: String = "",
        val brand: String = "",
        val imageUrl: String = ""
)

// New data classes for category management
data class Category(
        val id: Int,
        val label: String,
        val parentId: Int? = null,
        val requirePartNumber: Boolean = true,
        val isActive: Boolean = true,
        val level: Int = 0
)

data class CategoryAttribute(
        val id: Int,
        val attribute: String,
        val inputType: String,
        val isRequired: Boolean = false,
        val dependsOnAttributeId: Int? = null,
        val dependsOnOptionId: Int? = null,
        val options: List<AttributeOption> = emptyList()
)

data class AttributeOption(val id: Int, val optionValue: String, val attributeId: Int)

data class HierarchicalCategory(
        val id: Int,
        val label: String,
        val parentId: Int? = null,
        val requirePartNumber: Boolean = true,
        val isActive: Boolean = true,
        val level: Int = 0,
        val children: List<HierarchicalCategory> = emptyList()
) {
    val isLeaf: Boolean
        get() = children.isEmpty()
    val hasChildren: Boolean
        get() = children.isNotEmpty()
}

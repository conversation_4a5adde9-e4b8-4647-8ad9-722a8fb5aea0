package com.autoflow.android.ui.parts

import android.os.Bundle
import android.view.View
import android.widget.*
import com.autoflow.android.R
import com.autoflow.android.data.api.PartsApiService
import com.autoflow.android.ui.base.BaseBackendActivity

class PartUpdateActivity : BaseBackendActivity() {

    companion object {
        const val EXTRA_PART_ID = "extra_part_id"
        const val EXTRA_PART_NAME = "extra_part_name"
        const val EXTRA_PART_NUMBER = "extra_part_number"
        const val EXTRA_PART_DESCRIPTION = "extra_part_description"
        const val EXTRA_PART_PRICE = "extra_part_price"
        const val EXTRA_PART_STOCK = "extra_part_stock"
        const val EXTRA_PART_BRAND = "extra_part_brand"
        const val EXTRA_PART_CATEGORY = "extra_part_category"
    }

    // UI Elements
    private lateinit var partNameInput: EditText
    private lateinit var partNumberInput: EditText
    private lateinit var partDescriptionInput: EditText
    private lateinit var partPriceInput: EditText
    private lateinit var partStockInput: EditText
    private lateinit var partBrandSpinner: Spinner
    private lateinit var partCategorySpinner: Spinner
    private lateinit var saveButton: Button
    private lateinit var cancelButton: Button
    private lateinit var loadingLayout: View

    // Data
    private var currentPartId: String = ""
    private lateinit var partsApiService: PartsApiService

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Use a simple layout for now
        setBackendContentView(R.layout.fragment_basic_info)

        partsApiService = PartsApiService(this)

        initializeViews()
        setupSpinners()
        loadPartData()
        setupClickListeners()
    }

    private fun initializeViews() {
        partNameInput = findViewById(R.id.partNameInput)
        partNumberInput = findViewById(R.id.partNumberInput)
        partDescriptionInput = findViewById(R.id.partDescriptionInput)
        partBrandSpinner = findViewById(R.id.partBrandSpinner)
        partCategorySpinner = findViewById(R.id.partCategorySpinner)

        // Add save and cancel buttons to the layout programmatically
        // For now, we'll use Toast messages
    }

    private fun setupSpinners() {
        // Brand spinner
        val brands = arrayOf("VW", "AUDI", "BMW", "Mercedes", "Porsche", "Other")
        val brandAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, brands)
        brandAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        partBrandSpinner.adapter = brandAdapter

        // Category spinner
        val categories =
                arrayOf(
                        "Brakes",
                        "Engine",
                        "Transmission",
                        "Suspension",
                        "Electrical",
                        "Body",
                        "Interior",
                        "Exhaust",
                        "Cooling",
                        "Fuel System",
                        "Other"
                )
        val categoryAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, categories)
        categoryAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        partCategorySpinner.adapter = categoryAdapter
    }

    private fun loadPartData() {
        currentPartId = intent.getStringExtra(EXTRA_PART_ID) ?: ""

        partNameInput.setText(intent.getStringExtra(EXTRA_PART_NAME) ?: "")
        partNumberInput.setText(intent.getStringExtra(EXTRA_PART_NUMBER) ?: "")
        partDescriptionInput.setText(intent.getStringExtra(EXTRA_PART_DESCRIPTION) ?: "")

        // Set spinner selections
        val brand = intent.getStringExtra(EXTRA_PART_BRAND) ?: ""
        val category = intent.getStringExtra(EXTRA_PART_CATEGORY) ?: ""

        setSpinnerSelection(partBrandSpinner, brand)
        setSpinnerSelection(partCategorySpinner, category)
    }

    private fun setSpinnerSelection(spinner: Spinner, value: String) {
        val adapter = spinner.adapter as ArrayAdapter<String>
        val position = adapter.getPosition(value)
        if (position >= 0) {
            spinner.setSelection(position)
        }
    }

    private fun setupClickListeners() {
        // Show a message that the form is loaded
        Toast.makeText(this, "Part update form loaded successfully!", Toast.LENGTH_SHORT).show()
    }

    override fun onHamburgerMenuClick() {
        Toast.makeText(this, "Menu clicked", Toast.LENGTH_SHORT).show()
    }

    override fun onCategoriesMenuClick() {
        Toast.makeText(this, "Categories clicked", Toast.LENGTH_SHORT).show()
    }
}

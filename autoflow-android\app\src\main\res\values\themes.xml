<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base application theme. -->
    <style name="Theme.AutoflowAndroid" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Material Design 3 Color Scheme -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorPrimaryContainer">@color/primary_container</item>
        <item name="colorOnPrimaryContainer">@color/on_primary_container</item>
        
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="colorSecondaryContainer">@color/secondary_container</item>
        <item name="colorOnSecondaryContainer">@color/on_secondary_container</item>
        
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnSurface">@color/on_surface</item>
        <item name="colorSurfaceVariant">@color/surface_variant</item>
        <item name="colorOnSurfaceVariant">@color/on_surface_variant</item>
        
        <item name="colorOutline">@color/outline</item>
        <item name="colorOutlineVariant">@color/outline_variant</item>
        
        <!-- Status bar and navigation -->
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightNavigationBar">true</item>
        
        <!-- Material Design 3 specific attributes -->
        <item name="colorSurfaceBright">@color/surface</item>
        <item name="colorSurfaceDim">@color/surface_variant</item>
        <item name="colorSurfaceContainer">@color/surface_variant</item>
        <item name="colorSurfaceContainerHigh">@color/surface</item>
        <item name="colorSurfaceContainerLow">@color/surface_variant</item>
        <item name="colorSurfaceContainerHighest">@color/surface</item>
    </style>

    <!-- Login Button Style -->
    <style name="LoginButton" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/brand_teal</item>
        <item name="android:textColor">@color/text_on_brand</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">0.1</item>
        <item name="cornerRadius">28dp</item>
        <item name="elevation">8dp</item>
        <item name="rippleColor">@color/brand_teal_light</item>
    </style>

    <!-- OTP Digit Style -->
    <style name="OTPDigitStyle">
        <item name="android:background">@drawable/otp_digit_background</item>
        <item name="android:gravity">center</item>
        <item name="android:inputType">number</item>
        <item name="android:maxLength">1</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:importantForAutofill">no</item>
        <item name="android:maxLines">1</item>
        <item name="android:singleLine">true</item>
        <item name="android:selectAllOnFocus">true</item>
    </style>

    <!-- Modern Category Dropdown Styles -->
    <style name="CategoryDropdownTrigger" parent="Widget.Material3.CardView">
        <item name="cardBackgroundColor">@color/surface_variant</item>
        <item name="cardElevation">@dimen/elevation_medium</item>
        <item name="cardCornerRadius">@dimen/corner_radius_large</item>
        <item name="rippleColor">@color/primary</item>
    </style>

    <style name="CategoryDropdownItem" parent="Widget.Material3.CardView">
        <item name="cardBackgroundColor">@color/surface</item>
        <item name="cardElevation">@dimen/elevation_low</item>
        <item name="cardCornerRadius">@dimen/corner_radius_medium</item>
        <item name="rippleColor">@color/primary</item>
    </style>

    <style name="CategoryDropdownParent" parent="Widget.Material3.CardView">
        <item name="cardBackgroundColor">@color/surface_variant</item>
        <item name="cardElevation">@dimen/elevation_low</item>
        <item name="cardCornerRadius">@dimen/corner_radius_medium</item>
        <item name="rippleColor">@color/primary</item>
    </style>
</resources>

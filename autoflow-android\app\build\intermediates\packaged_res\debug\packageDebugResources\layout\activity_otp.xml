<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="@drawable/login_background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <!-- Back Button -->
        <ImageButton
            android:id="@+id/backButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginTop="16dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_arrow_back"
            android:contentDescription="Back"
            android:tint="@color/text_primary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Title -->
        <TextView
            android:id="@+id/titleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Verify Account"
            android:textColor="@color/text_primary"
            android:textSize="24sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backButton" />

        <!-- Illustration -->
        <ImageView
            android:id="@+id/illustrationImage"
            android:layout_width="160dp"
            android:layout_height="120dp"
            android:layout_marginTop="24dp"
            android:src="@drawable/otp_illustration_simple"
            android:scaleType="centerInside"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/titleText" />

        <!-- Main Title -->
        <TextView
            android:id="@+id/mainTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="Enter your Verification Code"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/illustrationImage" />

        <!-- Subtitle -->
        <TextView
            android:id="@+id/subtitleText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="We sent a verification code\nto your email address"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            android:gravity="center"
            android:lineSpacingExtra="2dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/mainTitle" />

        <!-- OTP Input Container -->
        <LinearLayout
            android:id="@+id/otpContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="32dp"
            android:layout_marginEnd="24dp"
            android:orientation="horizontal"
            android:gravity="center"
            android:weightSum="6"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/subtitleText">

            <EditText
                android:id="@+id/otpDigit1"
                style="@style/OTPDigitStyle"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:layout_marginEnd="4dp" />

            <EditText
                android:id="@+id/otpDigit2"
                style="@style/OTPDigitStyle"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp" />

            <EditText
                android:id="@+id/otpDigit3"
                style="@style/OTPDigitStyle"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp" />

            <EditText
                android:id="@+id/otpDigit4"
                style="@style/OTPDigitStyle"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp" />

            <EditText
                android:id="@+id/otpDigit5"
                style="@style/OTPDigitStyle"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp" />

            <EditText
                android:id="@+id/otpDigit6"
                style="@style/OTPDigitStyle"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- Verify Button -->
        <Button
            android:id="@+id/verifyButton"
            style="@style/LoginButton"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_marginStart="24dp"
            android:layout_marginTop="32dp"
            android:layout_marginEnd="24dp"
            android:text="Verify OTP"
            android:enabled="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/otpContainer" />

        <!-- Resend Text -->
        <TextView
            android:id="@+id/resendText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="Didn't receive code? "
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            app:layout_constraintEnd_toStartOf="@id/resendButton"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/verifyButton" />

        <TextView
            android:id="@+id/resendButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Resend again"
            android:textColor="@color/brand_teal"
            android:textSize="14sp"
            android:textStyle="bold"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/resendText"
            app:layout_constraintTop_toTopOf="@id/resendText" />

        <!-- Loading Progress -->
        <ProgressBar
            android:id="@+id/loadingProgress"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:visibility="gone"
            android:indeterminateTint="@android:color/white"
            app:layout_constraintBottom_toBottomOf="@id/verifyButton"
            app:layout_constraintEnd_toEndOf="@id/verifyButton"
            app:layout_constraintStart_toStartOf="@id/verifyButton"
            app:layout_constraintTop_toTopOf="@id/verifyButton" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>

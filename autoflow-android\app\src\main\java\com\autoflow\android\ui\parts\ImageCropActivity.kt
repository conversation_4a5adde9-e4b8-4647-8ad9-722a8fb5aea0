package com.autoflow.android.ui.parts

import android.app.Activity
import android.content.Intent
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.ImageView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.autoflow.android.R
import com.yalantis.ucrop.UCrop
import java.io.File

class ImageCropActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "ImageCropActivity"
    }

    private lateinit var imageView: ImageView
    private lateinit var cropButton: Button
    private lateinit var cancelButton: Button
    private var imageUri: Uri? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_image_crop)

        initializeViews()
        setupClickListeners()
        loadImageAndStartCrop()
    }

    private fun initializeViews() {
        imageView = findViewById(R.id.imageView)
        cropButton = findViewById(R.id.cropButton)
        cancelButton = findViewById(R.id.cancelButton)
    }

    private fun setupClickListeners() {
        cropButton.setOnClickListener {
            imageUri?.let { startUCrop(it) }
                    ?: run { Toast.makeText(this, "No image to crop", Toast.LENGTH_SHORT).show() }
        }
        cancelButton.setOnClickListener { finish() }
    }

    private fun loadImageAndStartCrop() {
        val imageUriString = intent.getStringExtra("image_uri")
        if (imageUriString.isNullOrBlank()) {
            Toast.makeText(this, "Error loading image", Toast.LENGTH_SHORT).show()
            finish()
            return
        }
        imageUri = Uri.parse(imageUriString)

        try {
            // Display a quick preview (works for file://, for content:// fall back to UCrop
            // preview)
            val uri = imageUri!!
            when (uri.scheme) {
                "file" -> {
                    val path = uri.path
                    if (!path.isNullOrBlank()) {
                        val bmp = BitmapFactory.decodeFile(path)
                        if (bmp != null) imageView.setImageBitmap(bmp)
                    }
                }
                else -> {
                    contentResolver.openInputStream(uri)?.use { input ->
                        val bmp = BitmapFactory.decodeStream(input)
                        if (bmp != null) imageView.setImageBitmap(bmp)
                    }
                }
            }
        } catch (e: Exception) {
            Log.w(TAG, "Preview load failed, proceeding to crop UI", e)
        }

        // Immediately open crop UI with 1:1 aspect ratio
        startUCrop(imageUri!!)
    }

    private fun startUCrop(sourceUri: Uri) {
        try {
            val destFile = File(cacheDir, "cropped_${System.currentTimeMillis()}.jpg")
            val destUri = Uri.fromFile(destFile)

            val options =
                    UCrop.Options().apply {
                        setCompressionFormat(android.graphics.Bitmap.CompressFormat.JPEG)
                        setCompressionQuality(90)
                        setHideBottomControls(false)
                        setFreeStyleCropEnabled(true)
                        setToolbarColor(resources.getColor(android.R.color.black, null))
                        setStatusBarColor(resources.getColor(android.R.color.black, null))
                        setActiveControlsWidgetColor(
                                resources.getColor(android.R.color.white, null)
                        )
                        setToolbarWidgetColor(resources.getColor(android.R.color.white, null))
                    }

            UCrop.of(sourceUri, destUri).withAspectRatio(1f, 1f).withOptions(options).start(this)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start crop", e)
            Toast.makeText(this, "Failed to start crop", Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == UCrop.REQUEST_CROP) {
            if (resultCode == Activity.RESULT_OK) {
                val resultUri = data?.let { UCrop.getOutput(it) }
                if (resultUri != null) {
                    navigateToAddPart(resultUri)
                } else {
                    Toast.makeText(this, "Crop failed", Toast.LENGTH_SHORT).show()
                    finish()
                }
            } else if (resultCode == UCrop.RESULT_ERROR) {
                val cropError = UCrop.getError(data!!)
                Log.e(TAG, "Crop error", cropError)
                Toast.makeText(this, "Crop error", Toast.LENGTH_SHORT).show()
                finish()
            } else {
                // User cancelled
                finish()
            }
        }
    }

    private fun navigateToAddPart(croppedUri: Uri) {
        val intent =
                Intent(this, com.autoflow.android.ui.addpart.AddPartActivity::class.java).apply {
                    putExtra(
                            com.autoflow.android.ui.addpart.AddPartActivity.EXTRA_CROPPED_IMAGE_URI,
                            croppedUri.toString()
                    )
                }
        startActivity(intent)
        finish()
    }
}

package com.autoflow.android.ui.parts

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.autoflow.android.R

class AlternativePartsFragment : Fragment() {

    // UI Elements
    private lateinit var generatePartNumbersButton: Button
    private lateinit var partNumberInput: EditText
    private lateinit var addPartNumberButton: Button
    private lateinit var alternativePartsRecyclerView: RecyclerView
    private lateinit var emptyPartsLayout: View
    private lateinit var partsLoadingLayout: View

    // Data
    private val alternativePartNumbers = mutableListOf<String>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_alternative_parts, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        initializeViews(view)
        setupRecyclerView()
        setupClickListeners()
        updateEmptyState()
    }

    private fun initializeViews(view: View) {
        generatePartNumbersButton = view.findViewById(R.id.generatePartNumbersButton)
        partNumberInput = view.findViewById(R.id.partNumberInput)
        addPartNumberButton = view.findViewById(R.id.addPartNumberButton)
        alternativePartsRecyclerView = view.findViewById(R.id.alternativePartsRecyclerView)
        emptyPartsLayout = view.findViewById(R.id.emptyPartsLayout)
        partsLoadingLayout = view.findViewById(R.id.partsLoadingLayout)
    }

    private fun setupRecyclerView() {
        alternativePartsRecyclerView.layoutManager = LinearLayoutManager(requireContext())
        // TODO: Setup adapter for alternative part numbers list
    }

    private fun setupClickListeners() {
        generatePartNumbersButton.setOnClickListener {
            generateAlternativePartNumbers()
        }

        addPartNumberButton.setOnClickListener {
            addPartNumberManually()
        }
    }

    private fun generateAlternativePartNumbers() {
        showLoading(true)
        
        // TODO: Implement AI generation of alternative part numbers
        // For now, simulate with delay
        view?.postDelayed({
            // Add some sample part numbers
            alternativePartNumbers.addAll(listOf(
                "1K0615301AA",
                "1K0615301AB", 
                "1K0615301AC",
                "JZW615301B"
            ))
            updateEmptyState()
            showLoading(false)
            Toast.makeText(requireContext(), "Alternative part numbers generated", Toast.LENGTH_SHORT).show()
        }, 2000)
    }

    private fun addPartNumberManually() {
        val partNumberText = partNumberInput.text.toString().trim()
        if (partNumberText.isNotEmpty()) {
            if (!alternativePartNumbers.contains(partNumberText)) {
                alternativePartNumbers.add(partNumberText)
                partNumberInput.setText("")
                updateEmptyState()
                Toast.makeText(requireContext(), "Part number added", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(requireContext(), "Part number already exists", Toast.LENGTH_SHORT).show()
            }
        } else {
            partNumberInput.error = "Please enter a part number"
        }
    }

    private fun updateEmptyState() {
        if (alternativePartNumbers.isEmpty()) {
            emptyPartsLayout.visibility = View.VISIBLE
            alternativePartsRecyclerView.visibility = View.GONE
        } else {
            emptyPartsLayout.visibility = View.GONE
            alternativePartsRecyclerView.visibility = View.VISIBLE
            // TODO: Update RecyclerView adapter
        }
    }

    private fun showLoading(show: Boolean) {
        partsLoadingLayout.visibility = if (show) View.VISIBLE else View.GONE
        generatePartNumbersButton.isEnabled = !show
        addPartNumberButton.isEnabled = !show
    }

    fun getAlternativePartNumbers(): List<String> {
        return alternativePartNumbers.toList()
    }

    fun loadAlternativePartNumbers(partNumbers: List<String>) {
        alternativePartNumbers.clear()
        alternativePartNumbers.addAll(partNumbers)
        updateEmptyState()
    }
}

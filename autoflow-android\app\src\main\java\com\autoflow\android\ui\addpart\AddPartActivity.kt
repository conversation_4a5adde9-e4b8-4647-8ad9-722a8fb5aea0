package com.autoflow.android.ui.addpart

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.*
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.autoflow.android.core.auth.AuthManager
import com.autoflow.android.data.api.RealAddPartApiService
import com.autoflow.android.data.repositories.AddPartRepository
import com.autoflow.android.domain.models.AddPartStep
import com.autoflow.android.ui.addpart.fragments.*
import com.autoflow.android.ui.storage.StorageLocationActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Main Add Part Activity - hosts the complete wizard flow Mirrors web app's multi-step form with
 * image-first approach
 */
class AddPartActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "AddPartActivity"
        const val EXTRA_CROPPED_IMAGE_URI = "cropped_image_uri"
        const val EXTRA_CATEGORY_ID = "category_id"
        const val EXTRA_CATEGORY_NAME = "category_name"
        const val EXTRA_REQUIRES_PART_NUMBER = "requires_part_number"
        const val EXTRA_HAS_ATTRIBUTES = "has_attributes"
    }

    private lateinit var viewModel: AddPartViewModel
    private lateinit var authManager: AuthManager

    // UI Components
    private lateinit var progressBar: ProgressBar
    private lateinit var stepIndicator: TextView
    private lateinit var fragmentContainer: FrameLayout
    private lateinit var backButton: Button
    private lateinit var nextButton: Button
    private lateinit var errorBanner: LinearLayout
    private lateinit var errorText: TextView
    private lateinit var dismissErrorButton: Button

    // Status ticker
    private lateinit var statusTicker: LinearLayout
    private lateinit var statusProgress: ProgressBar
    private lateinit var statusText: TextView

    // Storage location launcher
    private val storageLocationLauncher =
            registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
                if (result.resultCode == RESULT_OK) {
                    // Storage location saved successfully
                    Toast.makeText(this, "Part created and stored successfully!", Toast.LENGTH_LONG)
                            .show()
                    finish()
                }
            }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(createLayout())

        initializeComponents()
        setupViewModel()

        // Show snackbar banner when token is refreshed
        lifecycleScope.launch {
            authManager.tokenRefreshedAt.collect { ts ->
                if (ts != null) {
                    statusTicker.visibility = View.VISIBLE
                    statusText.text = "Session refreshed, retrying…"
                    statusTicker.alpha = 0f
                    statusTicker
                            .animate()
                            .alpha(1f)
                            .setDuration(150)
                            .withEndAction {
                                statusTicker.postDelayed(
                                        {
                                            statusTicker
                                                    .animate()
                                                    .alpha(0f)
                                                    .setDuration(150)
                                                    .withEndAction {
                                                        statusTicker.visibility = View.GONE
                                                        statusTicker.alpha = 1f
                                                    }
                                                    .start()
                                        },
                                        1200
                                )
                            }
                            .start()
                }
            }
        }

        lifecycleScope.launch(Dispatchers.IO) {
            // Proactive refresh loop: refresh ~5 min before expiry
            while (true) {
                val now = System.currentTimeMillis()
                val expiry = authManager.getExpiryTimeMillis() ?: (now + 30 * 60 * 1000)
                val refreshAt = expiry - 5 * 60 * 1000 // 5 minutes before
                val delayMs = (refreshAt - now).coerceAtLeast(5_000L)
                kotlinx.coroutines.delay(delayMs)
                // Only refresh if still logged in and token close to expiry
                if (authManager.hasValidToken()) {
                    val success = authManager.refreshToken()
                    if (!success) {
                        // Optionally flag re-auth required here
                    }
                }
            }
        }

        observeViewModel()
        handleIntentExtras()
    }

    private fun createLayout(): LinearLayout {
        return LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(16, 16, 16, 16)

            // Header with progress
            val headerLayout =
                    LinearLayout(this@AddPartActivity).apply {
                        orientation = LinearLayout.VERTICAL
                        setPadding(0, 0, 0, 16)
                    }

            // Step indicator
            stepIndicator =
                    TextView(this@AddPartActivity).apply {
                        text = "Step 1 of 6: Add Images"
                        textSize = 16f
                        setPadding(0, 0, 0, 8)
                    }

            // Progress bar
            progressBar =
                    ProgressBar(
                                    this@AddPartActivity,
                                    null,
                                    android.R.attr.progressBarStyleHorizontal
                            )
                            .apply {
                                max = 6
                                progress = 1
                                layoutParams =
                                        LinearLayout.LayoutParams(
                                                LinearLayout.LayoutParams.MATCH_PARENT,
                                                24
                                        )
                            }

            headerLayout.addView(stepIndicator)
            headerLayout.addView(progressBar)

            // Status ticker banner
            statusTicker =
                    LinearLayout(this@AddPartActivity).apply {
                        orientation = LinearLayout.HORIZONTAL
                        setBackgroundColor(
                                resources.getColor(android.R.color.holo_blue_light, null)
                        )
                        setPadding(16, 12, 16, 12)
                        visibility = View.GONE
                    }
            statusProgress =
                    ProgressBar(this@AddPartActivity, null, android.R.attr.progressBarStyleSmall)
                            .apply {
                                isIndeterminate = true
                                visibility = View.VISIBLE
                            }
            statusText =
                    TextView(this@AddPartActivity).apply {
                        text = "Session refreshed, retrying…"
                        setPadding(12, 0, 0, 0)
                    }
            statusTicker.addView(statusProgress)
            statusTicker.addView(statusText)

            headerLayout.addView(statusTicker)

            // Error banner (initially hidden)
            errorBanner =
                    LinearLayout(this@AddPartActivity).apply {
                        orientation = LinearLayout.HORIZONTAL
                        setBackgroundColor(resources.getColor(android.R.color.holo_red_light, null))
                        setPadding(16, 16, 16, 16)
                        visibility = View.GONE
                    }

            errorText =
                    TextView(this@AddPartActivity).apply {
                        textSize = 14f
                        setTextColor(resources.getColor(android.R.color.white, null))
                        layoutParams =
                                LinearLayout.LayoutParams(
                                        0,
                                        LinearLayout.LayoutParams.WRAP_CONTENT,
                                        1f
                                )
                    }

            dismissErrorButton =
                    Button(this@AddPartActivity).apply {
                        text = "×"
                        setBackgroundColor(android.R.color.transparent)
                        setTextColor(resources.getColor(android.R.color.white, null))
                        setOnClickListener { hideError() }
                    }

            errorBanner.addView(errorText)
            errorBanner.addView(dismissErrorButton)

            // Status ticker (compact, at top of content)
            statusTicker =
                    LinearLayout(this@AddPartActivity).apply {
                        orientation = LinearLayout.HORIZONTAL
                        visibility = View.GONE
                        setPadding(0, 8, 0, 8)
                    }
            statusProgress = ProgressBar(this@AddPartActivity).apply { isIndeterminate = true }
            statusText =
                    TextView(this@AddPartActivity).apply {
                        textSize = 14f
                        setPadding(12, 0, 0, 0)
                    }
            statusTicker.addView(statusProgress)
            statusTicker.addView(statusText)

            // Fragment container
            fragmentContainer =
                    FrameLayout(this@AddPartActivity).apply {
                        id = View.generateViewId()
                        layoutParams =
                                LinearLayout.LayoutParams(
                                        LinearLayout.LayoutParams.MATCH_PARENT,
                                        0,
                                        1f
                                )
                    }

            // Navigation buttons
            val navigationLayout =
                    LinearLayout(this@AddPartActivity).apply {
                        orientation = LinearLayout.HORIZONTAL
                        setPadding(0, 16, 0, 0)
                    }

            backButton =
                    Button(this@AddPartActivity).apply {
                        text = "Back"
                        setOnClickListener { viewModel.goToPreviousStep() }
                    }

            val spacer =
                    View(this@AddPartActivity).apply {
                        layoutParams = LinearLayout.LayoutParams(0, 0, 1f)
                    }

            nextButton =
                    Button(this@AddPartActivity).apply {
                        text = "Next"
                        setOnClickListener { handleNextButton() }
                    }

            navigationLayout.addView(backButton)
            navigationLayout.addView(spacer)
            navigationLayout.addView(nextButton)

            // Add all views to main layout
            addView(headerLayout)
            addView(errorBanner)
            addView(statusTicker)
            addView(fragmentContainer)
            addView(navigationLayout)
        }
    }

    private fun showStatus(message: String) {
        statusText.text = message
        statusTicker.visibility = View.VISIBLE
    }

    private fun hideStatus() {
        statusTicker.visibility = View.GONE
        statusText.text = ""
    }

    private fun initializeComponents() {
        authManager = AuthManager(this)
    }

    private fun setupViewModel() {
        // Switch to real HTTP service using Retrofit
        val realService = RealAddPartApiService(authManager)
        val repository = AddPartRepository(realService)

        val factory = AddPartViewModelFactory(repository, authManager)
        viewModel = ViewModelProvider(this, factory)[AddPartViewModel::class.java]
    }

    private fun observeViewModel() {
        // Observe form state for step changes
        lifecycleScope.launch {
            viewModel.formState.collect { formState ->
                updateStepIndicator(formState.currentStep)
                updateProgressBar(formState.currentStep)
                loadFragmentForStep(formState.currentStep)
                updateNavigationButtons(formState.currentStep)
            }
        }

        // Observe UI state for errors and loading
        lifecycleScope.launch {
            viewModel.uiState.collect { uiState ->
                if (uiState.error != null) {
                    showError(uiState.error)
                } else {
                    hideError()
                }

                // Status ticker messages
                if (uiState.isUploadingImage) {
                    showStatus("Uploading image...")
                } else if (uiState.isSubmitting) {
                    showStatus("Creating part...")
                } else if (uiState.submitSuccess) {
                    showStatus("Part created. Proceeding to storage location...")
                } else {
                    hideStatus()
                }

                if (uiState.submitSuccess) {
                    // Navigate to storage location
                    navigateToStorageLocation()
                }
            }
        }
    }

    private fun handleIntentExtras() {
        // If we come from crop screen, add the cropped image immediately to the form
        intent.getStringExtra(EXTRA_CROPPED_IMAGE_URI)?.let { uriString ->
            try {
                val uri = android.net.Uri.parse(uriString)
                val file =
                        when (uri.scheme) {
                            "file" -> java.io.File(uri.path!!)
                            "content" -> {
                                // Copy to a temp file
                                val input = contentResolver.openInputStream(uri)
                                val temp = java.io.File.createTempFile("cropped_", ".jpg", cacheDir)
                                input.use { inp ->
                                    temp.outputStream().use { out -> inp?.copyTo(out) }
                                }
                                temp
                            }
                            else -> java.io.File(uriString)
                        }
                viewModel.addImage(file)
            } catch (e: Exception) {
                Toast.makeText(this, "Failed to attach cropped image", Toast.LENGTH_SHORT).show()
            } finally {
                // Prevent duplicate re-processing on config changes
                intent.removeExtra(EXTRA_CROPPED_IMAGE_URI)
            }
        }

        // Optional: handle preselected category if provided in future
        intent.getStringExtra(EXTRA_CATEGORY_ID)?.let { _ ->
            // No-op for now
        }
    }

    private fun updateStepIndicator(step: AddPartStep) {
        val stepText =
                when (step) {
                    AddPartStep.IMAGES -> "Step 1 of 6: Add Images"
                    AddPartStep.CATEGORY -> "Step 2 of 6: Select Category"
                    AddPartStep.PART_NUMBER -> "Step 3 of 6: Part Number"
                    AddPartStep.VEHICLE_SELECTION -> "Step 3 of 6: Vehicle Selection"
                    AddPartStep.ATTRIBUTES -> "Step 4 of 6: Attributes"
                    AddPartStep.CONDITION_PRICING -> "Step 5 of 6: Condition & Pricing"
                    AddPartStep.SUBMIT -> "Step 6 of 6: Review & Submit"
                    AddPartStep.STORAGE_LOCATION -> "Storage Location"
                }
        stepIndicator.text = stepText
    }

    private fun updateProgressBar(step: AddPartStep) {
        val progress =
                when (step) {
                    AddPartStep.IMAGES -> 1
                    AddPartStep.CATEGORY -> 2
                    AddPartStep.PART_NUMBER, AddPartStep.VEHICLE_SELECTION -> 3
                    AddPartStep.ATTRIBUTES -> 4
                    AddPartStep.CONDITION_PRICING -> 5
                    AddPartStep.SUBMIT -> 6
                    AddPartStep.STORAGE_LOCATION -> 6
                }
        progressBar.progress = progress
    }

    private fun loadFragmentForStep(step: AddPartStep) {
        val fragment =
                when (step) {
                    AddPartStep.IMAGES -> ImagesFragment()
                    AddPartStep.CATEGORY -> CategoryFragment()
                    AddPartStep.PART_NUMBER -> PartNumberFragment()
                    AddPartStep.VEHICLE_SELECTION -> VehicleSelectionFragment()
                    AddPartStep.ATTRIBUTES -> AttributesFragment()
                    AddPartStep.CONDITION_PRICING -> ConditionPricingFragment()
                    AddPartStep.SUBMIT -> SubmitFragment()
                    AddPartStep.STORAGE_LOCATION -> return // Handled by separate activity
                }

        supportFragmentManager.beginTransaction().replace(fragmentContainer.id, fragment).commit()
    }

    private fun updateNavigationButtons(step: AddPartStep) {
        // On Images step, hide Back/Next (fragment shows Retake/Continue)
        if (step == AddPartStep.IMAGES) {
            backButton.visibility = View.GONE
            nextButton.visibility = View.GONE
            return
        } else {
            backButton.visibility = View.VISIBLE
            nextButton.visibility = View.VISIBLE
        }

        // Update next button text and behavior for other steps
        nextButton.text =
                when (step) {
                    AddPartStep.SUBMIT -> "Create Part"
                    AddPartStep.STORAGE_LOCATION -> "Save Location"
                    else -> "Next"
                }

        // Enable/disable next button based on step validation
        nextButton.isEnabled =
                when (step) {
                    AddPartStep.PART_NUMBER -> viewModel.canProceedFromPartNumber()
                    AddPartStep.VEHICLE_SELECTION -> viewModel.canProceedFromVehicleSelection()
                    else -> true
                }
    }

    private fun handleNextButton() {
        val currentStep = viewModel.formState.value.currentStep

        when (currentStep) {
            AddPartStep.SUBMIT -> {
                // Submit the part
                submitPart()
            }
            else -> {
                // Go to next step
                viewModel.goToNextStep()
            }
        }
    }

    private fun submitPart() {
        viewModel.submitPart { partId, title -> navigateToStorageLocation(partId, title) }
    }

    private fun navigateToStorageLocation(partId: Int? = null, partTitle: String? = null) {
        val intent =
                Intent(this, StorageLocationActivity::class.java).apply {
                    partId?.let { putExtra(StorageLocationActivity.EXTRA_PART_ID, it) }
                    partTitle?.let { putExtra(StorageLocationActivity.EXTRA_PART_TITLE, it) }
                }
        storageLocationLauncher.launch(intent)
    }

    private fun showError(message: String) {
        errorText.text = message
        errorBanner.visibility = View.VISIBLE
    }

    private fun hideError() {
        errorBanner.visibility = View.GONE
        viewModel.clearError()
    }

    override fun onBackPressed() {
        val currentStep = viewModel.formState.value.currentStep
        if (currentStep == AddPartStep.IMAGES) {
            super.onBackPressed()
        } else {
            viewModel.goToPreviousStep()
        }
    }
}

/** ViewModel factory for dependency injection */
class AddPartViewModelFactory(
        private val repository: AddPartRepository,
        private val authManager: AuthManager
) : ViewModelProvider.Factory {

    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(AddPartViewModel::class.java)) {
            return AddPartViewModel(repository, authManager) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}

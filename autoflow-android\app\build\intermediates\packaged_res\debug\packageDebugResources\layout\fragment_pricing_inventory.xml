<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Pricing Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Pricing Information"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <!-- Price and Cost Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <!-- Selling Price -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                app:boxStrokeColor="@color/brand_teal"
                app:hintTextColor="@color/brand_teal">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/partPriceInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Selling Price ($) *"
                    android:inputType="numberDecimal"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Cost Price -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                app:boxStrokeColor="@color/brand_teal"
                app:hintTextColor="@color/brand_teal">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/partCostInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Cost Price ($)"
                    android:inputType="numberDecimal"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- Discount and Tax Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="24dp">

            <!-- Discount -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                app:boxStrokeColor="@color/brand_teal"
                app:hintTextColor="@color/brand_teal">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/partDiscountInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Discount (%)"
                    android:inputType="numberDecimal"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Tax Rate -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                app:boxStrokeColor="@color/brand_teal"
                app:hintTextColor="@color/brand_teal">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/partTaxInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Tax Rate (%)"
                    android:inputType="numberDecimal"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- Inventory Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Inventory Information"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <!-- Stock and Min Stock Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <!-- Current Stock -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                app:boxStrokeColor="@color/brand_teal"
                app:hintTextColor="@color/brand_teal">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/partStockInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Current Stock *"
                    android:inputType="number"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Minimum Stock -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                app:boxStrokeColor="@color/brand_teal"
                app:hintTextColor="@color/brand_teal">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/partMinStockInput"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Minimum Stock"
                    android:inputType="number"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- Storage Location -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:boxStrokeColor="@color/brand_teal"
            app:hintTextColor="@color/brand_teal">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/partLocationInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Storage Location"
                android:inputType="text"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Supplier Information -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Supplier Information"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="16dp" />

        <!-- Supplier Name -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:boxStrokeColor="@color/brand_teal"
            app:hintTextColor="@color/brand_teal">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/partSupplierInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Supplier Name"
                android:inputType="text"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Supplier Part Number -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:boxStrokeColor="@color/brand_teal"
            app:hintTextColor="@color/brand_teal">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/partSupplierPartNumberInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Supplier Part Number"
                android:inputType="text"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

    </LinearLayout>

</ScrollView>

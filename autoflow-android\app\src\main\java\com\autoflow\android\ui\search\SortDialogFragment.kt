package com.autoflow.android.ui.search

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.Button
import android.widget.RadioButton
import android.widget.RadioGroup
import androidx.fragment.app.DialogFragment
import com.autoflow.android.R
import com.google.android.material.dialog.MaterialAlertDialogBuilder

/**
 * Dialog fragment for sort options
 */
class SortDialogFragment : DialogFragment() {

    companion object {
        private const val ARG_CURRENT_SORT = "current_sort"

        fun newInstance(currentSort: SortOption): SortDialogFragment {
            val fragment = SortDialogFragment()
            val args = Bundle()
            args.putSerializable(ARG_CURRENT_SORT, currentSort)
            fragment.arguments = args
            return fragment
        }
    }

    interface OnSortAppliedListener {
        fun onSortApplied(sortOption: SortOption)
    }

    private var listener: OnSortAppliedListener? = null
    private var currentSort = SortOption.RELEVANCE

    // UI Components
    private lateinit var sortOptionsGroup: RadioGroup
    private lateinit var sortRelevance: RadioButton
    private lateinit var sortPriceLowToHigh: RadioButton
    private lateinit var sortPriceHighToLow: RadioButton
    private lateinit var sortNameAToZ: RadioButton
    private lateinit var sortNameZToA: RadioButton
    private lateinit var sortNewestFirst: RadioButton
    private lateinit var cancelSortButton: Button
    private lateinit var applySortButton: Button

    fun setOnSortAppliedListener(listener: OnSortAppliedListener) {
        this.listener = listener
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            currentSort = it.getSerializable(ARG_CURRENT_SORT) as? SortOption ?: SortOption.RELEVANCE
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val view = LayoutInflater.from(requireContext()).inflate(R.layout.dialog_sort, null)
        initializeViews(view)
        setupClickListeners()
        setCurrentSort()

        return MaterialAlertDialogBuilder(requireContext())
            .setView(view)
            .create()
    }

    private fun initializeViews(view: View) {
        sortOptionsGroup = view.findViewById(R.id.sortOptionsGroup)
        sortRelevance = view.findViewById(R.id.sortRelevance)
        sortPriceLowToHigh = view.findViewById(R.id.sortPriceLowToHigh)
        sortPriceHighToLow = view.findViewById(R.id.sortPriceHighToLow)
        sortNameAToZ = view.findViewById(R.id.sortNameAToZ)
        sortNameZToA = view.findViewById(R.id.sortNameZToA)
        sortNewestFirst = view.findViewById(R.id.sortNewestFirst)
        cancelSortButton = view.findViewById(R.id.cancelSortButton)
        applySortButton = view.findViewById(R.id.applySortButton)
    }

    private fun setupClickListeners() {
        cancelSortButton.setOnClickListener {
            dismiss()
        }

        applySortButton.setOnClickListener {
            applySort()
        }
    }

    private fun setCurrentSort() {
        when (currentSort) {
            SortOption.RELEVANCE -> sortRelevance.isChecked = true
            SortOption.PRICE_LOW_TO_HIGH -> sortPriceLowToHigh.isChecked = true
            SortOption.PRICE_HIGH_TO_LOW -> sortPriceHighToLow.isChecked = true
            SortOption.NAME_A_TO_Z -> sortNameAToZ.isChecked = true
            SortOption.NAME_Z_TO_A -> sortNameZToA.isChecked = true
            SortOption.NEWEST_FIRST -> sortNewestFirst.isChecked = true
        }
    }

    private fun applySort() {
        val selectedSort = when (sortOptionsGroup.checkedRadioButtonId) {
            R.id.sortRelevance -> SortOption.RELEVANCE
            R.id.sortPriceLowToHigh -> SortOption.PRICE_LOW_TO_HIGH
            R.id.sortPriceHighToLow -> SortOption.PRICE_HIGH_TO_LOW
            R.id.sortNameAToZ -> SortOption.NAME_A_TO_Z
            R.id.sortNameZToA -> SortOption.NAME_Z_TO_A
            R.id.sortNewestFirst -> SortOption.NEWEST_FIRST
            else -> SortOption.RELEVANCE
        }

        listener?.onSortApplied(selectedSort)
        dismiss()
    }
}

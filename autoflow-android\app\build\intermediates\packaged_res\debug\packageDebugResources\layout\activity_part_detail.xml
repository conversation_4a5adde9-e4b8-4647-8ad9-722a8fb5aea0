<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Part Image with Share Button -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="300dp"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ImageView
                    android:id="@+id/partImage"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    android:src="@drawable/ic_part_placeholder" />

                <!-- Menu Button - Top Right -->
                <ImageButton
                    android:id="@+id/menuButton"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_gravity="top|end"
                    android:layout_margin="12dp"
                    android:src="@drawable/ic_more_vert"
                    android:background="@drawable/floating_button_background"
                    android:contentDescription="Menu"
                    android:alpha="0.9" />

                <!-- Share Button - Bottom Right -->
                <ImageButton
                    android:id="@+id/shareButton"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_gravity="bottom|end"
                    android:layout_margin="12dp"
                    android:src="@drawable/ic_share"
                    android:background="@drawable/floating_share_button_background"
                    android:contentDescription="Share"
                    android:alpha="0.8" />

            </FrameLayout>

        </androidx.cardview.widget.CardView>

        <!-- Part Information Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@android:color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp"
                android:background="@android:color/white">

                <!-- Part Name -->
                <TextView
                    android:id="@+id/partName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Part Name"
                    android:textColor="@color/text_primary"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <!-- Part Number -->
                <TextView
                    android:id="@+id/partNumber"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Part #: BP001"
                    android:textColor="@color/text_secondary"
                    android:textSize="16sp"
                    android:layout_marginBottom="12dp" />

                <!-- Brand and Category -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:id="@+id/partBrand"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/chip_background_teal"
                        android:paddingHorizontal="12dp"
                        android:paddingVertical="6dp"
                        android:text="VW"
                        android:textColor="@color/brand_teal"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        android:layout_marginEnd="8dp" />

                    <TextView
                        android:id="@+id/partCategory"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/chip_background_mustard"
                        android:paddingHorizontal="12dp"
                        android:paddingVertical="6dp"
                        android:text="Brakes"
                        android:textColor="@color/brand_mustard"
                        android:textSize="12sp"
                        android:textStyle="bold" />

                </LinearLayout>

                <!-- Price and Stock -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:id="@+id/partPrice"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="$89.99"
                        android:textColor="@color/brand_teal"
                        android:textSize="28sp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <View
                            android:id="@+id/stockIndicator"
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:background="@drawable/stock_indicator_green"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:id="@+id/partStock"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="15 in stock"
                            android:textColor="@color/stock_green"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                </LinearLayout>

                <!-- Description -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Description"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/partDescription"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="High-quality brake pads for front wheels. Compatible with various VW models."
                    android:textColor="@color/text_secondary"
                    android:textSize="16sp"
                    android:lineSpacingExtra="4dp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <Button
                android:id="@+id/contactSellerButton"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:background="@drawable/button_outline_teal"
                android:text="Contact Seller"
                android:textColor="@color/brand_teal"
                android:textSize="16sp"
                android:textStyle="bold" />

            <Button
                android:id="@+id/addToCartButton"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:background="@drawable/button_filled_teal"
                android:text="Add to Cart"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>

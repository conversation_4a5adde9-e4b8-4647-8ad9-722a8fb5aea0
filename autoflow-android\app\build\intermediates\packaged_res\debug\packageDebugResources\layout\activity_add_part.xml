<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".ui.parts.AddPartActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="24dp">

            <Button
                android:id="@+id/cancelButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Cancel"
                android:textAllCaps="false"
                android:background="@android:color/transparent"
                android:textColor="@color/brand_primary" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Add New Part"
                android:textSize="20sp"
                android:textStyle="bold"
                android:gravity="center"
                android:textColor="@color/brand_black" />

            <Button
                android:id="@+id/saveButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Save"
                android:textAllCaps="false"
                android:background="@color/brand_primary"
                android:textColor="@android:color/white"
                android:paddingHorizontal="24dp" />

        </LinearLayout>

        <!-- Status Text -->
        <TextView
            android:id="@+id/statusText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Ready to add part details"
            android:textSize="14sp"
            android:textColor="@color/brand_primary"
            android:gravity="center"
            android:layout_marginBottom="16dp"
            android:visibility="visible" />

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="16dp"
            android:visibility="gone" />

        <!-- Image Preview -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <ImageView
                android:id="@+id/partImageView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:background="@color/light_gray"
                tools:src="@drawable/ic_camera" />

        </androidx.cardview.widget.CardView>

        <!-- Category Display -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Category"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp"
            android:textColor="@color/brand_black" />

        <TextView
            android:id="@+id/categoryNameText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Engine Parts"
            android:textSize="18sp"
            android:textColor="@color/brand_primary"
            android:background="@drawable/spinner_background"
            android:padding="16dp"
            android:layout_marginBottom="24dp"
            tools:text="Engine Parts" />

        <!-- Form Fields -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="Part Title *">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/titleEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:maxLines="2" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="Description">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/descriptionEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textMultiLine"
                android:lines="3"
                android:maxLines="5" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Part Number (conditional) -->
        <LinearLayout
            android:id="@+id/partNumberLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="16dp"
            android:visibility="gone">

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Part Number *">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/partNumberEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- Storage Location -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="Storage Location">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/storageLocationEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Price and Stock Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:hint="Price (KES) *">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/priceEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="numberDecimal" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:hint="Stock Quantity *">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/stockEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- Category Attributes (dynamic) -->
        <LinearLayout
            android:id="@+id/attributesContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="16dp" />

        <!-- Condition -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Condition"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp"
            android:textColor="@color/brand_black" />

        <Spinner
            android:id="@+id/conditionSpinner"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginBottom="32dp"
            android:background="@drawable/spinner_background" />

        <!-- Bottom Spacing -->
        <View
            android:layout_width="match_parent"
            android:layout_height="24dp" />

    </LinearLayout>

</ScrollView>

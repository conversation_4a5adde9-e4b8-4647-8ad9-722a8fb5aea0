<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/dashboard_background">

    <!-- Search and Filters Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="@android:color/white"
        android:elevation="2dp">

        <!-- Search Input -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:orientation="horizontal"
            android:background="@drawable/search_input_background"
            android:gravity="center_vertical"
            android:paddingHorizontal="16dp"
            android:layout_marginBottom="12dp">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_search"
                android:layout_marginEnd="12dp" />

            <EditText
                android:id="@+id/searchInput"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@android:color/transparent"
                android:hint="Search for products, parts, or SKUs..."
                android:textColorHint="@color/text_secondary"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:inputType="text"
                android:imeOptions="actionSearch"
                android:maxLines="1"
                android:singleLine="true" />

            <ImageButton
                android:id="@+id/clearSearchButton"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_clear"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="Clear search"
                android:visibility="gone"
                android:padding="4dp" />

        </LinearLayout>

        <!-- Filters and Sort Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- Filters Button -->
            <Button
                android:id="@+id/filtersButton"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="Filters"
                android:textColor="@color/brand_teal"
                android:background="@drawable/button_outline_teal"
                android:drawableStart="@drawable/ic_filter"
                android:drawablePadding="8dp"
                android:paddingHorizontal="16dp"
                android:textSize="14sp"
                android:layout_marginEnd="12dp" />

            <!-- Sort Button -->
            <Button
                android:id="@+id/sortButton"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="Sort"
                android:textColor="@color/brand_teal"
                android:background="@drawable/button_outline_teal"
                android:drawableStart="@drawable/ic_sort"
                android:drawablePadding="8dp"
                android:paddingHorizontal="16dp"
                android:textSize="14sp"
                android:layout_marginEnd="12dp" />

            <!-- Results Count -->
            <TextView
                android:id="@+id/resultsCountText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="0 results"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:gravity="end" />

        </LinearLayout>

    </LinearLayout>

    <!-- Active Filters Chips -->
    <HorizontalScrollView
        android:id="@+id/activeFiltersContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:paddingHorizontal="16dp"
        android:paddingVertical="8dp"
        android:background="@color/background_light">

        <LinearLayout
            android:id="@+id/activeFiltersLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal" />

    </HorizontalScrollView>

    <!-- Search Results -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <!-- Results RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/resultsRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="16dp"
            android:clipToPadding="false" />

        <!-- Loading State -->
        <LinearLayout
            android:id="@+id/loadingLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="gone">

            <ProgressBar
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Searching for parts..."
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />

        </LinearLayout>

        <!-- Empty State -->
        <LinearLayout
            android:id="@+id/emptyLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="gone"
            android:padding="32dp">

            <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:src="@drawable/ic_search_empty"
                android:tint="@color/text_secondary"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="No parts found"
                android:textColor="@color/text_primary"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Try adjusting your search or filters"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:gravity="center" />

        </LinearLayout>

        <!-- Error State -->
        <LinearLayout
            android:id="@+id/errorLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="gone"
            android:padding="32dp">

            <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:src="@drawable/ic_error"
                android:tint="@color/brand_red"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Search failed"
                android:textColor="@color/text_primary"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/errorMessageText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Please check your connection and try again"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:gravity="center"
                android:layout_marginBottom="16dp" />

            <Button
                android:id="@+id/retryButton"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:text="Retry"
                android:textColor="@color/text_on_brand"
                android:background="@drawable/button_filled_teal"
                android:paddingHorizontal="24dp"
                android:textSize="14sp" />

        </LinearLayout>

    </FrameLayout>

    <!-- Pagination -->
    <LinearLayout
        android:id="@+id/paginationLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:padding="16dp"
        android:background="@android:color/white"
        android:elevation="2dp"
        android:visibility="gone">

        <Button
            android:id="@+id/previousPageButton"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="Previous"
            android:textColor="@color/brand_teal"
            android:background="@drawable/button_outline_teal"
            android:paddingHorizontal="16dp"
            android:textSize="14sp"
            android:layout_marginEnd="16dp"
            android:enabled="false" />

        <TextView
            android:id="@+id/pageInfoText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Page 1 of 5"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            android:layout_marginHorizontal="16dp" />

        <Button
            android:id="@+id/nextPageButton"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="Next"
            android:textColor="@color/brand_teal"
            android:background="@drawable/button_outline_teal"
            android:paddingHorizontal="16dp"
            android:textSize="14sp"
            android:layout_marginStart="16dp" />

    </LinearLayout>

</LinearLayout>

package com.autoflow.android.ui.parts

import android.content.Intent
import android.graphics.Typeface
import android.net.Uri
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.View
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import com.autoflow.android.data.api.PartsApiService
import com.autoflow.android.data.repository.AuthRepository

class HierarchicalCategorySelectionActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "HierarchicalCategory"
    }

    private lateinit var authRepository: AuthRepository
    private lateinit var partsApiService: PartsApiService

    // UI Elements
    private lateinit var imageView: ImageView
    private lateinit var categoryDropdown: LinearLayout
    private lateinit var dropdownTrigger: LinearLayout
    private lateinit var selectedCategoryText: TextView
    private lateinit var dropdownIcon: ImageView
    private lateinit var dropdownContent: LinearLayout
    private lateinit var searchInput: EditText
    private lateinit var categoriesList: LinearLayout
    private lateinit var statusText: TextView
    private lateinit var continueButton: Button
    private lateinit var backButton: Button

    private var croppedImageUri: Uri? = null
    private var selectedCategory: Category? = null
    private val allCategories = mutableListOf<Category>()
    private val rootCategories = mutableListOf<Category>()
    private var isDropdownOpen = false

    data class Category(
            val id: Int,
            val name: String,
            val description: String? = null,
            val requiresPartNumber: Boolean = false,
            val hasAttributes: Boolean = false,
            val parentId: Int? = null,
            val level: Int = 0,
            val children: MutableList<Category> = mutableListOf()
    ) {
        val isLeaf: Boolean
            get() = children.isEmpty()
        val hasChildren: Boolean
            get() = children.isNotEmpty()

        fun getAllDescendants(): List<Category> {
            val descendants = mutableListOf<Category>()
            children.forEach { child ->
                descendants.add(child)
                descendants.addAll(child.getAllDescendants())
            }
            return descendants
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        authRepository = AuthRepository(this)
        partsApiService = PartsApiService(this)

        createHierarchicalUI()
        loadCroppedImage()
        loadCategories()
    }

    private fun createHierarchicalUI() {
        Log.d(TAG, "Creating hierarchical UI")

        // Create main layout
        val mainLayout =
                LinearLayout(this).apply {
                    orientation = LinearLayout.VERTICAL
                    setPadding(32, 32, 32, 32)
                }

        // Create header
        val headerLayout = LinearLayout(this).apply { orientation = LinearLayout.HORIZONTAL }

        backButton =
                Button(this).apply {
                    text = "Back"
                    setOnClickListener { finish() }
                }

        val titleText =
                TextView(this).apply {
                    text = "Select Category"
                    textSize = 20f
                    layoutParams =
                            LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
                }

        continueButton =
                Button(this).apply {
                    text = "Continue"
                    isEnabled = false
                    setOnClickListener { proceedToNextStep() }
                }

        headerLayout.addView(backButton)
        headerLayout.addView(titleText)
        headerLayout.addView(continueButton)

        // Create status text
        statusText =
                TextView(this).apply {
                    text = "Loading categories..."
                    textSize = 14f
                    setPadding(0, 16, 0, 16)
                }

        // Create image view
        imageView =
                ImageView(this).apply {
                    layoutParams = LinearLayout.LayoutParams(200, 200)
                    scaleType = ImageView.ScaleType.CENTER_CROP
                }

        // Create hierarchical dropdown
        createHierarchicalDropdown()

        // Add all views to main layout
        mainLayout.addView(headerLayout)
        mainLayout.addView(statusText)
        mainLayout.addView(imageView)
        mainLayout.addView(categoryDropdown)

        // Set as content view
        setContentView(mainLayout)

        Log.d(TAG, "Hierarchical UI created successfully")
    }

    private fun createHierarchicalDropdown() {
        // Main dropdown container
        categoryDropdown =
                LinearLayout(this).apply {
                    orientation = LinearLayout.VERTICAL
                    setPadding(0, 24, 0, 0)
                }

        // Category label
        val categoryLabel =
                TextView(this).apply {
                    text = "Category"
                    textSize = 16f
                    setPadding(0, 0, 0, 8)
                    setTypeface(null, Typeface.BOLD)
                }

        // Dropdown trigger (like web version's NestedSelectTrigger)
        dropdownTrigger =
                LinearLayout(this).apply {
                    orientation = LinearLayout.HORIZONTAL
                    layoutParams =
                            LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, 120)
                    setPadding(16, 16, 16, 16)
                    setBackgroundResource(android.R.drawable.edit_text)
                    setOnClickListener { toggleDropdown() }
                }

        selectedCategoryText =
                TextView(this).apply {
                    text = "Select a category"
                    textSize = 16f
                    layoutParams =
                            LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
                }

        dropdownIcon =
                ImageView(this).apply {
                    setImageResource(android.R.drawable.arrow_down_float)
                    layoutParams = LinearLayout.LayoutParams(48, 48)
                }

        dropdownTrigger.addView(selectedCategoryText)
        dropdownTrigger.addView(dropdownIcon)

        // Dropdown content (like web version's NestedSelectContent)
        dropdownContent =
                LinearLayout(this).apply {
                    orientation = LinearLayout.VERTICAL
                    visibility = View.GONE
                    setBackgroundResource(android.R.drawable.dialog_frame)
                    setPadding(8, 8, 8, 8)
                }

        // Search input (like web version's search)
        searchInput =
                EditText(this).apply {
                    hint = "Search categories..."
                    setPadding(16, 16, 16, 16)
                    addTextChangedListener(
                            object : TextWatcher {
                                override fun beforeTextChanged(
                                        s: CharSequence?,
                                        start: Int,
                                        count: Int,
                                        after: Int
                                ) {}
                                override fun onTextChanged(
                                        s: CharSequence?,
                                        start: Int,
                                        before: Int,
                                        count: Int
                                ) {}
                                override fun afterTextChanged(s: Editable?) {
                                    filterCategories(s.toString())
                                }
                            }
                    )
                }

        // Categories list container
        categoriesList = LinearLayout(this).apply { orientation = LinearLayout.VERTICAL }

        dropdownContent.addView(searchInput)
        dropdownContent.addView(categoriesList)

        categoryDropdown.addView(categoryLabel)
        categoryDropdown.addView(dropdownTrigger)
        categoryDropdown.addView(dropdownContent)
    }

    private fun toggleDropdown() {
        isDropdownOpen = !isDropdownOpen
        dropdownContent.visibility = if (isDropdownOpen) View.VISIBLE else View.GONE

        // Rotate dropdown icon
        dropdownIcon.rotation = if (isDropdownOpen) 180f else 0f

        Log.d(TAG, "Dropdown toggled: $isDropdownOpen")
    }

    private fun loadCroppedImage() {
        val imageUriString = intent.getStringExtra("cropped_image_uri")
        if (imageUriString != null) {
            croppedImageUri = Uri.parse(imageUriString)
            imageView.setImageURI(croppedImageUri)
            Log.d(TAG, "Image loaded successfully")
        } else {
            Toast.makeText(this, "Error loading image", Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    private fun loadCategories() {
        Log.d(TAG, "Loading hierarchical categories...")

        // Load hierarchical categories
        allCategories.clear()
        rootCategories.clear()

        val flatCategories = getHierarchicalCategories()
        allCategories.addAll(flatCategories)

        // Build tree structure (like web version's buildCategoryTree)
        buildCategoryTree()

        // Display categories
        displayCategories()

        statusText.text = "Select a category for your part"
        Log.d(
                TAG,
                "Hierarchical categories loaded: ${allCategories.size} total, ${rootCategories.size} root"
        )
    }

    private fun getHierarchicalCategories(): List<Category> {
        return listOf(
                // Root categories
                Category(
                        1,
                        "Engine Parts",
                        "Engine components and accessories",
                        true,
                        true,
                        null,
                        0
                ),
                Category(
                        2,
                        "Body Parts",
                        "Exterior and interior body components",
                        false,
                        true,
                        null,
                        0
                ),
                Category(
                        3,
                        "Electrical Parts",
                        "Electrical components and wiring",
                        true,
                        true,
                        null,
                        0
                ),
                Category(
                        4,
                        "Suspension Parts",
                        "Suspension and steering components",
                        true,
                        true,
                        null,
                        0
                ),
                Category(5, "Brake Parts", "Brake system components", true, true, null, 0),

                // Engine subcategories
                Category(11, "Engine Mounts", "Engine mounting components", true, true, 1, 1),
                Category(12, "Pistons & Rings", "Piston assemblies and rings", true, true, 1, 1),
                Category(
                        13,
                        "Valves & Timing",
                        "Valve train and timing components",
                        true,
                        true,
                        1,
                        1
                ),

                // Body subcategories
                Category(21, "Doors & Windows", "Door and window components", false, true, 2, 1),
                Category(22, "Bumpers & Grilles", "Front and rear bumpers", false, true, 2, 1),
                Category(
                        23,
                        "Lights & Mirrors",
                        "Lighting and mirror assemblies",
                        false,
                        true,
                        2,
                        1
                ),

                // Electrical subcategories
                Category(31, "Sensors", "Various automotive sensors", true, true, 3, 1),
                Category(
                        32,
                        "Wiring & Connectors",
                        "Electrical wiring components",
                        true,
                        true,
                        3,
                        1
                ),
                Category(33, "Control Modules", "ECU and control units", true, true, 3, 1),

                // Suspension subcategories
                Category(41, "Shock Absorbers", "Shock absorber assemblies", true, true, 4, 1),
                Category(42, "Springs", "Coil and leaf springs", true, true, 4, 1),
                Category(43, "Control Arms", "Suspension control arms", true, true, 4, 1),

                // Brake subcategories
                Category(51, "Brake Pads", "Brake pad assemblies", true, true, 5, 1),
                Category(52, "Brake Discs", "Brake disc rotors", true, true, 5, 1),
                Category(53, "Brake Calipers", "Brake caliper assemblies", true, true, 5, 1),

                // Third level categories (Engine Mounts subcategories)
                Category(111, "Front Engine Mounts", "Front engine mounting", true, true, 11, 2),
                Category(112, "Rear Engine Mounts", "Rear engine mounting", true, true, 11, 2),
                Category(113, "Transmission Mounts", "Transmission mounting", true, true, 11, 2)
        )
    }

    private fun buildCategoryTree() {
        // Create category map for quick lookup
        val categoryMap = mutableMapOf<Int, Category>()
        allCategories.forEach { category -> categoryMap[category.id] = category }

        // Build parent-child relationships
        allCategories.forEach { category ->
            category.parentId?.let { parentId -> categoryMap[parentId]?.children?.add(category) }
                    ?: run { rootCategories.add(category) }
        }

        Log.d(TAG, "Category tree built: ${rootCategories.size} root categories")
    }

    private fun displayCategories(searchQuery: String = "") {
        categoriesList.removeAllViews()

        val categoriesToShow =
                if (searchQuery.isEmpty()) {
                    rootCategories
                } else {
                    filterCategoriesBySearch(searchQuery)
                }

        categoriesToShow.forEach { category -> addCategoryToList(category, 0) }
    }

    private fun addCategoryToList(category: Category, level: Int) {
        // Create category item (like web version's SelectItemComponent)
        val categoryItem =
                LinearLayout(this).apply {
                    orientation = LinearLayout.HORIZONTAL
                    setPadding(level * 32 + 16, 16, 16, 16)
                    setOnClickListener {
                        if (category.isLeaf) {
                            selectCategory(category)
                        }
                    }
                }

        // Add chevron for parent categories
        if (category.hasChildren) {
            val chevron =
                    TextView(this).apply {
                        text = "▶"
                        textSize = 12f
                        setPadding(0, 0, 8, 0)
                    }
            categoryItem.addView(chevron)
        }

        // Category name
        val categoryName =
                TextView(this).apply {
                    text = category.name
                    textSize = 16f
                    if (category.hasChildren) {
                        setTypeface(null, Typeface.BOLD)
                    }
                    if (!category.isLeaf) {
                        setTextColor(resources.getColor(android.R.color.darker_gray, null))
                    }
                }
        categoryItem.addView(categoryName)

        categoriesList.addView(categoryItem)

        // Add children (always show in hierarchical view)
        category.children.forEach { child -> addCategoryToList(child, level + 1) }
    }

    private fun selectCategory(category: Category) {
        selectedCategory = category
        selectedCategoryText.text = category.name
        continueButton.isEnabled = true

        // Close dropdown
        isDropdownOpen = false
        dropdownContent.visibility = View.GONE
        dropdownIcon.rotation = 0f

        statusText.text =
                "Selected: ${category.name}" +
                        if (category.requiresPartNumber) " (Part number required)"
                        else " (Part number optional)"

        Log.d(TAG, "Category selected: ${category.name}")
    }

    private fun filterCategories(query: String) {
        displayCategories(query)
    }

    private fun filterCategoriesBySearch(query: String): List<Category> {
        val filtered = mutableListOf<Category>()

        fun searchInCategory(category: Category): Boolean {
            val matches = category.name.contains(query, ignoreCase = true)
            val childMatches = category.children.any { searchInCategory(it) }

            if (matches || childMatches) {
                filtered.add(category)
                return true
            }
            return false
        }

        rootCategories.forEach { searchInCategory(it) }
        return filtered
    }

    private fun proceedToNextStep() {
        selectedCategory?.let { category ->
            val intent = Intent(this, AddPartActivity::class.java)
            intent.putExtra("cropped_image_uri", croppedImageUri.toString())
            intent.putExtra("category_id", category.id)
            intent.putExtra("category_name", category.name)
            intent.putExtra("requires_part_number", category.requiresPartNumber)
            intent.putExtra("has_attributes", category.hasAttributes)
            startActivity(intent)
            finish()
        }
    }
}

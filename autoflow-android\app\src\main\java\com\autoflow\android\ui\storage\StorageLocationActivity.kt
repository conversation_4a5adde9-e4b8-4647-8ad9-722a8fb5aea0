package com.autoflow.android.ui.storage

import android.os.Bundle
import android.view.View
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.autoflow.android.core.auth.AuthManager
import com.autoflow.android.data.api.AddPartApiClient
import com.autoflow.android.data.repositories.AddPartRepository
import com.autoflow.android.domain.models.StorageArea
import com.autoflow.android.domain.models.StorageLocationFormData
import com.autoflow.android.domain.models.StorageUnit
import kotlinx.coroutines.launch

/**
 * Storage Location Activity - Final step of Add Part flow Handles storage location assignment with
 * dynamic subtype fields Mirrors web app's storage location modal
 */
class StorageLocationActivity : AppCompatActivity() {

    companion object {
        const val EXTRA_PART_ID = "part_id"
        const val EXTRA_PART_TITLE = "part_title"
        private const val TAG = "StorageLocationActivity"
    }

    private lateinit var viewModel: StorageLocationViewModel
    private lateinit var authManager: AuthManager

    // UI Components
    private lateinit var titleText: TextView
    private lateinit var partNameText: TextView
    private lateinit var areaSpinner: Spinner
    private lateinit var unitSpinner: Spinner
    private lateinit var subtypeSpinner: Spinner
    private lateinit var quantityInput: EditText
    private lateinit var notesInput: EditText
    private lateinit var dynamicFieldsContainer: LinearLayout
    private lateinit var saveButton: Button
    private lateinit var cancelButton: Button
    private lateinit var loadingIndicator: ProgressBar
    private lateinit var errorText: TextView

    // Data
    private var partId: Int = -1
    private var partTitle: String = ""
    private val dynamicFields = mutableMapOf<String, EditText>()

    // Spinners adapters
    private lateinit var areasAdapter: ArrayAdapter<StorageArea>
    private lateinit var unitsAdapter: ArrayAdapter<StorageUnit>
    private lateinit var subtypesAdapter: ArrayAdapter<String>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(createLayout())

        extractIntentData()
        initializeComponents()
        setupViewModel()
        setupSpinners()
        setupClickListeners()
        observeViewModel()
    }

    private fun createLayout(): LinearLayout {
        return LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(24, 24, 24, 24)

            // Header
            titleText =
                    TextView(context).apply {
                        text = "Storage Location"
                        textSize = 24f
                        setTypeface(null, android.graphics.Typeface.BOLD)
                        setPadding(0, 0, 0, 16)
                    }

            partNameText =
                    TextView(context).apply {
                        text = partTitle
                        textSize = 18f
                        setPadding(0, 0, 0, 24)
                    }

            // Error display
            errorText =
                    TextView(context).apply {
                        textSize = 14f
                        setTextColor(resources.getColor(android.R.color.holo_red_dark, null))
                        visibility = View.GONE
                        setPadding(0, 0, 0, 16)
                    }

            // Storage Area
            val areaLabel =
                    TextView(context).apply {
                        text = "Storage Area *"
                        textSize = 16f
                        setTypeface(null, android.graphics.Typeface.BOLD)
                        setPadding(0, 0, 0, 8)
                    }

            areaSpinner =
                    Spinner(context).apply {
                        layoutParams =
                                LinearLayout.LayoutParams(
                                        LinearLayout.LayoutParams.MATCH_PARENT,
                                        LinearLayout.LayoutParams.WRAP_CONTENT
                                )
                        setPadding(0, 0, 0, 16)
                    }

            // Storage Unit
            val unitLabel =
                    TextView(context).apply {
                        text = "Storage Unit *"
                        textSize = 16f
                        setTypeface(null, android.graphics.Typeface.BOLD)
                        setPadding(0, 0, 0, 8)
                    }

            unitSpinner =
                    Spinner(context).apply {
                        layoutParams =
                                LinearLayout.LayoutParams(
                                        LinearLayout.LayoutParams.MATCH_PARENT,
                                        LinearLayout.LayoutParams.WRAP_CONTENT
                                )
                        setPadding(0, 0, 0, 16)
                    }

            // Location Subtype
            val subtypeLabel =
                    TextView(context).apply {
                        text = "Location Type *"
                        textSize = 16f
                        setTypeface(null, android.graphics.Typeface.BOLD)
                        setPadding(0, 0, 0, 8)
                    }

            subtypeSpinner =
                    Spinner(context).apply {
                        layoutParams =
                                LinearLayout.LayoutParams(
                                        LinearLayout.LayoutParams.MATCH_PARENT,
                                        LinearLayout.LayoutParams.WRAP_CONTENT
                                )
                        setPadding(0, 0, 0, 16)
                    }

            // Dynamic fields container
            dynamicFieldsContainer =
                    LinearLayout(context).apply {
                        orientation = LinearLayout.VERTICAL
                        setPadding(0, 0, 0, 16)
                    }

            // Quantity
            val quantityLabel =
                    TextView(context).apply {
                        text = "Quantity *"
                        textSize = 16f
                        setTypeface(null, android.graphics.Typeface.BOLD)
                        setPadding(0, 0, 0, 8)
                    }

            quantityInput =
                    EditText(context).apply {
                        hint = "Enter quantity"
                        inputType = android.text.InputType.TYPE_CLASS_NUMBER
                        setText("1")
                        setPadding(16, 16, 16, 16)
                        layoutParams =
                                LinearLayout.LayoutParams(
                                        LinearLayout.LayoutParams.MATCH_PARENT,
                                        LinearLayout.LayoutParams.WRAP_CONTENT
                                )
                    }

            // Notes
            val notesLabel =
                    TextView(context).apply {
                        text = "Notes (Optional)"
                        textSize = 16f
                        setTypeface(null, android.graphics.Typeface.BOLD)
                        setPadding(0, 16, 0, 8)
                    }

            notesInput =
                    EditText(context).apply {
                        hint = "Additional notes about the location"
                        setPadding(16, 16, 16, 16)
                        layoutParams =
                                LinearLayout.LayoutParams(
                                        LinearLayout.LayoutParams.MATCH_PARENT,
                                        120
                                )
                        gravity = android.view.Gravity.TOP
                    }

            // Loading indicator
            loadingIndicator =
                    ProgressBar(context).apply {
                        visibility = View.GONE
                        setPadding(0, 16, 0, 16)
                    }

            // Buttons
            val buttonLayout =
                    LinearLayout(context).apply {
                        orientation = LinearLayout.HORIZONTAL
                        setPadding(0, 24, 0, 0)
                    }

            cancelButton =
                    Button(context).apply {
                        text = "Cancel"
                        layoutParams =
                                LinearLayout.LayoutParams(
                                        0,
                                        LinearLayout.LayoutParams.WRAP_CONTENT,
                                        1f
                                )
                        setOnClickListener { finish() }
                    }

            val spacer = View(context).apply { layoutParams = LinearLayout.LayoutParams(16, 0) }

            saveButton =
                    Button(context).apply {
                        text = "Save Location"
                        layoutParams =
                                LinearLayout.LayoutParams(
                                        0,
                                        LinearLayout.LayoutParams.WRAP_CONTENT,
                                        2f
                                )
                        setOnClickListener { saveLocation() }
                    }

            buttonLayout.addView(cancelButton)
            buttonLayout.addView(spacer)
            buttonLayout.addView(saveButton)

            // Add all views
            addView(titleText)
            addView(partNameText)
            addView(errorText)
            addView(areaLabel)
            addView(areaSpinner)
            addView(unitLabel)
            addView(unitSpinner)
            addView(subtypeLabel)
            addView(subtypeSpinner)
            addView(dynamicFieldsContainer)
            addView(quantityLabel)
            addView(quantityInput)
            addView(notesLabel)
            addView(notesInput)
            addView(loadingIndicator)
            addView(buttonLayout)
        }
    }

    private fun extractIntentData() {
        partId = intent.getIntExtra(EXTRA_PART_ID, -1)
        partTitle = intent.getStringExtra(EXTRA_PART_TITLE) ?: "Unknown Part"

        if (partId == -1) {
            Toast.makeText(this, "Error: No part ID provided", Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    private fun initializeComponents() {
        authManager = AuthManager(this)
    }

    private fun setupViewModel() {
        val apiClient = AddPartApiClient(this, authManager)
        val repository = AddPartRepository(apiClient.apiService)

        val factory = StorageLocationViewModelFactory(repository)
        viewModel = ViewModelProvider(this, factory)[StorageLocationViewModel::class.java]
    }

    private fun setupSpinners() {
        // Areas adapter
        areasAdapter =
                ArrayAdapter(
                        this,
                        android.R.layout.simple_spinner_item,
                        mutableListOf<StorageArea>()
                )
        areasAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        areaSpinner.adapter = areasAdapter

        // Units adapter
        unitsAdapter =
                ArrayAdapter(
                        this,
                        android.R.layout.simple_spinner_item,
                        mutableListOf<StorageUnit>()
                )
        unitsAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        unitSpinner.adapter = unitsAdapter

        // Subtypes adapter
        val subtypes =
                listOf(
                        "Select location type",
                        "crate",
                        "container",
                        "shelf_section",
                        "open_shelf",
                        "cage_section",
                        "hanging_point",
                        "open_area_spot"
                )
        subtypesAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, subtypes)
        subtypesAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        subtypeSpinner.adapter = subtypesAdapter
    }

    private fun setupClickListeners() {
        areaSpinner.onItemSelectedListener =
                object : AdapterView.OnItemSelectedListener {
                    override fun onItemSelected(
                            parent: AdapterView<*>?,
                            view: View?,
                            position: Int,
                            id: Long
                    ) {
                        if (position > 0) {
                            val selectedArea = areasAdapter.getItem(position)
                            selectedArea?.let { viewModel.selectArea(it) }
                        }
                    }
                    override fun onNothingSelected(parent: AdapterView<*>?) {}
                }

        unitSpinner.onItemSelectedListener =
                object : AdapterView.OnItemSelectedListener {
                    override fun onItemSelected(
                            parent: AdapterView<*>?,
                            view: View?,
                            position: Int,
                            id: Long
                    ) {
                        if (position > 0) {
                            val selectedUnit = unitsAdapter.getItem(position)
                            selectedUnit?.let { viewModel.selectUnit(it) }
                        }
                    }
                    override fun onNothingSelected(parent: AdapterView<*>?) {}
                }

        subtypeSpinner.onItemSelectedListener =
                object : AdapterView.OnItemSelectedListener {
                    override fun onItemSelected(
                            parent: AdapterView<*>?,
                            view: View?,
                            position: Int,
                            id: Long
                    ) {
                        if (position > 0) {
                            val selectedSubtype = subtypesAdapter.getItem(position)
                            selectedSubtype?.let {
                                viewModel.selectSubtype(it)
                                createDynamicFields(it)
                            }
                        }
                    }
                    override fun onNothingSelected(parent: AdapterView<*>?) {}
                }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.areas.collect { areas ->
                areasAdapter.clear()
                areasAdapter.add(StorageArea(0, "Select storage area"))
                areasAdapter.addAll(areas)
                areasAdapter.notifyDataSetChanged()
            }
        }

        lifecycleScope.launch {
            viewModel.filteredUnits.collect { units ->
                unitsAdapter.clear()
                unitsAdapter.add(StorageUnit(0, 0, "Select storage unit", ""))
                unitsAdapter.addAll(units)
                unitsAdapter.notifyDataSetChanged()
            }
        }

        lifecycleScope.launch {
            viewModel.uiState.collect { uiState ->
                loadingIndicator.visibility = if (uiState.isLoading) View.VISIBLE else View.GONE
                saveButton.isEnabled = !uiState.isLoading

                if (uiState.error != null) {
                    showError(uiState.error)
                } else {
                    hideError()
                }

                if (uiState.isSuccess) {
                    Toast.makeText(
                                    this@StorageLocationActivity,
                                    "Location saved successfully!",
                                    Toast.LENGTH_SHORT
                            )
                            .show()
                    setResult(RESULT_OK)
                    finish()
                }
            }
        }
    }

    private fun createDynamicFields(subtype: String) {
        dynamicFieldsContainer.removeAllViews()
        dynamicFields.clear()

        val fieldsToCreate =
                when (subtype) {
                    "crate" -> listOf("level" to "Level", "crate_code" to "Crate Code")
                    "container" -> listOf("level" to "Level", "container_code" to "Container Code")
                    "shelf_section" -> listOf("level" to "Level", "section" to "Section")
                    "open_shelf" -> listOf("level" to "Level")
                    "cage_section" -> listOf("row" to "Row", "col" to "Column")
                    "hanging_point" -> listOf("point_identifier" to "Point Identifier")
                    "open_area_spot" -> listOf("spot_description" to "Spot Description")
                    else -> emptyList()
                }

        fieldsToCreate.forEach { (fieldKey, fieldLabel) ->
            val label =
                    TextView(this).apply {
                        text = "$fieldLabel *"
                        textSize = 16f
                        setTypeface(null, android.graphics.Typeface.BOLD)
                        setPadding(0, 8, 0, 8)
                    }

            val input =
                    EditText(this).apply {
                        hint = "Enter $fieldLabel"
                        setPadding(16, 16, 16, 16)
                        layoutParams =
                                LinearLayout.LayoutParams(
                                        LinearLayout.LayoutParams.MATCH_PARENT,
                                        LinearLayout.LayoutParams.WRAP_CONTENT
                                )

                        // Pre-fill container code with "C-"
                        if (fieldKey == "container_code") {
                            setText("C-")
                            setSelection(2) // Position cursor after "C-"
                        }
                    }

            dynamicFields[fieldKey] = input
            dynamicFieldsContainer.addView(label)
            dynamicFieldsContainer.addView(input)
        }
    }

    private fun saveLocation() {
        if (!validateForm()) {
            return
        }

        val locationData = buildLocationData()
        viewModel.saveLocation(partId, locationData)
    }

    private fun validateForm(): Boolean {
        val selectedArea = areaSpinner.selectedItem as? StorageArea
        val selectedUnit = unitSpinner.selectedItem as? StorageUnit
        val selectedSubtype = subtypeSpinner.selectedItem as? String
        val quantity = quantityInput.text.toString().toIntOrNull()

        when {
            selectedArea == null || selectedArea.id == 0 -> {
                showError("Please select a storage area")
                return false
            }
            selectedUnit == null || selectedUnit.id == 0 -> {
                showError("Please select a storage unit")
                return false
            }
            selectedSubtype == null || selectedSubtype == "Select location type" -> {
                showError("Please select a location type")
                return false
            }
            quantity == null || quantity < 1 -> {
                showError("Please enter a valid quantity (minimum 1)")
                return false
            }
        }

        // Validate dynamic fields
        for ((fieldKey, input) in dynamicFields) {
            if (input.text.toString().trim().isEmpty()) {
                showError("Please fill in all required fields")
                return false
            }
        }

        return true
    }

    private fun buildLocationData(): StorageLocationFormData {
        val selectedArea = areaSpinner.selectedItem as StorageArea
        val selectedUnit = unitSpinner.selectedItem as StorageUnit
        val selectedSubtype = subtypeSpinner.selectedItem as String
        val quantity = quantityInput.text.toString().toInt()
        val notes = notesInput.text.toString().trim()

        val details = mutableMapOf<String, String>()
        dynamicFields.forEach { (key, input) -> details[key] = input.text.toString().trim() }

        return StorageLocationFormData(
                areaId = selectedArea.id,
                unitId = selectedUnit.id,
                locationSubtype = selectedSubtype,
                quantity = quantity,
                notes = notes,
                details = details
        )
    }

    private fun showError(message: String) {
        errorText.text = message
        errorText.visibility = View.VISIBLE
    }

    private fun hideError() {
        errorText.visibility = View.GONE
    }
}

/** ViewModel factory for StorageLocationViewModel */
class StorageLocationViewModelFactory(private val repository: AddPartRepository) :
        ViewModelProvider.Factory {

    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(StorageLocationViewModel::class.java)) {
            return StorageLocationViewModel(repository) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}

<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="6dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:strokeColor="@color/light_gray"
    app:strokeWidth="1dp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/image_thumb"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:scaleType="centerCrop"
            android:contentDescription="Item image"/>

        <com.google.android.material.chip.Chip
            android:id="@+id/chip_main"
            style="@style/Widget.Material3.Chip.Filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top|end"
            android:layout_margin="8dp"
            android:text="MAIN"
            app:chipBackgroundColor="@color/brand_teal"
            android:textColor="@color/text_on_brand"/>

        <ImageButton
            android:id="@+id/btn_remove"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_gravity="top|start"
            android:layout_margin="8dp"
            android:backgroundTint="#66000000"
            android:contentDescription="Remove"
            android:tint="@android:color/white"
            android:src="@android:drawable/ic_menu_close_clear_cancel"/>

        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/progress_thumb"
            style="@style/Widget.Material3.LinearProgressIndicator"
            android:layout_width="match_parent"
            android:layout_height="4dp"
            android:layout_gravity="bottom"
            android:visibility="gone"/>
    </FrameLayout>

</com.google.android.material.card.MaterialCardView>


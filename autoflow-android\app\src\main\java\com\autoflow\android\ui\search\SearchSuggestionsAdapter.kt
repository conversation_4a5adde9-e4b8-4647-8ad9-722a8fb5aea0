package com.autoflow.android.ui.search

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.autoflow.android.R

/**
 * Adapter for search suggestions dropdown
 */
class SearchSuggestionsAdapter(
    private val onSuggestionClick: (String) -> Unit,
    private val onSuggestionRemove: (String) -> Unit
) : RecyclerView.Adapter<SearchSuggestionsAdapter.SuggestionViewHolder>() {

    private var suggestions = listOf<SearchSuggestion>()

    fun updateSuggestions(newSuggestions: List<SearchSuggestion>) {
        suggestions = newSuggestions
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SuggestionViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_search_suggestion, parent, false)
        return SuggestionViewHolder(view)
    }

    override fun onBindViewHolder(holder: SuggestionViewHolder, position: Int) {
        holder.bind(suggestions[position])
    }

    override fun getItemCount(): Int = suggestions.size

    inner class SuggestionViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val suggestionIcon: ImageView = itemView.findViewById(R.id.suggestionIcon)
        private val suggestionText: TextView = itemView.findViewById(R.id.suggestionText)
        private val suggestionSubtext: TextView = itemView.findViewById(R.id.suggestionSubtext)
        private val removeButton: ImageButton = itemView.findViewById(R.id.removeSuggestionButton)

        fun bind(suggestion: SearchSuggestion) {
            suggestionText.text = suggestion.query
            
            when (suggestion.type) {
                SuggestionType.RECENT -> {
                    suggestionIcon.setImageResource(R.drawable.ic_history)
                    suggestionSubtext.text = "Recent search"
                    suggestionSubtext.visibility = View.VISIBLE
                    removeButton.visibility = View.VISIBLE
                }
                SuggestionType.POPULAR -> {
                    suggestionIcon.setImageResource(R.drawable.ic_trending)
                    suggestionSubtext.text = "Popular search"
                    suggestionSubtext.visibility = View.VISIBLE
                    removeButton.visibility = View.GONE
                }
                SuggestionType.AUTOCOMPLETE -> {
                    suggestionIcon.setImageResource(R.drawable.ic_search)
                    suggestionSubtext.visibility = View.GONE
                    removeButton.visibility = View.GONE
                }
            }

            // Click listeners
            itemView.setOnClickListener {
                onSuggestionClick(suggestion.query)
            }

            removeButton.setOnClickListener {
                onSuggestionRemove(suggestion.query)
            }
        }
    }
}

/**
 * Data class for search suggestions
 */
data class SearchSuggestion(
    val query: String,
    val type: SuggestionType
)

/**
 * Types of search suggestions
 */
enum class SuggestionType {
    RECENT,      // From search history
    POPULAR,     // Popular/trending searches
    AUTOCOMPLETE // Auto-complete suggestions
}

package com.autoflow.android.data.api

/**
 * Sealed class representing the result of an API call
 * Provides type-safe handling of success, error, and loading states
 */
sealed class ApiResult<out T> {
    data class Success<T>(val data: T) : ApiResult<T>()
    data class Error(val exception: Throwable, val message: String) : ApiResult<Nothing>()
    object Loading : ApiResult<Nothing>()
}

/**
 * Extension function to safely execute API calls and wrap results in ApiResult
 */
suspend fun <T> safeApiCall(apiCall: suspend () -> T): ApiResult<T> {
    return try {
        val result = apiCall()
        ApiResult.Success(result)
    } catch (e: Exception) {
        ApiResult.Error(e, e.message ?: "Unknown error occurred")
    }
}

package com.autoflow.android.data.api.dto

// DTOs matching server contracts (Moshi)

data class CategoryDto(
        val id: Int,
        val label: String,
        val parent: Int?,
        val requirePartNumber: Boolean
)

// Fallback shape used by /api/parts/categories
data class SimpleCategoryDto(val id: Int, val name: String)

data class AttributeDto(
        val id: Int,
        val attribute: String,
        val inputType: String,
        val isRequired: Boolean = false,
        val dependsOnAttributeId: Int? = null,
        val dependsOnValue: String? = null,
        val options: List<AttributeOptionDto>? = null
)

data class AttributeOptionDto(val id: Int, val value: String)

data class CarModelDto(val id: Int, val name: String, val brandId: Int)

data class CarGenerationDto(val id: Int, val name: String, val modelId: Int)

data class CarVariationDto(val id: Int, val name: String, val generationId: Int)

data class CarTrimDto(val id: Int, val name: String, val variationId: Int)

data class CheckCompatibilityBody(val partNumber: String, val categoryId: Int)

data class CompatibilityDto(
        val partName: String?,
        val compatiblePartNumbers: List<String>,
        val isEnginePart: Boolean,
        val engineCompatibility: List<EngineCompatibilityDto>,
        val vehicleCompatibility: List<VehicleCompatibilityDto>,
        val partnumberGroup: Long?
)

data class EngineCompatibilityDto(
        val engineCode: String,
        val engineCapacity: String?,
        val fuelType: String?,
        val engineType: String?
)

data class VehicleCompatibilityDto(
        val brand: String,
        val model: String,
        val generation: String,
        val variation: String?,
        val trims: List<String>?
)

data class GenerateTitleBody(
        val categoryId: Int,
        val images: List<String>? = null,
        val attributes: Map<String, Any>? = null
)

data class GenerateDescriptionBody(
        val categoryId: Int,
        val title: String,
        val attributes: Map<String, Any>? = null
)

data class TitleDto(val title: String)

data class DescriptionDto(val description: String)

data class ImageUploadDto(val imageUrl: String)

data class CreatePartBody(
        val category_id: Int,
        val title: String,
        val description: String,
        val partnumber_group: Long?,
        val createdBy: Int,
        val images: List<CreatePartImageDto>,
        val condition: String,
        val new: StockPriceDto?,
        val used: StockPriceDto?,
        val attributes: List<AttributeValueDto>,
        val vehicles: VehiclesPayloadDto?,
        val engines: List<String>?
)

data class CreatePartImageDto(val url: String, val isMain: Boolean)

data class StockPriceDto(val stock: Int, val price: Double, val discounted_price: Double?)

data class AttributeValueDto(val attribute_id: Int, val value: String)

data class VehiclesPayloadDto(
        val mode: String,
        val list: List<VehicleCompatibilityDto>?,
        val trimId: Int?
)

data class CreatePartDto(val partId: Int, val title: String)

data class StorageAreaDto(val id: Int, val name: String)

data class StorageUnitDto(val id: Int, val areaId: Int, val name: String, val unitType: String)

data class SaveLocationBody(
        val unit_id: Int,
        val quantity: Int,
        val location_subtype: String,
        val details: Map<String, String>,
        val notes: String?
)

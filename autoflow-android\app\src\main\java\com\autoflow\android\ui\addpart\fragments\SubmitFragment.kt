package com.autoflow.android.ui.addpart.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import com.autoflow.android.domain.models.PartFormValues
import com.autoflow.android.ui.addpart.AddPartViewModel
import kotlinx.coroutines.launch

/**
 * Submit Fragment - Step 6 of Add Part flow Review and submit the complete part information Mirrors
 * web app's final review and submission
 */
class SubmitFragment : Fragment() {

    companion object {
        private const val TAG = "SubmitFragment"
    }

    private val viewModel: AddPartViewModel by activityViewModels()

    // UI Components
    private lateinit var instructionText: TextView
    private lateinit var reviewContainer: LinearLayout
    private lateinit var imagesSection: LinearLayout
    private lateinit var categorySection: LinearLayout
    private lateinit var partNumberSection: LinearLayout
    private lateinit var vehicleSection: LinearLayout
    private lateinit var compatibilitySection: LinearLayout
    private lateinit var attributesSection: LinearLayout
    private lateinit var pricingSection: LinearLayout
    private lateinit var submitButton: Button
    private lateinit var loadingIndicator: ProgressBar
    private lateinit var statusText: TextView

    override fun onCreateView(
            inflater: LayoutInflater,
            container: ViewGroup?,
            savedInstanceState: Bundle?
    ): View? {
        return createLayout()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupClickListeners()
        observeViewModel()
    }

    private fun createLayout(): ScrollView {
        val scrollView = ScrollView(requireContext())

        val mainLayout =
                LinearLayout(requireContext()).apply {
                    orientation = LinearLayout.VERTICAL
                    setPadding(16, 16, 16, 16)
                }

        // Instructions
        instructionText =
                TextView(requireContext()).apply {
                    text = "Review your part information and submit to create the part"
                    textSize = 16f
                    setPadding(0, 0, 0, 16)
                }

        // Review container
        reviewContainer =
                LinearLayout(requireContext()).apply {
                    orientation = LinearLayout.VERTICAL
                    setBackgroundResource(android.R.drawable.dialog_frame)
                    setPadding(16, 16, 16, 16)
                }

        // Create review sections
        imagesSection = createReviewSection("Images")
        categorySection = createReviewSection("Category")
        partNumberSection = createReviewSection("Part Number")
        vehicleSection = createReviewSection("Vehicle")
        compatibilitySection = createReviewSection("Compatibility")
        attributesSection = createReviewSection("Attributes")
        pricingSection = createReviewSection("Pricing & Stock")

        reviewContainer.addView(imagesSection)
        reviewContainer.addView(categorySection)
        reviewContainer.addView(partNumberSection)
        reviewContainer.addView(vehicleSection)
        reviewContainer.addView(compatibilitySection)
        reviewContainer.addView(attributesSection)
        reviewContainer.addView(pricingSection)

        // Status and loading
        statusText =
                TextView(requireContext()).apply {
                    text = ""
                    textSize = 14f
                    setPadding(0, 16, 0, 8)
                    visibility = View.GONE
                }

        loadingIndicator = ProgressBar(requireContext()).apply { visibility = View.GONE }

        // Submit button
        submitButton =
                Button(requireContext()).apply {
                    text = "Create Part"
                    textSize = 18f
                    setPadding(0, 16, 0, 0)
                    layoutParams =
                            LinearLayout.LayoutParams(
                                            LinearLayout.LayoutParams.MATCH_PARENT,
                                            LinearLayout.LayoutParams.WRAP_CONTENT
                                    )
                                    .apply { topMargin = 24 }
                }

        // Add all views
        mainLayout.addView(instructionText)
        mainLayout.addView(reviewContainer)
        mainLayout.addView(statusText)
        mainLayout.addView(loadingIndicator)
        mainLayout.addView(submitButton)

        scrollView.addView(mainLayout)
        return scrollView
    }

    private fun createReviewSection(title: String): LinearLayout {
        val section =
                LinearLayout(requireContext()).apply {
                    orientation = LinearLayout.VERTICAL
                    setPadding(0, 0, 0, 16)
                }

        val titleText =
                TextView(requireContext()).apply {
                    text = title
                    textSize = 16f
                    setTypeface(null, android.graphics.Typeface.BOLD)
                    setPadding(0, 0, 0, 8)
                }

        val contentText =
                TextView(requireContext()).apply {
                    textSize = 14f
                    setPadding(16, 0, 0, 0)
                }

        section.addView(titleText)
        section.addView(contentText)

        return section
    }

    private fun setupClickListeners() {
        submitButton.setOnClickListener {
            // Trigger submission via ViewModel; Activity observes submitSuccess and navigates
            viewModel.submitPart()
        }
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.formState.collect { formState -> updateReviewSections(formState) }
        }

        lifecycleScope.launch {
            viewModel.uiState.collect { uiState ->
                if (uiState.isSubmitting) {
                    showSubmissionProgress()
                } else {
                    hideSubmissionProgress()
                }

                if (uiState.submitSuccess) {
                    showSubmissionSuccess()
                }
            }
        }
    }

    private fun updateReviewSections(formState: PartFormValues) {
        // Update Images section
        val imagesContent = imagesSection.getChildAt(1) as TextView
        imagesContent.text =
                if (formState.images.isNotEmpty()) {
                    "${formState.images.size} image(s) uploaded\nMain image: ${formState.images.find { it.isMain }?.url?.substringAfterLast("/") ?: "None"}"
                } else {
                    "No images uploaded"
                }

        // Update Category section
        val categoryContent = categorySection.getChildAt(1) as TextView
        categoryContent.text =
                if (formState.selectedCategory.isNotBlank()) {
                    "${formState.selectedCategory}\n${if (formState.requirePartNumber) "Requires part number" else "Manual vehicle selection"}"
                } else {
                    "No category selected"
                }

        // Update Part Number section
        val partNumberContent = partNumberSection.getChildAt(1) as TextView
        if (formState.requirePartNumber) {
            partNumberSection.visibility = View.VISIBLE
            partNumberContent.text =
                    if (formState.partNumber.isNotBlank()) {
                        "Part Number: ${formState.partNumber}\nCompatibility: ${if (formState.compatibilityData != null) "Checked" else "Not checked"}"
                    } else {
                        "No part number entered"
                    }
        } else {
            partNumberSection.visibility = View.GONE
        }

        // Update Vehicle section
        val vehicleContent = vehicleSection.getChildAt(1) as TextView
        if (!formState.requirePartNumber) {
            vehicleSection.visibility = View.VISIBLE
            vehicleContent.text =
                    if (formState.trimId.isNotBlank()) {
                        "${formState.modelName} ${formState.generationName} ${formState.generationYears}\n${formState.variationName} ${formState.trimName}"
                    } else {
                        "No vehicle selected"
                    }
        } else {
            vehicleSection.visibility = View.GONE
        }

        // Update Compatibility section
        val compatibilityContent = compatibilitySection.getChildAt(1) as TextView
        if (formState.compatibilityData != null) {
            compatibilitySection.visibility = View.VISIBLE
            val data = formState.compatibilityData
            compatibilityContent.text = buildString {
                append("Compatible Parts: ${data.compatiblePartNumbers.size}\n")
                append("Engines: ${data.engineCompatibility.size}\n")
                append("Vehicles: ${data.vehicleCompatibility.size}\n")
                if (formState.additionalEngineCodes.isNotEmpty()) {
                    append(
                            "Additional Engines: ${formState.additionalEngineCodes.joinToString(", ")}"
                    )
                }
            }
        } else {
            compatibilitySection.visibility = View.GONE
        }

        // Update Attributes section
        val attributesContent = attributesSection.getChildAt(1) as TextView
        if (formState.categoryAttributes.isNotEmpty()) {
            attributesSection.visibility = View.VISIBLE
            attributesContent.text = "${formState.categoryAttributes.size} attribute(s) filled"
        } else {
            attributesSection.visibility = View.GONE
        }

        // Update Pricing section
        val pricingContent = pricingSection.getChildAt(1) as TextView
        pricingContent.text = buildString {
            append("Condition: ${formState.condition.name}\n")

            when (formState.condition) {
                com.autoflow.android.domain.models.PartCondition.NEW -> {
                    append("Stock: ${formState.newStock ?: formState.stock ?: 0}\n")
                    append(
                            "Price: KES ${String.format("%.2f", formState.newPrice ?: formState.price ?: 0.0)}"
                    )
                    formState.newDiscountPrice?.let { discount ->
                        append("\nDiscount: KES ${String.format("%.2f", discount)}")
                    }
                }
                com.autoflow.android.domain.models.PartCondition.USED -> {
                    append("Stock: ${formState.usedStock ?: formState.stock ?: 0}\n")
                    append(
                            "Price: KES ${String.format("%.2f", formState.usedPrice ?: formState.price ?: 0.0)}"
                    )
                    formState.usedDiscountPrice?.let { discount ->
                        append("\nDiscount: KES ${String.format("%.2f", discount)}")
                    }
                }
                com.autoflow.android.domain.models.PartCondition.BOTH -> {
                    append(
                            "NEW - Stock: ${formState.newStock ?: 0}, Price: KES ${String.format("%.2f", formState.newPrice ?: 0.0)}\n"
                    )
                    append(
                            "USED - Stock: ${formState.usedStock ?: 0}, Price: KES ${String.format("%.2f", formState.usedPrice ?: 0.0)}"
                    )
                }
            }
        }
    }

    private fun showSubmissionProgress() {
        statusText.text = "Creating part..."
        statusText.visibility = View.VISIBLE
        loadingIndicator.visibility = View.VISIBLE
        submitButton.isEnabled = false
    }

    private fun hideSubmissionProgress() {
        loadingIndicator.visibility = View.GONE
        submitButton.isEnabled = true
    }

    private fun showSubmissionSuccess() {
        statusText.text = "Part created successfully! Proceeding to storage location..."
        statusText.visibility = View.VISIBLE
    }

    /** Validate all form data before submission */
    fun validateForSubmission(): Boolean {
        val formState = viewModel.formState.value

        // Check images
        if (formState.images.isEmpty()) {
            Toast.makeText(context, "At least one image is required", Toast.LENGTH_SHORT).show()
            return false
        }

        // Check category
        if (formState.categoryId.isBlank()) {
            Toast.makeText(context, "Category selection is required", Toast.LENGTH_SHORT).show()
            return false
        }

        // Check part number for PN-required categories
        if (formState.requirePartNumber) {
            if (formState.partNumber.isBlank()) {
                Toast.makeText(
                                context,
                                "Part number is required for this category",
                                Toast.LENGTH_SHORT
                        )
                        .show()
                return false
            }

            if (formState.compatibilityData == null) {
                Toast.makeText(context, "Compatibility check is required", Toast.LENGTH_SHORT)
                        .show()
                return false
            }
        } else {
            // Check vehicle selection for non-PN categories
            if (formState.trimId.isBlank()) {
                Toast.makeText(context, "Vehicle selection is required", Toast.LENGTH_SHORT).show()
                return false
            }
        }

        // Check pricing
        when (formState.condition) {
            com.autoflow.android.domain.models.PartCondition.NEW -> {
                val stock = formState.newStock ?: formState.stock ?: 0
                val price = formState.newPrice ?: formState.price ?: 0.0

                if (stock < 1) {
                    Toast.makeText(context, "Stock quantity must be at least 1", Toast.LENGTH_SHORT)
                            .show()
                    return false
                }

                if (price <= 0) {
                    Toast.makeText(context, "Price must be greater than 0", Toast.LENGTH_SHORT)
                            .show()
                    return false
                }
            }
            com.autoflow.android.domain.models.PartCondition.USED -> {
                val stock = formState.usedStock ?: formState.stock ?: 0
                val price = formState.usedPrice ?: formState.price ?: 0.0

                if (stock < 1) {
                    Toast.makeText(context, "Stock quantity must be at least 1", Toast.LENGTH_SHORT)
                            .show()
                    return false
                }

                if (price <= 0) {
                    Toast.makeText(context, "Price must be greater than 0", Toast.LENGTH_SHORT)
                            .show()
                    return false
                }
            }
            com.autoflow.android.domain.models.PartCondition.BOTH -> {
                val newStock = formState.newStock ?: 0
                val newPrice = formState.newPrice ?: 0.0
                val usedStock = formState.usedStock ?: 0
                val usedPrice = formState.usedPrice ?: 0.0

                if (newStock < 1 || usedStock < 1) {
                    Toast.makeText(
                                    context,
                                    "Both new and used stock must be at least 1",
                                    Toast.LENGTH_SHORT
                            )
                            .show()
                    return false
                }

                if (newPrice <= 0 || usedPrice <= 0) {
                    Toast.makeText(
                                    context,
                                    "Both new and used prices must be greater than 0",
                                    Toast.LENGTH_SHORT
                            )
                            .show()
                    return false
                }
            }
        }

        return true
    }
}

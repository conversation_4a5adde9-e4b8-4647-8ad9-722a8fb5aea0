<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    tools:context=".ui.parts.CameraCaptureActivity">

    <!-- Camera Preview -->
    <androidx.camera.view.PreviewView
        android:id="@+id/viewFinder"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/captureControls"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Capture Controls -->
    <LinearLayout
        android:id="@+id/captureControls"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:orientation="horizontal"
        android:gravity="center"
        android:background="@android:color/black"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Back Button -->
        <Button
            android:id="@+id/backButton"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_marginEnd="32dp"
            android:background="@drawable/circle_button_background"
            android:text="←"
            android:textColor="@android:color/white"
            android:textSize="24sp" />

        <!-- Capture Button -->
        <Button
            android:id="@+id/captureButton"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:background="@drawable/capture_button_background"
            android:layout_marginHorizontal="16dp" />

        <!-- Gallery Button (placeholder for future) -->
        <Button
            android:id="@+id/galleryButton"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_marginStart="32dp"
            android:background="@drawable/circle_button_background"
            android:text="📷"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Top Controls -->
    <LinearLayout
        android:id="@+id/topControls"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="16dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Close Button -->
        <Button
            android:id="@+id/closeButton"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/circle_button_background"
            android:text="✕"
            android:textColor="@android:color/white"
            android:textSize="18sp" />

        <!-- Title -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Take Photo"
            android:textColor="@android:color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="center" />

        <!-- Flash Toggle (placeholder for future) -->
        <Button
            android:id="@+id/flashButton"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/circle_button_background"
            android:text="⚡"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:visibility="gone" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

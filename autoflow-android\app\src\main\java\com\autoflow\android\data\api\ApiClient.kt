package com.autoflow.android.data.api

import com.autoflow.android.BuildConfig
import com.autoflow.android.core.auth.AuthManager
import com.squareup.moshi.Moshi
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import java.util.concurrent.TimeUnit
import okhttp3.Authenticator
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.moshi.MoshiConverterFactory

/** Central Retrofit API client with OkHttp interceptors and Moshi */
object ApiClient {
    private const val TIMEOUT_SECONDS = 30L

    @Volatile private var retrofit: Retrofit? = null

    fun getRetrofit(authManager: AuthManager, useStaging: Boolean = false): Retrofit {
        val baseUrl = if (useStaging) BuildConfig.STAGING_BASE_URL else BuildConfig.BASE_URL
        // Always build a fresh Retrofit when a different AuthManager instance is passed
        return buildRetrofit(baseUrl, authManager)
    }

    private fun buildRetrofit(baseUrl: String, authManager: AuthManager): Retrofit {
        val moshi = Moshi.Builder().addLast(KotlinJsonAdapterFactory()).build()

        val logging =
                HttpLoggingInterceptor().apply {
                    level =
                            if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.BODY
                            else HttpLoggingInterceptor.Level.NONE
                }

        val authInterceptor = Interceptor { chain ->
            android.util.Log.e("ApiClient", "=== AUTH INTERCEPTOR CALLED ===")
            try {
                val original = chain.request()
                android.util.Log.d("ApiClient", "Auth interceptor processing: ${original.url}")
                val builder: Request.Builder =
                        original.newBuilder().header("Accept", "application/json")

                val token =
                        try {
                            authManager.getAuthToken()
                        } catch (e: Exception) {
                            android.util.Log.e("ApiClient", "Error getting auth token", e)
                            null
                        }

                android.util.Log.d(
                        "ApiClient",
                        "Token from authManager: ${if (token != null) "PRESENT(${token.take(20)}...)" else "NULL"}"
                )
                if (token != null) {
                    android.util.Log.d(
                            "ApiClient",
                            "Adding Authorization header for ${original.url}"
                    )
                    builder.header("Authorization", "Bearer $token")
                } else {
                    android.util.Log.w("ApiClient", "No auth token available for ${original.url}")
                }

                val request = builder.build()
                android.util.Log.d("ApiClient", "Final request headers: ${request.headers}")
                chain.proceed(request)
            } catch (e: Exception) {
                android.util.Log.e("ApiClient", "Auth interceptor error", e)
                chain.proceed(chain.request())
            }
        }

        val authenticator = Authenticator { _, response ->
            android.util.Log.d("ApiClient", "Authenticator triggered for ${response.request.url}")

            // Avoid infinite loops - only avoid if the prior response was also a 401
            val priorResponse = response.priorResponse
            if (priorResponse != null && priorResponse.code == 401) {
                android.util.Log.d("ApiClient", "Authenticator: avoiding retry loop (prior 401)")
                return@Authenticator null
            } else if (priorResponse != null) {
                android.util.Log.d(
                        "ApiClient",
                        "Authenticator: prior response was ${priorResponse.code}, continuing"
                )
            }

            // Try to refresh token synchronously
            val newToken =
                    try {
                        android.util.Log.d("ApiClient", "Authenticator: attempting token refresh")
                        authManager.refreshTokenBlocking()
                    } catch (e: Exception) {
                        android.util.Log.e("ApiClient", "Authenticator: refresh failed", e)
                        null
                    }

            if (newToken.isNullOrBlank()) {
                android.util.Log.w("ApiClient", "Authenticator: no new token available")
                return@Authenticator null
            }

            android.util.Log.d("ApiClient", "Authenticator: retrying with new token")
            response.request.newBuilder().header("Authorization", "Bearer $newToken").build()
        }

        val client =
                OkHttpClient.Builder()
                        .connectTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
                        .readTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
                        .writeTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
                        .authenticator(authenticator)
                        .addInterceptor(authInterceptor)
                        .addInterceptor(logging)
                        .build()

        val base = if (baseUrl.endsWith('/')) baseUrl else "$baseUrl/"

        return Retrofit.Builder()
                .baseUrl(base)
                // Be lenient to handle slight variations in JSON from Next.js routes
                .addConverterFactory(MoshiConverterFactory.create(moshi).asLenient())
                .client(client)
                .build()
    }
}

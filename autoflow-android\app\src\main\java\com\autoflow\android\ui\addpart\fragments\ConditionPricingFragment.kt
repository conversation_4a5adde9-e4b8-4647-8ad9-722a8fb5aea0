package com.autoflow.android.ui.addpart.fragments

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import com.autoflow.android.domain.models.PartCondition
import com.autoflow.android.ui.addpart.AddPartViewModel
import kotlinx.coroutines.launch

/**
 * Condition & Pricing Fragment - Step 5 of Add Part flow Handles condition selection
 * (NEW/USED/BOTH) and pricing/stock Mirrors web app's dual condition pricing system
 */
class ConditionPricingFragment : Fragment() {

    companion object {
        private const val TAG = "ConditionPricingFragment"
    }

    private val viewModel: AddPartViewModel by activityViewModels()

    // UI Components
    private lateinit var instructionText: TextView
    private lateinit var conditionRadioGroup: RadioGroup
    private lateinit var newConditionRadio: RadioButton
    private lateinit var usedConditionRadio: RadioButton
    private lateinit var bothConditionRadio: RadioButton

    // New condition fields
    private lateinit var newConditionSection: LinearLayout
    private lateinit var newStockInput: EditText
    private lateinit var newPriceInput: EditText
    private lateinit var newDiscountPriceInput: EditText

    // Used condition fields
    private lateinit var usedConditionSection: LinearLayout
    private lateinit var usedStockInput: EditText
    private lateinit var usedPriceInput: EditText
    private lateinit var usedDiscountPriceInput: EditText

    // Summary
    private lateinit var pricingSummary: TextView

    override fun onCreateView(
            inflater: LayoutInflater,
            container: ViewGroup?,
            savedInstanceState: Bundle?
    ): View? {
        return createLayout()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupClickListeners()
        observeViewModel()
    }

    private fun createLayout(): ScrollView {
        val scrollView = ScrollView(requireContext())

        val mainLayout =
                LinearLayout(requireContext()).apply {
                    orientation = LinearLayout.VERTICAL
                    setPadding(16, 16, 16, 16)
                }

        // Instructions
        instructionText =
                TextView(requireContext()).apply {
                    text = "Set the condition, stock quantity, and pricing for this part"
                    textSize = 16f
                    setPadding(0, 0, 0, 16)
                }

        // Condition selection
        val conditionLabel =
                TextView(requireContext()).apply {
                    text = "Condition *"
                    textSize = 16f
                    setTypeface(null, android.graphics.Typeface.BOLD)
                    setPadding(0, 0, 0, 8)
                }

        conditionRadioGroup =
                RadioGroup(requireContext()).apply { orientation = RadioGroup.VERTICAL }

        newConditionRadio =
                RadioButton(requireContext()).apply {
                    text = "New"
                    id = View.generateViewId()
                }

        usedConditionRadio =
                RadioButton(requireContext()).apply {
                    text = "Used"
                    id = View.generateViewId()
                }

        bothConditionRadio =
                RadioButton(requireContext()).apply {
                    text = "Both (New and Used)"
                    id = View.generateViewId()
                }

        conditionRadioGroup.addView(newConditionRadio)
        conditionRadioGroup.addView(usedConditionRadio)
        conditionRadioGroup.addView(bothConditionRadio)

        // New condition section
        newConditionSection = createConditionSection("New Condition", "new")

        // Used condition section
        usedConditionSection = createConditionSection("Used Condition", "used")

        // Pricing summary
        pricingSummary =
                TextView(requireContext()).apply {
                    text = ""
                    textSize = 14f
                    setPadding(16, 16, 16, 16)
                    setBackgroundResource(android.R.drawable.dialog_frame)
                    visibility = View.GONE
                }

        // Add all views
        mainLayout.addView(instructionText)
        mainLayout.addView(conditionLabel)
        mainLayout.addView(conditionRadioGroup)
        mainLayout.addView(newConditionSection)
        mainLayout.addView(usedConditionSection)
        mainLayout.addView(pricingSummary)

        scrollView.addView(mainLayout)
        return scrollView
    }

    private fun createConditionSection(title: String, prefix: String): LinearLayout {
        val section =
                LinearLayout(requireContext()).apply {
                    orientation = LinearLayout.VERTICAL
                    setPadding(0, 16, 0, 16)
                    visibility = View.GONE
                }

        val titleText =
                TextView(requireContext()).apply {
                    text = title
                    textSize = 18f
                    setTypeface(null, android.graphics.Typeface.BOLD)
                    setPadding(0, 0, 0, 16)
                }
        section.addView(titleText)

        // Stock input
        val stockLabel =
                TextView(requireContext()).apply {
                    text = "Stock Quantity *"
                    textSize = 14f
                    setTypeface(null, android.graphics.Typeface.BOLD)
                    setPadding(0, 0, 0, 4)
                }

        val stockInput =
                EditText(requireContext()).apply {
                    hint = "Enter stock quantity"
                    inputType = android.text.InputType.TYPE_CLASS_NUMBER
                    setPadding(16, 16, 16, 16)
                }

        // Price input
        val priceLabel =
                TextView(requireContext()).apply {
                    text = "Price (KES) *"
                    textSize = 14f
                    setTypeface(null, android.graphics.Typeface.BOLD)
                    setPadding(0, 8, 0, 4)
                }

        val priceInput =
                EditText(requireContext()).apply {
                    hint = "Enter price in KES"
                    inputType =
                            android.text.InputType.TYPE_CLASS_NUMBER or
                                    android.text.InputType.TYPE_NUMBER_FLAG_DECIMAL
                    setPadding(16, 16, 16, 16)
                }

        // Discount price input
        val discountLabel =
                TextView(requireContext()).apply {
                    text = "Discount Price (KES) - Optional"
                    textSize = 14f
                    setTypeface(null, android.graphics.Typeface.BOLD)
                    setPadding(0, 8, 0, 4)
                }

        val discountInput =
                EditText(requireContext()).apply {
                    hint = "Enter discount price (optional)"
                    inputType =
                            android.text.InputType.TYPE_CLASS_NUMBER or
                                    android.text.InputType.TYPE_NUMBER_FLAG_DECIMAL
                    setPadding(16, 16, 16, 16)
                }

        section.addView(stockLabel)
        section.addView(stockInput)
        section.addView(priceLabel)
        section.addView(priceInput)
        section.addView(discountLabel)
        section.addView(discountInput)

        // Store references based on prefix
        if (prefix == "new") {
            newStockInput = stockInput
            newPriceInput = priceInput
            newDiscountPriceInput = discountInput
        } else {
            usedStockInput = stockInput
            usedPriceInput = priceInput
            usedDiscountPriceInput = discountInput
        }

        // Add text watchers for real-time updates
        stockInput.addTextChangedListener(createTextWatcher())
        priceInput.addTextChangedListener(createTextWatcher())
        discountInput.addTextChangedListener(createTextWatcher())

        return section
    }

    private fun createTextWatcher(): TextWatcher {
        return object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                updatePricingSummary()
                updateFormState()
            }
        }
    }

    private fun setupClickListeners() {
        conditionRadioGroup.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                newConditionRadio.id -> {
                    showConditionSections(showNew = true, showUsed = false)
                    updateFormCondition(PartCondition.NEW)
                }
                usedConditionRadio.id -> {
                    showConditionSections(showNew = false, showUsed = true)
                    updateFormCondition(PartCondition.USED)
                }
                bothConditionRadio.id -> {
                    showConditionSections(showNew = true, showUsed = true)
                    updateFormCondition(PartCondition.BOTH)
                }
            }
            updatePricingSummary()
        }

        // Set default selection
        newConditionRadio.isChecked = true
        showConditionSections(showNew = true, showUsed = false)
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.formState.collect { formState ->
                // Update UI based on form state if needed
            }
        }
    }

    private fun showConditionSections(showNew: Boolean, showUsed: Boolean) {
        newConditionSection.visibility = if (showNew) View.VISIBLE else View.GONE
        usedConditionSection.visibility = if (showUsed) View.VISIBLE else View.GONE
    }

    private fun updateFormCondition(condition: PartCondition) {
        viewModel.updateCondition(condition)
    }

    private fun updateFormState() {
        // Update ViewModel with current pricing values
        val newStock = newStockInput.text.toString().toIntOrNull()
        val newPrice = newPriceInput.text.toString().toDoubleOrNull()
        val newDiscountPrice = newDiscountPriceInput.text.toString().toDoubleOrNull()

        val usedStock = usedStockInput.text.toString().toIntOrNull()
        val usedPrice = usedPriceInput.text.toString().toDoubleOrNull()
        val usedDiscountPrice = usedDiscountPriceInput.text.toString().toDoubleOrNull()

        viewModel.updatePricing(
                newStock,
                newPrice,
                newDiscountPrice,
                usedStock,
                usedPrice,
                usedDiscountPrice
        )
    }

    private fun updatePricingSummary() {
        val summary = buildString {
            append("Pricing Summary:\n\n")

            if (newConditionSection.visibility == View.VISIBLE) {
                append("NEW CONDITION:\n")
                val stock = newStockInput.text.toString().toIntOrNull() ?: 0
                val price = newPriceInput.text.toString().toDoubleOrNull() ?: 0.0
                val discount = newDiscountPriceInput.text.toString().toDoubleOrNull()

                append("Stock: $stock units\n")
                append("Price: KES ${String.format("%.2f", price)}\n")
                if (discount != null && discount > 0) {
                    append("Discount Price: KES ${String.format("%.2f", discount)}\n")
                }
                append("\n")
            }

            if (usedConditionSection.visibility == View.VISIBLE) {
                append("USED CONDITION:\n")
                val stock = usedStockInput.text.toString().toIntOrNull() ?: 0
                val price = usedPriceInput.text.toString().toDoubleOrNull() ?: 0.0
                val discount = usedDiscountPriceInput.text.toString().toDoubleOrNull()

                append("Stock: $stock units\n")
                append("Price: KES ${String.format("%.2f", price)}\n")
                if (discount != null && discount > 0) {
                    append("Discount Price: KES ${String.format("%.2f", discount)}\n")
                }
            }
        }

        if (summary.isNotBlank()) {
            pricingSummary.text = summary
            pricingSummary.visibility = View.VISIBLE
        } else {
            pricingSummary.visibility = View.GONE
        }
    }

    /** Validate pricing fields */
    fun validatePricing(): Boolean {
        if (newConditionSection.visibility == View.VISIBLE) {
            val stock = newStockInput.text.toString().toIntOrNull()
            val price = newPriceInput.text.toString().toDoubleOrNull()

            if (stock == null || stock < 1) {
                Toast.makeText(
                                context,
                                "New condition stock must be at least 1",
                                Toast.LENGTH_SHORT
                        )
                        .show()
                return false
            }

            if (price == null || price <= 0) {
                Toast.makeText(
                                context,
                                "New condition price must be greater than 0",
                                Toast.LENGTH_SHORT
                        )
                        .show()
                return false
            }
        }

        if (usedConditionSection.visibility == View.VISIBLE) {
            val stock = usedStockInput.text.toString().toIntOrNull()
            val price = usedPriceInput.text.toString().toDoubleOrNull()

            if (stock == null || stock < 1) {
                Toast.makeText(
                                context,
                                "Used condition stock must be at least 1",
                                Toast.LENGTH_SHORT
                        )
                        .show()
                return false
            }

            if (price == null || price <= 0) {
                Toast.makeText(
                                context,
                                "Used condition price must be greater than 0",
                                Toast.LENGTH_SHORT
                        )
                        .show()
                return false
            }
        }

        return true
    }
}

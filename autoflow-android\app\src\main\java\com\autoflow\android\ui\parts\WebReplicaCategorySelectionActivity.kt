package com.autoflow.android.ui.parts

import android.content.Intent
import android.graphics.Typeface
import android.net.Uri
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.View
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.autoflow.android.data.api.HierarchicalCategory
import com.autoflow.android.data.api.PartsApiService
import com.autoflow.android.data.repository.AuthRepository
import kotlinx.coroutines.launch

/**
 * Exact replica of web version's NestedSelect category implementation Mirrors the web's
 * buildCategoryTree and NestedSelect component behavior
 */
class WebReplicaCategorySelectionActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "WebReplicaCategory"
    }

    private lateinit var authRepository: AuthRepository
    private lateinit var partsApiService: PartsApiService

    // UI Elements - exactly like web version's NestedSelect
    private lateinit var imageView: ImageView
    private lateinit var nestedSelectContainer: LinearLayout
    private lateinit var nestedSelectTrigger: LinearLayout
    private lateinit var selectedValueText: TextView
    private lateinit var chevronIcon: ImageView
    private lateinit var nestedSelectContent: LinearLayout
    private lateinit var searchInput: EditText
    private lateinit var itemsList: LinearLayout
    private lateinit var statusText: TextView
    private lateinit var continueButton: Button
    private lateinit var backButton: Button

    private var croppedImageUri: Uri? = null
    private var selectedValue: String? = null
    private var selectedCategory: HierarchicalCategory? = null
    private val allCategories = mutableListOf<HierarchicalCategory>()
    private var isDropdownOpen = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        authRepository = AuthRepository(this)
        partsApiService = PartsApiService(this)

        createWebReplicaUI()
        loadCroppedImage()
        loadCategoriesFromDatabase()
    }

    private fun createWebReplicaUI() {
        Log.d(TAG, "Creating web replica UI - exact NestedSelect implementation")

        // Create main layout
        val mainLayout =
                LinearLayout(this).apply {
                    orientation = LinearLayout.VERTICAL
                    setPadding(32, 32, 32, 32)
                }

        // Create header
        val headerLayout = LinearLayout(this).apply { orientation = LinearLayout.HORIZONTAL }

        backButton =
                Button(this).apply {
                    text = "Back"
                    setOnClickListener { finish() }
                }

        val titleText =
                TextView(this).apply {
                    text = "Select Category"
                    textSize = 20f
                    layoutParams =
                            LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
                }

        continueButton =
                Button(this).apply {
                    text = "Continue"
                    isEnabled = false
                    setOnClickListener { proceedToNextStep() }
                }

        headerLayout.addView(backButton)
        headerLayout.addView(titleText)
        headerLayout.addView(continueButton)

        // Create status text
        statusText =
                TextView(this).apply {
                    text = "Loading categories..."
                    textSize = 14f
                    setPadding(0, 16, 0, 16)
                }

        // Create image view
        imageView =
                ImageView(this).apply {
                    layoutParams = LinearLayout.LayoutParams(200, 200)
                    scaleType = ImageView.ScaleType.CENTER_CROP
                }

        // Create NestedSelect component - exact replica of web version
        createNestedSelectComponent()

        // Add all views to main layout
        mainLayout.addView(headerLayout)
        mainLayout.addView(statusText)
        mainLayout.addView(imageView)
        mainLayout.addView(nestedSelectContainer)

        // Set as content view
        setContentView(mainLayout)

        Log.d(TAG, "Web replica UI created successfully")
    }

    private fun createNestedSelectComponent() {
        // Main NestedSelect container
        nestedSelectContainer =
                LinearLayout(this).apply {
                    orientation = LinearLayout.VERTICAL
                    setPadding(0, 24, 0, 0)
                }

        // Category label
        val categoryLabel =
                TextView(this).apply {
                    text = "Category"
                    textSize = 16f
                    setPadding(0, 0, 0, 8)
                    setTypeface(null, Typeface.BOLD)
                }

        // NestedSelectTrigger - exact replica of web component
        nestedSelectTrigger =
                LinearLayout(this).apply {
                    orientation = LinearLayout.HORIZONTAL
                    layoutParams =
                            LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, 120)
                    setPadding(16, 16, 16, 16)
                    setBackgroundResource(android.R.drawable.edit_text)
                    setOnClickListener { toggleNestedSelect() }
                }

        selectedValueText =
                TextView(this).apply {
                    text = "Select a category"
                    textSize = 16f
                    layoutParams =
                            LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
                }

        chevronIcon =
                ImageView(this).apply {
                    setImageResource(android.R.drawable.arrow_down_float)
                    layoutParams = LinearLayout.LayoutParams(48, 48)
                }

        nestedSelectTrigger.addView(selectedValueText)
        nestedSelectTrigger.addView(chevronIcon)

        // NestedSelectContent - exact replica of web component
        nestedSelectContent =
                LinearLayout(this).apply {
                    orientation = LinearLayout.VERTICAL
                    visibility = View.GONE
                    setBackgroundResource(android.R.drawable.dialog_frame)
                    setPadding(8, 8, 8, 8)
                }

        // Search input - exact replica of web version
        searchInput =
                EditText(this).apply {
                    hint = "Search..."
                    setPadding(16, 16, 16, 16)
                    addTextChangedListener(
                            object : TextWatcher {
                                override fun beforeTextChanged(
                                        s: CharSequence?,
                                        start: Int,
                                        count: Int,
                                        after: Int
                                ) {}
                                override fun onTextChanged(
                                        s: CharSequence?,
                                        start: Int,
                                        before: Int,
                                        count: Int
                                ) {}
                                override fun afterTextChanged(s: Editable?) {
                                    filterItems(s.toString())
                                }
                            }
                    )
                }

        // Items list container - where SelectItemComponent items are rendered
        itemsList = LinearLayout(this).apply { orientation = LinearLayout.VERTICAL }

        nestedSelectContent.addView(searchInput)
        nestedSelectContent.addView(itemsList)

        nestedSelectContainer.addView(categoryLabel)
        nestedSelectContainer.addView(nestedSelectTrigger)
        nestedSelectContainer.addView(nestedSelectContent)
    }

    private fun toggleNestedSelect() {
        isDropdownOpen = !isDropdownOpen
        nestedSelectContent.visibility = if (isDropdownOpen) View.VISIBLE else View.GONE

        // Rotate chevron icon - exact web behavior
        chevronIcon.rotation = if (isDropdownOpen) 180f else 0f

        Log.d(TAG, "NestedSelect toggled: $isDropdownOpen")
    }

    private fun loadCroppedImage() {
        val imageUriString = intent.getStringExtra("cropped_image_uri")
        if (imageUriString != null) {
            croppedImageUri = Uri.parse(imageUriString)
            imageView.setImageURI(croppedImageUri)
            Log.d(TAG, "Image loaded successfully")
        } else {
            Toast.makeText(this, "Error loading image", Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    private fun loadCategoriesFromDatabase() {
        Log.d(TAG, "Loading categories from database - exactly like web version")

        lifecycleScope.launch {
            try {
                statusText.text = "Loading categories..."

                // Fetch hierarchical categories from API - exactly like web version
                val result = partsApiService.fetchHierarchicalCategories()

                if (result.isSuccess) {
                    val categories = result.getOrNull() ?: emptyList()
                    Log.d(
                            TAG,
                            "Successfully loaded ${categories.size} hierarchical categories from database"
                    )

                    allCategories.clear()
                    allCategories.addAll(categories)

                    // Display categories - exactly like web version
                    displayCategories()

                    statusText.text = "Select a category for your part"
                } else {
                    Log.e(
                            TAG,
                            "Failed to load categories from database: ${result.exceptionOrNull()}"
                    )
                    statusText.text = "Error loading categories"
                    Toast.makeText(
                                    this@WebReplicaCategorySelectionActivity,
                                    "Failed to load categories from database",
                                    Toast.LENGTH_LONG
                            )
                            .show()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error loading categories from database", e)
                statusText.text = "Error loading categories"
                Toast.makeText(
                                this@WebReplicaCategorySelectionActivity,
                                "Error loading categories: ${e.message}",
                                Toast.LENGTH_LONG
                        )
                        .show()
            }
        }
    }

    private fun displayCategories(searchQuery: String = "") {
        itemsList.removeAllViews()

        val filteredCategories =
                if (searchQuery.isEmpty()) {
                    allCategories
                } else {
                    filterItems(allCategories, searchQuery)
                }

        // Render each category - exactly like web's SelectItemComponent
        filteredCategories.forEach { category -> renderSelectItemComponent(category, 0) }

        if (filteredCategories.isEmpty()) {
            val noResultsText =
                    TextView(this).apply {
                        text = "No results found"
                        setPadding(16, 16, 16, 16)
                        textSize = 14f
                    }
            itemsList.addView(noResultsText)
        }
    }

    private fun renderSelectItemComponent(item: HierarchicalCategory, level: Int) {
        // Create SelectItemComponent - exact replica of web version
        val itemContainer =
                LinearLayout(this).apply {
                    orientation = LinearLayout.HORIZONTAL
                    setPadding(level * 24 + 16, 16, 16, 16) // Indentation based on level
                    setOnClickListener { handleSelect(item) }
                }

        // Add chevron for parent categories - exactly like web version
        if (item.hasChildren) {
            val chevron =
                    TextView(this).apply {
                        text = "▶"
                        textSize = 12f
                        setPadding(0, 0, 8, 0)
                    }
            itemContainer.addView(chevron)
        }

        // Category label
        val categoryLabel =
                TextView(this).apply {
                    text = item.label
                    textSize = 16f

                    // Style parent categories differently - exactly like web version
                    if (item.hasChildren) {
                        setTypeface(null, Typeface.BOLD)
                        setTextColor(resources.getColor(android.R.color.darker_gray, null))
                    } else {
                        // Highlight selected category
                        if (selectedValue == item.id.toString()) {
                            setTextColor(resources.getColor(android.R.color.holo_blue_dark, null))
                            setTypeface(null, Typeface.BOLD)
                        }
                    }
                }
        itemContainer.addView(categoryLabel)

        itemsList.addView(itemContainer)

        // Recursively render children - exactly like web version
        item.children.forEach { child -> renderSelectItemComponent(child, level + 1) }
    }

    private fun handleSelect(item: HierarchicalCategory) {
        // Only allow selection of leaf nodes - exactly like web version
        if (!item.hasChildren) {
            selectedValue = item.id.toString()
            selectedCategory = item
            selectedValueText.text = item.label
            continueButton.isEnabled = true

            // Close dropdown - exactly like web version
            isDropdownOpen = false
            nestedSelectContent.visibility = View.GONE
            chevronIcon.rotation = 0f

            statusText.text =
                    "Selected: ${item.label}" +
                            if (item.requirePartNumber) " (Part number required)"
                            else " (Part number optional)"

            Log.d(TAG, "Category selected: ${item.label} (ID: ${item.id})")

            // Refresh display to show selection
            displayCategories()
        } else {
            Log.d(TAG, "Cannot select parent category: ${item.label}")
        }
    }

    private fun filterItems(query: String) {
        displayCategories(query)
    }

    private fun filterItems(
            items: List<HierarchicalCategory>,
            query: String
    ): List<HierarchicalCategory> {
        // Exact replica of web version's filterItems function
        return items
                .filter { item ->
                    val matches = item.label.contains(query, ignoreCase = true)
                    val childMatches =
                            if (item.children.isNotEmpty()) {
                                filterItems(item.children, query).isNotEmpty()
                            } else {
                                false
                            }
                    matches || childMatches
                }
                .map { item ->
                    if (item.children.isNotEmpty()) {
                        // Create new item with filtered children
                        item.copy(children = filterItems(item.children, query))
                    } else {
                        item
                    }
                }
    }

    private fun proceedToNextStep() {
        selectedCategory?.let { category ->
            val intent = Intent(this, AddPartActivity::class.java)
            intent.putExtra("cropped_image_uri", croppedImageUri.toString())
            intent.putExtra("category_id", category.id)
            intent.putExtra("category_name", category.label)
            intent.putExtra("requires_part_number", category.requirePartNumber)
            intent.putExtra("has_attributes", true)
            startActivity(intent)
            finish()
        }
    }

    // Extension function to create copy with modified children
    private fun HierarchicalCategory.copy(
            children: List<HierarchicalCategory>
    ): HierarchicalCategory {
        return HierarchicalCategory(
                id = this.id,
                label = this.label,
                parentId = this.parentId,
                requirePartNumber = this.requirePartNumber,
                isActive = this.isActive,
                level = this.level,
                children = children
        )
    }
}

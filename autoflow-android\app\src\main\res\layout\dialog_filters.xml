<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/dialog_background">

    <!-- Dialog Title -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="24dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Filters"
            android:textColor="@color/brand_black"
            android:textSize="20sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/clearAllFiltersButton"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:text="Clear All"
            android:textColor="@color/brand_red"
            android:background="@drawable/button_text_red"
            android:paddingHorizontal="12dp"
            android:textSize="14sp" />

    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:maxHeight="400dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Categories Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Categories"
                android:textColor="@color/brand_black"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="12dp" />

            <LinearLayout
                android:id="@+id/categoriesContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="24dp">

                <CheckBox
                    android:id="@+id/categoryBrakes"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Brakes"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp" />

                <CheckBox
                    android:id="@+id/categoryEngine"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Engine"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp" />

                <CheckBox
                    android:id="@+id/categoryTransmission"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Transmission"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp" />

                <CheckBox
                    android:id="@+id/categorySuspension"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Suspension"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp" />

                <CheckBox
                    android:id="@+id/categoryElectrical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Electrical"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp" />

            </LinearLayout>

            <!-- Brands Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Brands"
                android:textColor="@color/brand_black"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="12dp" />

            <LinearLayout
                android:id="@+id/brandsContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="24dp">

                <CheckBox
                    android:id="@+id/brandVW"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Volkswagen (VW)"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp" />

                <CheckBox
                    android:id="@+id/brandAudi"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Audi"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp" />

                <CheckBox
                    android:id="@+id/brandBMW"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="BMW"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp" />

                <CheckBox
                    android:id="@+id/brandMercedes"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Mercedes-Benz"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp" />

            </LinearLayout>

            <!-- Price Range Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Price Range"
                android:textColor="@color/brand_black"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="12dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_marginEnd="8dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Min Price"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp"
                        android:layout_marginBottom="4dp" />

                    <EditText
                        android:id="@+id/minPriceInput"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:background="@drawable/input_background"
                        android:hint="$0"
                        android:inputType="numberDecimal"
                        android:paddingHorizontal="12dp"
                        android:textSize="14sp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_marginStart="8dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Max Price"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp"
                        android:layout_marginBottom="4dp" />

                    <EditText
                        android:id="@+id/maxPriceInput"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:background="@drawable/input_background"
                        android:hint="$999"
                        android:inputType="numberDecimal"
                        android:paddingHorizontal="12dp"
                        android:textSize="14sp" />

                </LinearLayout>

            </LinearLayout>

            <!-- Stock Availability -->
            <CheckBox
                android:id="@+id/inStockOnlyCheckbox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="In stock only"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:layout_marginBottom="24dp" />

        </LinearLayout>

    </ScrollView>

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end"
        android:layout_marginTop="16dp">

        <Button
            android:id="@+id/cancelButton"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:text="Cancel"
            android:textColor="@color/text_secondary"
            android:background="@drawable/button_text_secondary"
            android:paddingHorizontal="24dp"
            android:textSize="14sp"
            android:layout_marginEnd="12dp" />

        <Button
            android:id="@+id/applyFiltersButton"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:text="Apply Filters"
            android:textColor="@color/text_on_brand"
            android:background="@drawable/button_filled_teal"
            android:paddingHorizontal="24dp"
            android:textSize="14sp" />

    </LinearLayout>

</LinearLayout>

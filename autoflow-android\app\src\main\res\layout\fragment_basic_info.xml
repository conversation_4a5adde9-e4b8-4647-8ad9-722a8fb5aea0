<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Part Name -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:boxStrokeColor="@color/brand_teal"
            app:hintTextColor="@color/brand_teal">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/partNameInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Part Name *"
                android:inputType="text"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Part Number -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:boxStrokeColor="@color/brand_teal"
            app:hintTextColor="@color/brand_teal">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/partNumberInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Part Number *"
                android:inputType="text"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Title -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:boxStrokeColor="@color/brand_teal"
            app:hintTextColor="@color/brand_teal">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/partTitleInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Title"
                android:inputType="text"
                android:maxLines="2" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Description -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:boxStrokeColor="@color/brand_teal"
            app:hintTextColor="@color/brand_teal">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/partDescriptionInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Description"
                android:inputType="textMultiLine"
                android:maxLines="4"
                android:minLines="2" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Brand and Category Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <!-- Brand -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginEnd="8dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Brand *"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:layout_marginBottom="8dp" />

                <Spinner
                    android:id="@+id/partBrandSpinner"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:background="@drawable/spinner_background" />

            </LinearLayout>

            <!-- Category -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginStart="8dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Category *"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:layout_marginBottom="8dp" />

                <Spinner
                    android:id="@+id/partCategorySpinner"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:background="@drawable/spinner_background" />

            </LinearLayout>

        </LinearLayout>

        <!-- Condition -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Condition"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:layout_marginBottom="8dp" />

        <Spinner
            android:id="@+id/partConditionSpinner"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/spinner_background" />

        <!-- Status -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Status"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:layout_marginBottom="8dp" />

        <Spinner
            android:id="@+id/partStatusSpinner"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/spinner_background" />

    </LinearLayout>

</ScrollView>

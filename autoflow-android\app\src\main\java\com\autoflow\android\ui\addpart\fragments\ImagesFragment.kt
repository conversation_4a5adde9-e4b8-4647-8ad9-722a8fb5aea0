package com.autoflow.android.ui.addpart.fragments

import android.Manifest
import android.app.Activity
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import android.widget.Button
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.autoflow.android.domain.models.PartImage
import com.autoflow.android.ui.addpart.AddPartViewModel
import com.bumptech.glide.Glide
import com.yalantis.ucrop.UCrop
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.*
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * Images Fragment - Step 1 of Add Part flow Handles image capture, gallery selection, cropping, and
 * upload Mirrors web app's image-first approach
 */
class ImagesFragment : Fragment() {

    companion object {
        private const val TAG = "ImagesFragment"
        private const val MAX_IMAGES = 5
    }

    private val viewModel: AddPartViewModel by activityViewModels()

    // UI Components
    private lateinit var instructionText: TextView
    private lateinit var imagesRecyclerView: RecyclerView
    private lateinit var cameraButton: Button
    private lateinit var galleryButton: Button
    private lateinit var uploadProgressBar: ProgressBar
    private lateinit var uploadStatusText: TextView
    private lateinit var previewImageView: ImageView
    private lateinit var uploadOverlay: View

    // Image handling
    private lateinit var imagesAdapter: ImagesAdapter
    private var currentPhotoUri: Uri? = null

    // Activity result launchers
    private val cameraLauncher =
            registerForActivityResult(ActivityResultContracts.TakePicture()) { success ->
                if (success) {
                    currentPhotoUri?.let { uri -> startImageCrop(uri) }
                }
            }

    private val galleryLauncher =
            registerForActivityResult(ActivityResultContracts.GetContent()) { uri ->
                uri?.let { startImageCrop(it) }
            }

    private val cropLauncher =
            registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
                if (result.resultCode == Activity.RESULT_OK) {
                    val croppedUri = UCrop.getOutput(result.data!!)
                    croppedUri?.let { uri -> uploadImage(uri) }
                }
            }

    private val cameraPermissionLauncher =
            registerForActivityResult(ActivityResultContracts.RequestPermission()) { granted ->
                if (granted) {
                    openCamera()
                } else {
                    Toast.makeText(context, "Camera permission required", Toast.LENGTH_SHORT).show()
                }
            }

    override fun onCreateView(
            inflater: LayoutInflater,
            container: ViewGroup?,
            savedInstanceState: Bundle?
    ): View {
        val v = inflater.inflate(com.autoflow.android.R.layout.fragment_images, container, false)
        bindViews(v)
        return v
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupRecycler()
        setupClickListeners()
        val shouldOpenCamera = activity?.intent?.getBooleanExtra("open_camera", false) == true
        if (shouldOpenCamera) {
            activity?.intent?.removeExtra("open_camera")
            checkCameraPermissionAndOpen()
        }
        observeViewModel()
    }

    private fun bindViews(root: View) {
        instructionText = root.findViewById(com.autoflow.android.R.id.text_instructions)
        imagesRecyclerView = root.findViewById(com.autoflow.android.R.id.recycler_thumbnails)
        uploadProgressBar = root.findViewById(com.autoflow.android.R.id.progress_linear)
        uploadStatusText = root.findViewById(com.autoflow.android.R.id.text_status)
        cameraButton = root.findViewById(com.autoflow.android.R.id.btn_camera)
        galleryButton = root.findViewById(com.autoflow.android.R.id.btn_gallery)
        previewImageView = root.findViewById(com.autoflow.android.R.id.image_preview)
        uploadOverlay = root.findViewById(com.autoflow.android.R.id.overlay_upload)
        // Retake: delete current main image if uploaded, then open camera
        root.findViewById<View>(com.autoflow.android.R.id.btn_retake).setOnClickListener {
            lifecycleScope.launch {
                tryDeleteCurrentImage()
                checkCameraPermissionAndOpen()
            }
        }
        // Continue: proceed to next step via ViewModel
        root.findViewById<View>(com.autoflow.android.R.id.btn_continue).setOnClickListener {
            viewModel.goToNextStep()
        }
    }

    private fun setupRecycler() {
        imagesAdapter =
                ImagesAdapter(
                        onSetMainImage = { imageUrl -> viewModel.setMainImage(imageUrl) },
                        onRemoveImage = { imageUrl -> viewModel.removeImage(imageUrl) }
                )
        imagesRecyclerView.apply {
            layoutManager = GridLayoutManager(context, 2)
            adapter = imagesAdapter
        }
    }

    private fun setupRecyclerView() {
        /* replaced by setupRecycler() */
    }

    private fun setupClickListeners() {
        // Button click listeners are set in createLayout()
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.formState.collect { formState ->
                imagesAdapter.updateImages(formState.images)
                updateInstructions(formState.images)
            }
        }

        lifecycleScope.launch {
            viewModel.uiState.collect { uiState ->
                if (uiState.isUploadingImage) {
                    showUploadProgress("Uploading image...")
                } else {
                    hideUploadProgress()
                }
                uiState.error?.let {
                    Toast.makeText(requireContext(), it, Toast.LENGTH_LONG).show()
                    viewModel.clearError()
                }
            }
        }
    }

    private fun updateInstructions(images: List<PartImage>) {
        instructionText.text =
                when {
                    images.isEmpty() ->
                            "Add photos of your part. The first image will be the main image."
                    images.size < MAX_IMAGES ->
                            "Add more photos (${images.size}/$MAX_IMAGES). Tap an image to set as main."
                    else ->
                            "Maximum images reached (${images.size}/$MAX_IMAGES). Tap an image to set as main."
                }

        // Enable/disable add buttons based on image count
        val canAddMore = images.size < MAX_IMAGES
        cameraButton.isEnabled = canAddMore
        galleryButton.isEnabled = canAddMore
    }

    private fun checkCameraPermissionAndOpen() {
        when {
            ContextCompat.checkSelfPermission(requireContext(), Manifest.permission.CAMERA) ==
                    PackageManager.PERMISSION_GRANTED -> {
                openCamera()
            }
            else -> {
                cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
            }
        }
    }

    private fun openCamera() {
        try {
            val photoFile = createImageFile()
            currentPhotoUri =
                    FileProvider.getUriForFile(
                            requireContext(),
                            "${requireContext().packageName}.fileprovider",
                            photoFile
                    )
            cameraLauncher.launch(currentPhotoUri)
        } catch (e: Exception) {
            Toast.makeText(context, "Error opening camera: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun openGallery() {
        galleryLauncher.launch("image/*")
    }

    private fun createImageFile(): File {
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val imageFileName = "PART_${timeStamp}_"
        val storageDir = requireContext().getExternalFilesDir("images")
        return File.createTempFile(imageFileName, ".jpg", storageDir)
    }

    private fun startImageCrop(sourceUri: Uri) {
        try {
            val destinationFile = createImageFile()
            val destinationUri = Uri.fromFile(destinationFile)

            val cropIntent =
                    UCrop.of(sourceUri, destinationUri)
                            .withAspectRatio(1f, 1f) // Square crop
                            .withMaxResultSize(800, 800) // Max 800x800 pixels
                            .getIntent(requireContext())

            cropLauncher.launch(cropIntent)
        } catch (e: Exception) {
            Toast.makeText(context, "Error starting crop: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun compressToUnder500Kb(src: File): File {
        // Skip if already under 500KB
        val maxBytes = 500 * 1024 // 500 KB
        if (src.length() <= maxBytes) return src

        // Compress JPEG to be under 500KB using a quality loop
        return try {
            val bmp = BitmapFactory.decodeFile(src.absolutePath)
            var quality = 90
            val baos = ByteArrayOutputStream()
            bmp.compress(Bitmap.CompressFormat.JPEG, quality, baos)
            var bytes = baos.toByteArray()

            while (bytes.size > maxBytes && quality > 30) {
                quality -= 10
                baos.reset()
                bmp.compress(Bitmap.CompressFormat.JPEG, quality, baos)
                bytes = baos.toByteArray()
            }
            baos.close()

            val outFile = File(src.parentFile, src.nameWithoutExtension + "_c.jpg")
            FileOutputStream(outFile).use { it.write(bytes) }
            outFile
        } catch (e: Exception) {
            // Fallback to source on any error
            src
        }
    }

    private fun uploadImage(imageUri: Uri) {
        try {
            val imageFile = File(imageUri.path!!)
            if (imageFile.exists()) {
                val compressed = compressToUnder500Kb(imageFile)
                showUploadProgress("Uploading image...")
                viewModel.addImage(compressed)
            } else {
                Toast.makeText(context, "Image file not found", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Toast.makeText(context, "Error uploading image: ${e.message}", Toast.LENGTH_SHORT)
                    .show()
        }
    }

    private fun showUploadProgress(message: String) {
        uploadProgressBar.visibility = View.VISIBLE
        uploadStatusText.text = message
        uploadStatusText.visibility = View.VISIBLE
        uploadProgressBar.alpha = 0f
        uploadProgressBar.animate().alpha(1f).setDuration(200).start()
    }

    private fun hideUploadProgress() {
        uploadProgressBar
                .animate()
                .alpha(0f)
                .setDuration(150)
                .withEndAction {
                    uploadProgressBar.visibility = View.GONE
                    uploadProgressBar.alpha = 1f
                }
                .start()
        uploadStatusText.visibility = View.GONE
    }

    private suspend fun tryDeleteCurrentImage() {
        // Identify main image (uploaded URL preferred)
        val current = viewModel.formState.value.images.firstOrNull { it.isMain }
        val imageUrl = current?.url
        if (imageUrl.isNullOrBlank()) return

        // Best-effort server delete: storage + DB
        try {
            showUploadProgress("Removing previous image...")
            val baseUrl = com.autoflow.android.BuildConfig.BASE_URL.trimEnd('/')
            val requestBody =
                    "{\"imageUrl\":\"$imageUrl\"}".toRequestBody("application/json".toMediaType())
            val req = Request.Builder().url("$baseUrl/api/images/delete").post(requestBody).build()
            val resp = OkHttpClient().newCall(req).execute()
            resp.close()
        } catch (_: Exception) {}

        // Update local state to remove it immediately
        viewModel.removeImage(imageUrl)
        hideUploadProgress()
    }
}

/** RecyclerView adapter for displaying part images (Material 3) */
class ImagesAdapter(
        private val onSetMainImage: (String) -> Unit,
        private val onRemoveImage: (String) -> Unit
) : RecyclerView.Adapter<ImagesAdapter.ImageViewHolder>() {

    private var images = listOf<PartImage>()

    fun updateImages(newImages: List<PartImage>) {
        images = newImages
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ImageViewHolder {
        val view =
                LayoutInflater.from(parent.context)
                        .inflate(com.autoflow.android.R.layout.item_part_image, parent, false)
        return ImageViewHolder(view)
    }

    override fun onBindViewHolder(holder: ImageViewHolder, position: Int) {
        holder.bind(images[position], onSetMainImage, onRemoveImage)
    }

    override fun getItemCount(): Int = images.size

    class ImageViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val imageView: ImageView =
                itemView.findViewById(com.autoflow.android.R.id.image_thumb)
        private val chipMain: com.google.android.material.chip.Chip =
                itemView.findViewById(com.autoflow.android.R.id.chip_main)
        private val btnRemove: ImageButton =
                itemView.findViewById(com.autoflow.android.R.id.btn_remove)
        private val progressThumb:
                com.google.android.material.progressindicator.LinearProgressIndicator =
                itemView.findViewById(com.autoflow.android.R.id.progress_thumb)

        fun bind(
                image: PartImage,
                onSetMainImage: (String) -> Unit,
                onRemoveImage: (String) -> Unit
        ) {
            val ctx = imageView.context
            // Load image
            if (image.url.startsWith("http", true)) {
                Glide.with(ctx)
                        .load(image.url)
                        .placeholder(android.R.drawable.ic_menu_gallery)
                        .centerCrop()
                        .into(imageView)
            } else {
                Glide.with(ctx)
                        .load(java.io.File(image.url))
                        .placeholder(android.R.drawable.ic_menu_gallery)
                        .centerCrop()
                        .into(imageView)
            }

            // Main chip state
            chipMain.visibility = if (image.isMain) View.VISIBLE else View.INVISIBLE
            chipMain.setOnClickListener { if (!image.isMain) onSetMainImage(image.url) }

            // Remove
            btnRemove.setOnClickListener { onRemoveImage(image.url) }

            // Progress
            if (image.isUploading) {
                progressThumb.visibility = View.VISIBLE
                imageView.alpha = 0.6f
            } else {
                progressThumb.visibility = View.GONE
                imageView.alpha = 1f
            }
        }
    }
}

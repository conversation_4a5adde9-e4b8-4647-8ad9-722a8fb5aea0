package com.autoflow.android.data.repositories

import com.autoflow.android.data.api.AddPartApiService
import com.autoflow.android.data.api.ApiResult
import com.autoflow.android.data.api.CategoriesResponse
import com.autoflow.android.data.api.CategoryAttributesResponse
import com.autoflow.android.data.api.CheckCompatibilityRequest
import com.autoflow.android.data.api.CheckCompatibilityResponse
import com.autoflow.android.data.api.ConditionData
import com.autoflow.android.data.api.CreatePartRequest
import com.autoflow.android.data.api.CreatePartResponse
import com.autoflow.android.data.api.GenerateDescriptionRequest
import com.autoflow.android.data.api.GenerateDescriptionResponse
import com.autoflow.android.data.api.GenerateTitleRequest
import com.autoflow.android.data.api.GenerateTitleResponse
import com.autoflow.android.data.api.ImageUploadResponse
import com.autoflow.android.data.api.SaveLocationRequest
import com.autoflow.android.data.api.SaveLocationResponse
import com.autoflow.android.data.api.VehicleLink
import com.autoflow.android.data.api.safeApiCall
import com.autoflow.android.domain.models.*
import java.io.File

/**
 * Repository for Add Part flow - orchestrates API calls and data transformations Mirrors web app's
 * data layer logic
 */
class AddPartRepository(private val apiService: AddPartApiService) {

    // ========== CATEGORIES AND ATTRIBUTES ==========

    suspend fun getCategories(): ApiResult<com.autoflow.android.data.api.CategoriesResponse> {
        return safeApiCall { apiService.getCategories() }
    }

    suspend fun getCategoryAttributes(categoryId: Int): ApiResult<CategoryAttributesResponse> {
        return safeApiCall { apiService.getCategoryAttributes(categoryId) }
    }

    // ========== VEHICLE SELECTION CASCADE ==========

    suspend fun getBrands(): ApiResult<List<CarBrand>> {
        return safeApiCall { apiService.getBrands() }
    }

    suspend fun getModels(brandId: Int): ApiResult<List<CarModel>> {
        return safeApiCall { apiService.getModels(brandId) }
    }

    suspend fun getGenerations(modelId: Int): ApiResult<List<CarGeneration>> {
        return safeApiCall { apiService.getGenerations(modelId) }
    }

    suspend fun getVariations(generationId: Int): ApiResult<List<CarVariation>> {
        return safeApiCall { apiService.getVariations(generationId) }
    }

    suspend fun getTrims(variationId: Int): ApiResult<List<CarTrim>> {
        return safeApiCall { apiService.getTrims(variationId) }
    }

    // ========== PART NUMBER COMPATIBILITY ==========

    private data class CompatCacheEntry(
            val timestamp: Long,
            val response: CheckCompatibilityResponse
    )
    private val compatCache = mutableMapOf<String, CompatCacheEntry>()
    private val COMPAT_TTL_MS = 30_000L

    suspend fun checkCompatibility(
            request: CheckCompatibilityRequest
    ): ApiResult<CheckCompatibilityResponse> {
        val key = "${'$'}{request.partNumber}:${'$'}{request.categoryId}"
        val now = System.currentTimeMillis()
        val cached = compatCache[key]
        if (cached != null && now - cached.timestamp < COMPAT_TTL_MS) {
            return ApiResult.Success(cached.response)
        }
        val result = safeApiCall { apiService.checkCompatibility(request) }
        if (result is ApiResult.Success) {
            compatCache[key] = CompatCacheEntry(now, result.data)
        }
        return result
    }

    // ========== IMAGE UPLOAD ==========

    suspend fun uploadImage(imageFile: File): ApiResult<ImageUploadResponse> {
        return try {
            // API expects a File; no multipart preparation needed at repository level
            safeApiCall { apiService.uploadImage(imageFile) }
        } catch (e: Exception) {
            ApiResult.Error(e, "Failed to prepare image for upload")
        }
    }

    // ========== TITLE AND DESCRIPTION GENERATION ==========

    suspend fun generateTitle(request: GenerateTitleRequest): ApiResult<GenerateTitleResponse> {
        return safeApiCall { apiService.generateTitle(request) }
    }

    suspend fun generateDescription(
            request: GenerateDescriptionRequest
    ): ApiResult<GenerateDescriptionResponse> {
        return safeApiCall { apiService.generateDescription(request) }
    }

    // ========== PART CREATION ==========

    suspend fun createPart(formValues: PartFormValues): ApiResult<CreatePartResponse> {
        return try {
            // Build the create part request from form values
            val request = buildCreatePartRequest(formValues)
            safeApiCall { apiService.createPart(request) }
        } catch (e: Exception) {
            ApiResult.Error(e, "Failed to build part creation request")
        }
    }

    private fun buildCreatePartRequest(formValues: PartFormValues): CreatePartRequest {
        // Build condition data based on condition type
        val (newCondition, usedCondition) =
                when (formValues.condition) {
                    PartCondition.NEW -> {
                        val newCond =
                                ConditionData(
                                        stock = formValues.newStock ?: formValues.stock ?: 0,
                                        price = formValues.newPrice ?: formValues.price ?: 0.0,
                                        discountedPrice = formValues.newDiscountPrice
                                                        ?: formValues.discountPrice
                                )
                        Pair(newCond, null)
                    }
                    PartCondition.USED -> {
                        val usedCond =
                                ConditionData(
                                        stock = formValues.usedStock ?: formValues.stock ?: 0,
                                        price = formValues.usedPrice ?: formValues.price ?: 0.0,
                                        discountedPrice = formValues.usedDiscountPrice
                                                        ?: formValues.discountPrice
                                )
                        Pair(null, usedCond)
                    }
                    PartCondition.BOTH -> {
                        val newCond =
                                ConditionData(
                                        stock = formValues.newStock ?: 0,
                                        price = formValues.newPrice ?: 0.0,
                                        discountedPrice = formValues.newDiscountPrice
                                )
                        val usedCond =
                                ConditionData(
                                        stock = formValues.usedStock ?: 0,
                                        price = formValues.usedPrice ?: 0.0,
                                        discountedPrice = formValues.usedDiscountPrice
                                )
                        Pair(newCond, usedCond)
                    }
                }

        // Build vehicle link based on flow type
        val vehicleLink =
                if (formValues.requirePartNumber) {
                    // PN-required flow: use compatibility data
                    VehicleLink(
                            mode = "compatibility",
                            list = formValues.compatibilityData?.vehicleCompatibility
                    )
                } else {
                    // Non-PN flow: use selected trim
                    VehicleLink(mode = "manual", trimId = formValues.trimId.toIntOrNull())
                }

        // Combine AI engines with additional engine codes
        val allEngines = mutableListOf<String>()
        formValues.compatibilityData?.engineCompatibility?.forEach { engine ->
            allEngines.add(engine.engineCode)
        }
        allEngines.addAll(formValues.additionalEngineCodes)

        return CreatePartRequest(
                categoryId = formValues.categoryId.toInt(),
                title = formValues.title,
                description = formValues.description,
                partnumberGroup = formValues.compatibilityData?.partnumberGroup,
                createdBy = formValues.userId.toInt(),
                images = formValues.images.map { it.url },
                condition = formValues.condition.name.lowercase(),
                newCondition = newCondition,
                usedCondition = usedCondition,
                attributes = formValues.attributes,
                vehicles = vehicleLink,
                engines = allEngines.distinct()
        )
    }

    // ========== STORAGE LOCATIONS ==========

    suspend fun getStorageAreas(): ApiResult<List<StorageArea>> {
        return safeApiCall { apiService.getStorageAreas() }
    }

    suspend fun getStorageUnits(areaId: Int? = null): ApiResult<List<StorageUnit>> {
        return safeApiCall { apiService.getStorageUnits(areaId) }
    }

    suspend fun savePartLocation(
            partId: Int,
            locationData: StorageLocationFormData
    ): ApiResult<SaveLocationResponse> {
        val request =
                SaveLocationRequest(
                        unitId = locationData.unitId ?: 0,
                        quantity = locationData.quantity,
                        locationSubtype = locationData.locationSubtype,
                        details = locationData.details,
                        notes = locationData.notes
                )

        return safeApiCall { apiService.savePartLocation(partId, request) }
    }

    // ========== COMPLETE PART CREATION FLOW ==========

    /**
     * Complete part creation flow with title/description generation Mirrors web app's complete
     * submission process
     */
    suspend fun createPartComplete(formValues: PartFormValues): ApiResult<CreatePartResponse> {
        return try {
            // Step 1: Generate title
            val titleRequest =
                    GenerateTitleRequest(
                            categoryId = formValues.categoryId.toInt(),
                            partNumber = formValues.partNumber.takeIf { it.isNotBlank() },
                            vehicleInfo = buildVehicleInfoString(formValues),
                            compatibilityData = formValues.compatibilityData
                    )

            val titleResult = generateTitle(titleRequest)
            val title =
                    when (titleResult) {
                        is ApiResult.Success -> titleResult.data.title
                        is ApiResult.Error -> {
                            // Fallback title generation
                            buildFallbackTitle(formValues)
                        }
                        is ApiResult.Loading -> buildFallbackTitle(formValues)
                    }

            // Step 2: Generate description
            val descriptionRequest =
                    GenerateDescriptionRequest(
                            title = title,
                            categoryId = formValues.categoryId.toInt(),
                            condition = formValues.condition.name.lowercase(),
                            compatibilityData = formValues.compatibilityData
                    )

            val descriptionResult = generateDescription(descriptionRequest)
            val description =
                    when (descriptionResult) {
                        is ApiResult.Success -> descriptionResult.data.description
                        is ApiResult.Error -> {
                            // Fallback description
                            buildFallbackDescription(formValues, title)
                        }
                        is ApiResult.Loading -> buildFallbackDescription(formValues, title)
                    }

            // Step 3: Create part with generated title and description
            val updatedFormValues = formValues.copy(title = title, description = description)

            createPart(updatedFormValues)
        } catch (e: Exception) {
            ApiResult.Error(e, "Failed to complete part creation flow")
        }
    }

    private fun buildVehicleInfoString(formValues: PartFormValues): String {
        return if (formValues.requirePartNumber) {
            // Use compatibility data
            formValues.compatibilityData?.vehicleCompatibility?.joinToString(", ") { vehicle ->
                "${vehicle.brand} ${vehicle.model} ${vehicle.generation}"
            }
                    ?: ""
        } else {
            // Use selected vehicle
            "${formValues.modelName} ${formValues.generationName} ${formValues.variationName} ${formValues.trimName}".trim()
        }
    }

    private fun buildFallbackTitle(formValues: PartFormValues): String {
        val category = formValues.selectedCategory
        val partNumber = formValues.partNumber.takeIf { it.isNotBlank() }
        val vehicleInfo = buildVehicleInfoString(formValues)

        return buildString {
                    if (vehicleInfo.isNotBlank()) {
                        append(vehicleInfo)
                        append(" ")
                    }
                    append(category)
                    if (partNumber != null) {
                        append(" ")
                        append(partNumber)
                    }
                }
                .trim()
    }

    private fun buildFallbackDescription(formValues: PartFormValues, title: String): String {
        val condition =
                when (formValues.condition) {
                    PartCondition.NEW -> "New"
                    PartCondition.USED -> "Used"
                    PartCondition.BOTH -> "New and Used"
                }

        return "$condition ${formValues.selectedCategory.lowercase()} for ${buildVehicleInfoString(formValues)}. $title"
    }
}

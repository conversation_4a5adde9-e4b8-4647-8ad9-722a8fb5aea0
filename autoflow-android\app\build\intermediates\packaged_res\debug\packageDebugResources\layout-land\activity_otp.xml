<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="@drawable/login_background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <!-- Back Button -->
        <ImageButton
            android:id="@+id/backButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginTop="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_arrow_back"
            android:contentDescription="Back"
            android:tint="@color/text_primary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Title -->
        <TextView
            android:id="@+id/titleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Verify Account"
            android:textColor="@color/text_primary"
            android:textSize="24sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/backButton" />

        <!-- Content Container for Landscape -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginTop="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/titleText"
            app:layout_constraintBottom_toBottomOf="parent">

            <!-- Left Side - Illustration -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="16dp">

                <ImageView
                    android:id="@+id/illustrationImage"
                    android:layout_width="120dp"
                    android:layout_height="100dp"
                    android:src="@drawable/otp_illustration_simple"
                    android:scaleType="centerInside" />

            </LinearLayout>

            <!-- Right Side - Form -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="16dp">

                <!-- Main Title -->
                <TextView
                    android:id="@+id/mainTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Enter your Verification Code"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif-medium"
                    android:gravity="center" />

                <!-- Subtitle -->
                <TextView
                    android:id="@+id/subtitleText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="We sent a verification code\nto your email address"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:gravity="center"
                    android:lineSpacingExtra="2dp" />

                <!-- OTP Input Container -->
                <LinearLayout
                    android:id="@+id/otpContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:orientation="horizontal"
                    android:gravity="center"
                    android:weightSum="6">

                    <EditText
                        android:id="@+id/otpDigit1"
                        style="@style/OTPDigitStyle"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="4dp" />

                    <EditText
                        android:id="@+id/otpDigit2"
                        style="@style/OTPDigitStyle"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:layout_marginStart="4dp"
                        android:layout_marginEnd="4dp" />

                    <EditText
                        android:id="@+id/otpDigit3"
                        style="@style/OTPDigitStyle"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:layout_marginStart="4dp"
                        android:layout_marginEnd="4dp" />

                    <EditText
                        android:id="@+id/otpDigit4"
                        style="@style/OTPDigitStyle"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:layout_marginStart="4dp"
                        android:layout_marginEnd="4dp" />

                    <EditText
                        android:id="@+id/otpDigit5"
                        style="@style/OTPDigitStyle"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:layout_marginStart="4dp"
                        android:layout_marginEnd="4dp" />

                    <EditText
                        android:id="@+id/otpDigit6"
                        style="@style/OTPDigitStyle"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:layout_marginStart="4dp" />

                </LinearLayout>

                <!-- Verify Button -->
                <Button
                    android:id="@+id/verifyButton"
                    style="@style/LoginButton"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginTop="24dp"
                    android:text="Verify OTP"
                    android:enabled="false" />

                <!-- Resend Text -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="horizontal"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/resendText"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Didn't receive code? "
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/resendButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Resend again"
                        android:textColor="@color/purple_500"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:padding="8dp" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <!-- Loading Progress -->
        <ProgressBar
            android:id="@+id/loadingProgress"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:visibility="gone"
            android:indeterminateTint="@android:color/white"
            app:layout_constraintBottom_toBottomOf="@id/verifyButton"
            app:layout_constraintEnd_toEndOf="@id/verifyButton"
            app:layout_constraintStart_toStartOf="@id/verifyButton"
            app:layout_constraintTop_toTopOf="@id/verifyButton" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>

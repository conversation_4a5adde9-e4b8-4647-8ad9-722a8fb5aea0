com.autoflow.android.app-jetified-activity-1.7.2-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\00cf38f4190a53e97ba9209600294ab2\transformed\jetified-activity-1.7.2\res
com.autoflow.android.app-core-runtime-2.2.0-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\02815fae27931ae44da7fab74be7eac3\transformed\core-runtime-2.2.0\res
com.autoflow.android.app-jetified-ucrop-2.2.8-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\0e54198a82394605fc9efe0eed5d1d5f\transformed\jetified-ucrop-2.2.8\res
com.autoflow.android.app-jetified-emoji2-views-helper-1.2.0-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\1235f37b232d25913db15dc3cf2502d1\transformed\jetified-emoji2-views-helper-1.2.0\res
com.autoflow.android.app-jetified-lifecycle-viewmodel-savedstate-2.6.2-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\125478a017f02376aaf907258ea43015\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\res
com.autoflow.android.app-jetified-camera-core-1.2.3-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\14db308c6417231c0c3d58fa46d38064\transformed\jetified-camera-core-1.2.3\res
com.autoflow.android.app-jetified-annotation-experimental-1.3.0-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\1905fa04720f9aded41c2c7266b432f5\transformed\jetified-annotation-experimental-1.3.0\res
com.autoflow.android.app-core-1.10.1-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\1c5f58891eb309bb4a04ac21b97fad24\transformed\core-1.10.1\res
com.autoflow.android.app-appcompat-1.6.1-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\303aa64de72ab8a914eac82254eebb45\transformed\appcompat-1.6.1\res
com.autoflow.android.app-lifecycle-viewmodel-2.6.2-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\3ae3f07561c5228f22a7d470c7685769\transformed\lifecycle-viewmodel-2.6.2\res
com.autoflow.android.app-jetified-camera-view-1.2.3-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\3f1c71fda7d38cac5836f376d3cd4841\transformed\jetified-camera-view-1.2.3\res
com.autoflow.android.app-coordinatorlayout-1.1.0-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\402ab57b0e7ba6f5089b05fcb3c0ea47\transformed\coordinatorlayout-1.1.0\res
com.autoflow.android.app-jetified-lifecycle-runtime-ktx-2.6.2-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\4b9c5a6e42df1b61f13f380ee190613b\transformed\jetified-lifecycle-runtime-ktx-2.6.2\res
com.autoflow.android.app-jetified-activity-ktx-1.7.2-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\4d922eac76ec096fc81edd177259e3f1\transformed\jetified-activity-ktx-1.7.2\res
com.autoflow.android.app-lifecycle-livedata-2.6.2-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\574a51baaed8611751e256cb469fde66\transformed\lifecycle-livedata-2.6.2\res
com.autoflow.android.app-jetified-security-crypto-1.1.0-alpha06-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\655c0f29bd4e7daa35521825d54b2b0b\transformed\jetified-security-crypto-1.1.0-alpha06\res
com.autoflow.android.app-fragment-1.6.2-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\6afcc8c092fdc0e69dcc0a494c737cf3\transformed\fragment-1.6.2\res
com.autoflow.android.app-cardview-1.0.0-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\6cbba339ad84aadcb7c88ebb4e3df614\transformed\cardview-1.0.0\res
com.autoflow.android.app-jetified-glide-4.16.0-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\6e15637815ca42e4b319252e4b709f57\transformed\jetified-glide-4.16.0\res
com.autoflow.android.app-lifecycle-runtime-2.6.2-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\70aee03866c8d8d27e2862c7da29844e\transformed\lifecycle-runtime-2.6.2\res
com.autoflow.android.app-jetified-savedstate-1.2.1-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\7e679ca1755443ba159bad16970bae55\transformed\jetified-savedstate-1.2.1\res
com.autoflow.android.app-jetified-camera-camera2-1.2.3-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\894a783dca4aa815eb545af54b4b48b6\transformed\jetified-camera-camera2-1.2.3\res
com.autoflow.android.app-jetified-camera-lifecycle-1.2.3-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\8e3d9b328cdd75da8fd382fc6a82584c\transformed\jetified-camera-lifecycle-1.2.3\res
com.autoflow.android.app-drawerlayout-1.1.1-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\8e5db4685bf6ccf83c8280681ab238ef\transformed\drawerlayout-1.1.1\res
com.autoflow.android.app-jetified-lifecycle-viewmodel-ktx-2.6.2-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\a3ebb97f7886adc68cb1b96e69a304aa\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\res
com.autoflow.android.app-jetified-profileinstaller-1.3.0-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\ad0d81babbb712d29957e09e6c8c6a1b\transformed\jetified-profileinstaller-1.3.0\res
com.autoflow.android.app-jetified-lifecycle-livedata-core-ktx-2.6.2-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\ad4577fe8a010038de8762b6815943f3\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\res
com.autoflow.android.app-transition-1.4.1-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\adb6c888e1928c051191f75518118095\transformed\transition-1.4.1\res
com.autoflow.android.app-constraintlayout-2.1.4-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\afd4ca0b0d75028876529c00154594ee\transformed\constraintlayout-2.1.4\res
com.autoflow.android.app-jetified-fragment-ktx-1.6.2-29 C:\Users\<USER>\.gradle\caches\8.13\transforms\ba1c76a861f67417531d3b17cb9afe8e\transformed\jetified-fragment-ktx-1.6.2\res
com.autoflow.android.app-jetified-startup-runtime-1.1.1-30 C:\Users\<USER>\.gradle\caches\8.13\transforms\bd40cf52013e79f216188201b79d1d5b\transformed\jetified-startup-runtime-1.1.1\res
com.autoflow.android.app-lifecycle-livedata-core-2.6.2-31 C:\Users\<USER>\.gradle\caches\8.13\transforms\c6ecc5ab7198e78d415df13f119ae1fd\transformed\lifecycle-livedata-core-2.6.2\res
com.autoflow.android.app-jetified-lifecycle-livedata-ktx-2.6.2-32 C:\Users\<USER>\.gradle\caches\8.13\transforms\c89db88e6ae2827a86b641e6bb26d8d1\transformed\jetified-lifecycle-livedata-ktx-2.6.2\res
com.autoflow.android.app-jetified-customview-poolingcontainer-1.0.0-33 C:\Users\<USER>\.gradle\caches\8.13\transforms\d8f8aa0f8ce018e96e0c47b96e3baa33\transformed\jetified-customview-poolingcontainer-1.0.0\res
com.autoflow.android.app-jetified-savedstate-ktx-1.2.1-34 C:\Users\<USER>\.gradle\caches\8.13\transforms\db03cf5cf2ca6b085efb9013b689807c\transformed\jetified-savedstate-ktx-1.2.1\res
com.autoflow.android.app-jetified-core-ktx-1.10.1-35 C:\Users\<USER>\.gradle\caches\8.13\transforms\e511441ebcdbb52494bd61e876808728\transformed\jetified-core-ktx-1.10.1\res
com.autoflow.android.app-jetified-lifecycle-process-2.6.2-36 C:\Users\<USER>\.gradle\caches\8.13\transforms\ece72575c0e3a0c9796d85af5708f2d2\transformed\jetified-lifecycle-process-2.6.2\res
com.autoflow.android.app-biometric-1.1.0-37 C:\Users\<USER>\.gradle\caches\8.13\transforms\ed7078e8e71893b82f32857a328dfc89\transformed\biometric-1.1.0\res
com.autoflow.android.app-jetified-appcompat-resources-1.6.1-38 C:\Users\<USER>\.gradle\caches\8.13\transforms\f59aa3ce8c9c3009cb30362368e734b6\transformed\jetified-appcompat-resources-1.6.1\res
com.autoflow.android.app-jetified-viewpager2-1.1.0-beta02-39 C:\Users\<USER>\.gradle\caches\8.13\transforms\f6b682ebfb3461f254619670e2b339ea\transformed\jetified-viewpager2-1.1.0-beta02\res
com.autoflow.android.app-recyclerview-1.3.2-40 C:\Users\<USER>\.gradle\caches\8.13\transforms\f7f5a8b5debc415a9ff99230e8cb1d6b\transformed\recyclerview-1.3.2\res
com.autoflow.android.app-material-1.9.0-41 C:\Users\<USER>\.gradle\caches\8.13\transforms\fd2d40d61e01e6ef2b0f2f90a3e8aca5\transformed\material-1.9.0\res
com.autoflow.android.app-jetified-emoji2-1.2.0-42 C:\Users\<USER>\.gradle\caches\8.13\transforms\fe349995b136e85f6a005f5d89fa32f9\transformed\jetified-emoji2-1.2.0\res
com.autoflow.android.app-pngs-43 C:\Users\<USER>\Node\autoflow\autoflow-android\app\build\generated\res\pngs\debug
com.autoflow.android.app-resValues-44 C:\Users\<USER>\Node\autoflow\autoflow-android\app\build\generated\res\resValues\debug
com.autoflow.android.app-packageDebugResources-45 C:\Users\<USER>\Node\autoflow\autoflow-android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.autoflow.android.app-packageDebugResources-46 C:\Users\<USER>\Node\autoflow\autoflow-android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.autoflow.android.app-debug-47 C:\Users\<USER>\Node\autoflow\autoflow-android\app\build\intermediates\merged_res\debug\mergeDebugResources
com.autoflow.android.app-debug-48 C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\debug\res
com.autoflow.android.app-main-49 C:\Users\<USER>\Node\autoflow\autoflow-android\app\src\main\res

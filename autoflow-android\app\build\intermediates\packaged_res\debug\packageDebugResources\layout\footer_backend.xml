<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:background="@android:color/white"
    android:elevation="8dp"
    android:paddingVertical="12dp"
    android:paddingHorizontal="16dp">

    <!-- Home Button -->
    <LinearLayout
        android:id="@+id/footerHomeButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="8dp"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_home"
            android:tint="@color/brand_teal"
            android:layout_marginBottom="4dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Home"
            android:textColor="@color/brand_teal"
            android:textSize="12sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- Search Button -->
    <LinearLayout
        android:id="@+id/footerSearchButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="8dp"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_search"
            android:tint="@color/text_secondary"
            android:layout_marginBottom="4dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Search"
            android:textColor="@color/text_secondary"
            android:textSize="12sp" />

    </LinearLayout>

    <!-- Profile Button -->
    <LinearLayout
        android:id="@+id/footerProfileButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="8dp"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_person"
            android:tint="@color/text_secondary"
            android:layout_marginBottom="4dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Profile"
            android:textColor="@color/text_secondary"
            android:textSize="12sp" />

    </LinearLayout>

</LinearLayout>
